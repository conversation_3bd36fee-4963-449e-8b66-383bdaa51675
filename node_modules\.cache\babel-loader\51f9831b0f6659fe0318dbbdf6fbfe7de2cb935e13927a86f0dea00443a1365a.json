{"ast": null, "code": "import * as React from 'react';\nimport { useContext, useRef, cloneElement, Children, isValidElement } from 'react';\nimport { useForceUpdate } from '../../utils/use-force-update.mjs';\nimport { useIsMounted } from '../../utils/use-is-mounted.mjs';\nimport { PresenceChild } from './PresenceChild.mjs';\nimport { LayoutGroupContext } from '../../context/LayoutGroupContext.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\nimport { useUnmountEffect } from '../../utils/use-unmount-effect.mjs';\nimport { invariant } from '../../utils/errors.mjs';\nconst getChildKey = child => child.key || \"\";\nfunction updateChildLookup(children, allChildren) {\n  children.forEach(child => {\n    const key = getChildKey(child);\n    allChildren.set(key, child);\n  });\n}\nfunction onlyElements(children) {\n  const filtered = [];\n  // We use forEach here instead of map as map mutates the component key by preprending `.$`\n  Children.forEach(children, child => {\n    if (isValidElement(child)) filtered.push(child);\n  });\n  return filtered;\n}\n/**\n * `AnimatePresence` enables the animation of components that have been removed from the tree.\n *\n * When adding/removing more than a single child, every child **must** be given a unique `key` prop.\n *\n * Any `motion` components that have an `exit` property defined will animate out when removed from\n * the tree.\n *\n * ```jsx\n * import { motion, AnimatePresence } from 'framer-motion'\n *\n * export const Items = ({ items }) => (\n *   <AnimatePresence>\n *     {items.map(item => (\n *       <motion.div\n *         key={item.id}\n *         initial={{ opacity: 0 }}\n *         animate={{ opacity: 1 }}\n *         exit={{ opacity: 0 }}\n *       />\n *     ))}\n *   </AnimatePresence>\n * )\n * ```\n *\n * You can sequence exit animations throughout a tree using variants.\n *\n * If a child contains multiple `motion` components with `exit` props, it will only unmount the child\n * once all `motion` components have finished animating out. Likewise, any components using\n * `usePresence` all need to call `safeToRemove`.\n *\n * @public\n */\nconst AnimatePresence = ({\n  children,\n  custom,\n  initial = true,\n  onExitComplete,\n  exitBeforeEnter,\n  presenceAffectsLayout = true,\n  mode = \"sync\"\n}) => {\n  invariant(!exitBeforeEnter, \"Replace exitBeforeEnter with mode='wait'\");\n  // We want to force a re-render once all exiting animations have finished. We\n  // either use a local forceRender function, or one from a parent context if it exists.\n  const forceRender = useContext(LayoutGroupContext).forceRender || useForceUpdate()[0];\n  const isMounted = useIsMounted();\n  // Filter out any children that aren't ReactElements. We can only track ReactElements with a props.key\n  const filteredChildren = onlyElements(children);\n  let childrenToRender = filteredChildren;\n  const exitingChildren = useRef(new Map()).current;\n  // Keep a living record of the children we're actually rendering so we\n  // can diff to figure out which are entering and exiting\n  const presentChildren = useRef(childrenToRender);\n  // A lookup table to quickly reference components by key\n  const allChildren = useRef(new Map()).current;\n  // If this is the initial component render, just deal with logic surrounding whether\n  // we play onMount animations or not.\n  const isInitialRender = useRef(true);\n  useIsomorphicLayoutEffect(() => {\n    isInitialRender.current = false;\n    updateChildLookup(filteredChildren, allChildren);\n    presentChildren.current = childrenToRender;\n  });\n  useUnmountEffect(() => {\n    isInitialRender.current = true;\n    allChildren.clear();\n    exitingChildren.clear();\n  });\n  if (isInitialRender.current) {\n    return React.createElement(React.Fragment, null, childrenToRender.map(child => React.createElement(PresenceChild, {\n      key: getChildKey(child),\n      isPresent: true,\n      initial: initial ? undefined : false,\n      presenceAffectsLayout: presenceAffectsLayout,\n      mode: mode\n    }, child)));\n  }\n  // If this is a subsequent render, deal with entering and exiting children\n  childrenToRender = [...childrenToRender];\n  // Diff the keys of the currently-present and target children to update our\n  // exiting list.\n  const presentKeys = presentChildren.current.map(getChildKey);\n  const targetKeys = filteredChildren.map(getChildKey);\n  // Diff the present children with our target children and mark those that are exiting\n  const numPresent = presentKeys.length;\n  for (let i = 0; i < numPresent; i++) {\n    const key = presentKeys[i];\n    if (targetKeys.indexOf(key) === -1 && !exitingChildren.has(key)) {\n      exitingChildren.set(key, undefined);\n    }\n  }\n  // If we currently have exiting children, and we're deferring rendering incoming children\n  // until after all current children have exiting, empty the childrenToRender array\n  if (mode === \"wait\" && exitingChildren.size) {\n    childrenToRender = [];\n  }\n  // Loop through all currently exiting components and clone them to overwrite `animate`\n  // with any `exit` prop they might have defined.\n  exitingChildren.forEach((component, key) => {\n    // If this component is actually entering again, early return\n    if (targetKeys.indexOf(key) !== -1) return;\n    const child = allChildren.get(key);\n    if (!child) return;\n    const insertionIndex = presentKeys.indexOf(key);\n    let exitingComponent = component;\n    if (!exitingComponent) {\n      const onExit = () => {\n        // clean up the exiting children map\n        exitingChildren.delete(key);\n        // compute the keys of children that were rendered once but are no longer present\n        // this could happen in case of too many fast consequent renderings\n        // @link https://github.com/framer/motion/issues/2023\n        const leftOverKeys = Array.from(allChildren.keys()).filter(childKey => !targetKeys.includes(childKey));\n        // clean up the all children map\n        leftOverKeys.forEach(leftOverKey => allChildren.delete(leftOverKey));\n        // make sure to render only the children that are actually visible\n        presentChildren.current = filteredChildren.filter(presentChild => {\n          const presentChildKey = getChildKey(presentChild);\n          return (\n            // filter out the node exiting\n            presentChildKey === key ||\n            // filter out the leftover children\n            leftOverKeys.includes(presentChildKey)\n          );\n        });\n        // Defer re-rendering until all exiting children have indeed left\n        if (!exitingChildren.size) {\n          if (isMounted.current === false) return;\n          forceRender();\n          onExitComplete && onExitComplete();\n        }\n      };\n      exitingComponent = React.createElement(PresenceChild, {\n        key: getChildKey(child),\n        isPresent: false,\n        onExitComplete: onExit,\n        custom: custom,\n        presenceAffectsLayout: presenceAffectsLayout,\n        mode: mode\n      }, child);\n      exitingChildren.set(key, exitingComponent);\n    }\n    childrenToRender.splice(insertionIndex, 0, exitingComponent);\n  });\n  // Add `MotionContext` even to children that don't need it to ensure we're rendering\n  // the same tree between renders\n  childrenToRender = childrenToRender.map(child => {\n    const key = child.key;\n    return exitingChildren.has(key) ? child : React.createElement(PresenceChild, {\n      key: getChildKey(child),\n      isPresent: true,\n      presenceAffectsLayout: presenceAffectsLayout,\n      mode: mode\n    }, child);\n  });\n  if (process.env.NODE_ENV !== \"production\" && mode === \"wait\" && childrenToRender.length > 1) {\n    console.warn(`You're attempting to animate multiple children within AnimatePresence, but its mode is set to \"wait\". This will lead to odd visual behaviour.`);\n  }\n  return React.createElement(React.Fragment, null, exitingChildren.size ? childrenToRender : childrenToRender.map(child => cloneElement(child)));\n};\nexport { AnimatePresence };", "map": {"version": 3, "names": ["React", "useContext", "useRef", "cloneElement", "Children", "isValidElement", "useForceUpdate", "useIsMounted", "Presence<PERSON><PERSON><PERSON>", "LayoutGroupContext", "useIsomorphicLayoutEffect", "useUnmountEffect", "invariant", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "child", "key", "updateChildLookup", "children", "allChildren", "for<PERSON>ach", "set", "onlyElements", "filtered", "push", "AnimatePresence", "custom", "initial", "onExitComplete", "exitBeforeEnter", "presenceAffectsLayout", "mode", "forceRender", "isMounted", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children<PERSON><PERSON><PERSON><PERSON>", "exiting<PERSON><PERSON><PERSON><PERSON>", "Map", "current", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isInitialRender", "clear", "createElement", "Fragment", "map", "isPresent", "undefined", "present<PERSON><PERSON>s", "targetKeys", "numPresent", "length", "i", "indexOf", "has", "size", "component", "get", "insertionIndex", "exitingComponent", "onExit", "delete", "leftOverKeys", "Array", "from", "keys", "filter", "<PERSON><PERSON><PERSON>", "includes", "leftOverKey", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "splice", "process", "env", "NODE_ENV", "console", "warn"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs"], "sourcesContent": ["import * as React from 'react';\nimport { useContext, useRef, cloneElement, Children, isValidElement } from 'react';\nimport { useForceUpdate } from '../../utils/use-force-update.mjs';\nimport { useIsMounted } from '../../utils/use-is-mounted.mjs';\nimport { PresenceChild } from './PresenceChild.mjs';\nimport { LayoutGroupContext } from '../../context/LayoutGroupContext.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\nimport { useUnmountEffect } from '../../utils/use-unmount-effect.mjs';\nimport { invariant } from '../../utils/errors.mjs';\n\nconst getChildKey = (child) => child.key || \"\";\nfunction updateChildLookup(children, allChildren) {\n    children.forEach((child) => {\n        const key = getChildKey(child);\n        allChildren.set(key, child);\n    });\n}\nfunction onlyElements(children) {\n    const filtered = [];\n    // We use forEach here instead of map as map mutates the component key by preprending `.$`\n    Children.forEach(children, (child) => {\n        if (isValidElement(child))\n            filtered.push(child);\n    });\n    return filtered;\n}\n/**\n * `AnimatePresence` enables the animation of components that have been removed from the tree.\n *\n * When adding/removing more than a single child, every child **must** be given a unique `key` prop.\n *\n * Any `motion` components that have an `exit` property defined will animate out when removed from\n * the tree.\n *\n * ```jsx\n * import { motion, AnimatePresence } from 'framer-motion'\n *\n * export const Items = ({ items }) => (\n *   <AnimatePresence>\n *     {items.map(item => (\n *       <motion.div\n *         key={item.id}\n *         initial={{ opacity: 0 }}\n *         animate={{ opacity: 1 }}\n *         exit={{ opacity: 0 }}\n *       />\n *     ))}\n *   </AnimatePresence>\n * )\n * ```\n *\n * You can sequence exit animations throughout a tree using variants.\n *\n * If a child contains multiple `motion` components with `exit` props, it will only unmount the child\n * once all `motion` components have finished animating out. Likewise, any components using\n * `usePresence` all need to call `safeToRemove`.\n *\n * @public\n */\nconst AnimatePresence = ({ children, custom, initial = true, onExitComplete, exitBeforeEnter, presenceAffectsLayout = true, mode = \"sync\", }) => {\n    invariant(!exitBeforeEnter, \"Replace exitBeforeEnter with mode='wait'\");\n    // We want to force a re-render once all exiting animations have finished. We\n    // either use a local forceRender function, or one from a parent context if it exists.\n    const forceRender = useContext(LayoutGroupContext).forceRender || useForceUpdate()[0];\n    const isMounted = useIsMounted();\n    // Filter out any children that aren't ReactElements. We can only track ReactElements with a props.key\n    const filteredChildren = onlyElements(children);\n    let childrenToRender = filteredChildren;\n    const exitingChildren = useRef(new Map()).current;\n    // Keep a living record of the children we're actually rendering so we\n    // can diff to figure out which are entering and exiting\n    const presentChildren = useRef(childrenToRender);\n    // A lookup table to quickly reference components by key\n    const allChildren = useRef(new Map()).current;\n    // If this is the initial component render, just deal with logic surrounding whether\n    // we play onMount animations or not.\n    const isInitialRender = useRef(true);\n    useIsomorphicLayoutEffect(() => {\n        isInitialRender.current = false;\n        updateChildLookup(filteredChildren, allChildren);\n        presentChildren.current = childrenToRender;\n    });\n    useUnmountEffect(() => {\n        isInitialRender.current = true;\n        allChildren.clear();\n        exitingChildren.clear();\n    });\n    if (isInitialRender.current) {\n        return (React.createElement(React.Fragment, null, childrenToRender.map((child) => (React.createElement(PresenceChild, { key: getChildKey(child), isPresent: true, initial: initial ? undefined : false, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child)))));\n    }\n    // If this is a subsequent render, deal with entering and exiting children\n    childrenToRender = [...childrenToRender];\n    // Diff the keys of the currently-present and target children to update our\n    // exiting list.\n    const presentKeys = presentChildren.current.map(getChildKey);\n    const targetKeys = filteredChildren.map(getChildKey);\n    // Diff the present children with our target children and mark those that are exiting\n    const numPresent = presentKeys.length;\n    for (let i = 0; i < numPresent; i++) {\n        const key = presentKeys[i];\n        if (targetKeys.indexOf(key) === -1 && !exitingChildren.has(key)) {\n            exitingChildren.set(key, undefined);\n        }\n    }\n    // If we currently have exiting children, and we're deferring rendering incoming children\n    // until after all current children have exiting, empty the childrenToRender array\n    if (mode === \"wait\" && exitingChildren.size) {\n        childrenToRender = [];\n    }\n    // Loop through all currently exiting components and clone them to overwrite `animate`\n    // with any `exit` prop they might have defined.\n    exitingChildren.forEach((component, key) => {\n        // If this component is actually entering again, early return\n        if (targetKeys.indexOf(key) !== -1)\n            return;\n        const child = allChildren.get(key);\n        if (!child)\n            return;\n        const insertionIndex = presentKeys.indexOf(key);\n        let exitingComponent = component;\n        if (!exitingComponent) {\n            const onExit = () => {\n                // clean up the exiting children map\n                exitingChildren.delete(key);\n                // compute the keys of children that were rendered once but are no longer present\n                // this could happen in case of too many fast consequent renderings\n                // @link https://github.com/framer/motion/issues/2023\n                const leftOverKeys = Array.from(allChildren.keys()).filter((childKey) => !targetKeys.includes(childKey));\n                // clean up the all children map\n                leftOverKeys.forEach((leftOverKey) => allChildren.delete(leftOverKey));\n                // make sure to render only the children that are actually visible\n                presentChildren.current = filteredChildren.filter((presentChild) => {\n                    const presentChildKey = getChildKey(presentChild);\n                    return (\n                    // filter out the node exiting\n                    presentChildKey === key ||\n                        // filter out the leftover children\n                        leftOverKeys.includes(presentChildKey));\n                });\n                // Defer re-rendering until all exiting children have indeed left\n                if (!exitingChildren.size) {\n                    if (isMounted.current === false)\n                        return;\n                    forceRender();\n                    onExitComplete && onExitComplete();\n                }\n            };\n            exitingComponent = (React.createElement(PresenceChild, { key: getChildKey(child), isPresent: false, onExitComplete: onExit, custom: custom, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child));\n            exitingChildren.set(key, exitingComponent);\n        }\n        childrenToRender.splice(insertionIndex, 0, exitingComponent);\n    });\n    // Add `MotionContext` even to children that don't need it to ensure we're rendering\n    // the same tree between renders\n    childrenToRender = childrenToRender.map((child) => {\n        const key = child.key;\n        return exitingChildren.has(key) ? (child) : (React.createElement(PresenceChild, { key: getChildKey(child), isPresent: true, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child));\n    });\n    if (process.env.NODE_ENV !== \"production\" &&\n        mode === \"wait\" &&\n        childrenToRender.length > 1) {\n        console.warn(`You're attempting to animate multiple children within AnimatePresence, but its mode is set to \"wait\". This will lead to odd visual behaviour.`);\n    }\n    return (React.createElement(React.Fragment, null, exitingChildren.size\n        ? childrenToRender\n        : childrenToRender.map((child) => cloneElement(child))));\n};\n\nexport { AnimatePresence };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,OAAO;AAClF,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,kBAAkB,QAAQ,sCAAsC;AACzE,SAASC,yBAAyB,QAAQ,uCAAuC;AACjF,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,SAAS,QAAQ,wBAAwB;AAElD,MAAMC,WAAW,GAAIC,KAAK,IAAKA,KAAK,CAACC,GAAG,IAAI,EAAE;AAC9C,SAASC,iBAAiBA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAC9CD,QAAQ,CAACE,OAAO,CAAEL,KAAK,IAAK;IACxB,MAAMC,GAAG,GAAGF,WAAW,CAACC,KAAK,CAAC;IAC9BI,WAAW,CAACE,GAAG,CAACL,GAAG,EAAED,KAAK,CAAC;EAC/B,CAAC,CAAC;AACN;AACA,SAASO,YAAYA,CAACJ,QAAQ,EAAE;EAC5B,MAAMK,QAAQ,GAAG,EAAE;EACnB;EACAlB,QAAQ,CAACe,OAAO,CAACF,QAAQ,EAAGH,KAAK,IAAK;IAClC,IAAIT,cAAc,CAACS,KAAK,CAAC,EACrBQ,QAAQ,CAACC,IAAI,CAACT,KAAK,CAAC;EAC5B,CAAC,CAAC;EACF,OAAOQ,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,eAAe,GAAGA,CAAC;EAAEP,QAAQ;EAAEQ,MAAM;EAAEC,OAAO,GAAG,IAAI;EAAEC,cAAc;EAAEC,eAAe;EAAEC,qBAAqB,GAAG,IAAI;EAAEC,IAAI,GAAG;AAAQ,CAAC,KAAK;EAC7IlB,SAAS,CAAC,CAACgB,eAAe,EAAE,0CAA0C,CAAC;EACvE;EACA;EACA,MAAMG,WAAW,GAAG9B,UAAU,CAACQ,kBAAkB,CAAC,CAACsB,WAAW,IAAIzB,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrF,MAAM0B,SAAS,GAAGzB,YAAY,CAAC,CAAC;EAChC;EACA,MAAM0B,gBAAgB,GAAGZ,YAAY,CAACJ,QAAQ,CAAC;EAC/C,IAAIiB,gBAAgB,GAAGD,gBAAgB;EACvC,MAAME,eAAe,GAAGjC,MAAM,CAAC,IAAIkC,GAAG,CAAC,CAAC,CAAC,CAACC,OAAO;EACjD;EACA;EACA,MAAMC,eAAe,GAAGpC,MAAM,CAACgC,gBAAgB,CAAC;EAChD;EACA,MAAMhB,WAAW,GAAGhB,MAAM,CAAC,IAAIkC,GAAG,CAAC,CAAC,CAAC,CAACC,OAAO;EAC7C;EACA;EACA,MAAME,eAAe,GAAGrC,MAAM,CAAC,IAAI,CAAC;EACpCQ,yBAAyB,CAAC,MAAM;IAC5B6B,eAAe,CAACF,OAAO,GAAG,KAAK;IAC/BrB,iBAAiB,CAACiB,gBAAgB,EAAEf,WAAW,CAAC;IAChDoB,eAAe,CAACD,OAAO,GAAGH,gBAAgB;EAC9C,CAAC,CAAC;EACFvB,gBAAgB,CAAC,MAAM;IACnB4B,eAAe,CAACF,OAAO,GAAG,IAAI;IAC9BnB,WAAW,CAACsB,KAAK,CAAC,CAAC;IACnBL,eAAe,CAACK,KAAK,CAAC,CAAC;EAC3B,CAAC,CAAC;EACF,IAAID,eAAe,CAACF,OAAO,EAAE;IACzB,OAAQrC,KAAK,CAACyC,aAAa,CAACzC,KAAK,CAAC0C,QAAQ,EAAE,IAAI,EAAER,gBAAgB,CAACS,GAAG,CAAE7B,KAAK,IAAMd,KAAK,CAACyC,aAAa,CAACjC,aAAa,EAAE;MAAEO,GAAG,EAAEF,WAAW,CAACC,KAAK,CAAC;MAAE8B,SAAS,EAAE,IAAI;MAAElB,OAAO,EAAEA,OAAO,GAAGmB,SAAS,GAAG,KAAK;MAAEhB,qBAAqB,EAAEA,qBAAqB;MAAEC,IAAI,EAAEA;IAAK,CAAC,EAAEhB,KAAK,CAAE,CAAC,CAAC;EACjR;EACA;EACAoB,gBAAgB,GAAG,CAAC,GAAGA,gBAAgB,CAAC;EACxC;EACA;EACA,MAAMY,WAAW,GAAGR,eAAe,CAACD,OAAO,CAACM,GAAG,CAAC9B,WAAW,CAAC;EAC5D,MAAMkC,UAAU,GAAGd,gBAAgB,CAACU,GAAG,CAAC9B,WAAW,CAAC;EACpD;EACA,MAAMmC,UAAU,GAAGF,WAAW,CAACG,MAAM;EACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,UAAU,EAAEE,CAAC,EAAE,EAAE;IACjC,MAAMnC,GAAG,GAAG+B,WAAW,CAACI,CAAC,CAAC;IAC1B,IAAIH,UAAU,CAACI,OAAO,CAACpC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAACoB,eAAe,CAACiB,GAAG,CAACrC,GAAG,CAAC,EAAE;MAC7DoB,eAAe,CAACf,GAAG,CAACL,GAAG,EAAE8B,SAAS,CAAC;IACvC;EACJ;EACA;EACA;EACA,IAAIf,IAAI,KAAK,MAAM,IAAIK,eAAe,CAACkB,IAAI,EAAE;IACzCnB,gBAAgB,GAAG,EAAE;EACzB;EACA;EACA;EACAC,eAAe,CAAChB,OAAO,CAAC,CAACmC,SAAS,EAAEvC,GAAG,KAAK;IACxC;IACA,IAAIgC,UAAU,CAACI,OAAO,CAACpC,GAAG,CAAC,KAAK,CAAC,CAAC,EAC9B;IACJ,MAAMD,KAAK,GAAGI,WAAW,CAACqC,GAAG,CAACxC,GAAG,CAAC;IAClC,IAAI,CAACD,KAAK,EACN;IACJ,MAAM0C,cAAc,GAAGV,WAAW,CAACK,OAAO,CAACpC,GAAG,CAAC;IAC/C,IAAI0C,gBAAgB,GAAGH,SAAS;IAChC,IAAI,CAACG,gBAAgB,EAAE;MACnB,MAAMC,MAAM,GAAGA,CAAA,KAAM;QACjB;QACAvB,eAAe,CAACwB,MAAM,CAAC5C,GAAG,CAAC;QAC3B;QACA;QACA;QACA,MAAM6C,YAAY,GAAGC,KAAK,CAACC,IAAI,CAAC5C,WAAW,CAAC6C,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAAEC,QAAQ,IAAK,CAAClB,UAAU,CAACmB,QAAQ,CAACD,QAAQ,CAAC,CAAC;QACxG;QACAL,YAAY,CAACzC,OAAO,CAAEgD,WAAW,IAAKjD,WAAW,CAACyC,MAAM,CAACQ,WAAW,CAAC,CAAC;QACtE;QACA7B,eAAe,CAACD,OAAO,GAAGJ,gBAAgB,CAAC+B,MAAM,CAAEI,YAAY,IAAK;UAChE,MAAMC,eAAe,GAAGxD,WAAW,CAACuD,YAAY,CAAC;UACjD;YACA;YACAC,eAAe,KAAKtD,GAAG;YACnB;YACA6C,YAAY,CAACM,QAAQ,CAACG,eAAe;UAAC;QAC9C,CAAC,CAAC;QACF;QACA,IAAI,CAAClC,eAAe,CAACkB,IAAI,EAAE;UACvB,IAAIrB,SAAS,CAACK,OAAO,KAAK,KAAK,EAC3B;UACJN,WAAW,CAAC,CAAC;UACbJ,cAAc,IAAIA,cAAc,CAAC,CAAC;QACtC;MACJ,CAAC;MACD8B,gBAAgB,GAAIzD,KAAK,CAACyC,aAAa,CAACjC,aAAa,EAAE;QAAEO,GAAG,EAAEF,WAAW,CAACC,KAAK,CAAC;QAAE8B,SAAS,EAAE,KAAK;QAAEjB,cAAc,EAAE+B,MAAM;QAAEjC,MAAM,EAAEA,MAAM;QAAEI,qBAAqB,EAAEA,qBAAqB;QAAEC,IAAI,EAAEA;MAAK,CAAC,EAAEhB,KAAK,CAAE;MAC/MqB,eAAe,CAACf,GAAG,CAACL,GAAG,EAAE0C,gBAAgB,CAAC;IAC9C;IACAvB,gBAAgB,CAACoC,MAAM,CAACd,cAAc,EAAE,CAAC,EAAEC,gBAAgB,CAAC;EAChE,CAAC,CAAC;EACF;EACA;EACAvB,gBAAgB,GAAGA,gBAAgB,CAACS,GAAG,CAAE7B,KAAK,IAAK;IAC/C,MAAMC,GAAG,GAAGD,KAAK,CAACC,GAAG;IACrB,OAAOoB,eAAe,CAACiB,GAAG,CAACrC,GAAG,CAAC,GAAID,KAAK,GAAKd,KAAK,CAACyC,aAAa,CAACjC,aAAa,EAAE;MAAEO,GAAG,EAAEF,WAAW,CAACC,KAAK,CAAC;MAAE8B,SAAS,EAAE,IAAI;MAAEf,qBAAqB,EAAEA,qBAAqB;MAAEC,IAAI,EAAEA;IAAK,CAAC,EAAEhB,KAAK,CAAE;EACnM,CAAC,CAAC;EACF,IAAIyD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACrC3C,IAAI,KAAK,MAAM,IACfI,gBAAgB,CAACe,MAAM,GAAG,CAAC,EAAE;IAC7ByB,OAAO,CAACC,IAAI,CAAC,+IAA+I,CAAC;EACjK;EACA,OAAQ3E,KAAK,CAACyC,aAAa,CAACzC,KAAK,CAAC0C,QAAQ,EAAE,IAAI,EAAEP,eAAe,CAACkB,IAAI,GAChEnB,gBAAgB,GAChBA,gBAAgB,CAACS,GAAG,CAAE7B,KAAK,IAAKX,YAAY,CAACW,KAAK,CAAC,CAAC,CAAC;AAC/D,CAAC;AAED,SAASU,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}