{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('./state.js').State} State\n */\n\nimport { normalizeUri } from 'micromark-util-sanitize-uri';\n\n/**\n * Generate a hast footer for called footnote definitions.\n *\n * @param {State} state\n *   Info passed around.\n * @returns {Element | undefined}\n *   `section` element or `undefined`.\n */\nexport function footer(state) {\n  /** @type {Array<ElementContent>} */\n  const listItems = [];\n  let index = -1;\n  while (++index < state.footnoteOrder.length) {\n    const def = state.footnoteById[state.footnoteOrder[index]];\n    if (!def) {\n      continue;\n    }\n    const content = state.all(def);\n    const id = String(def.identifier).toUpperCase();\n    const safeId = normalizeUri(id.toLowerCase());\n    let referenceIndex = 0;\n    /** @type {Array<ElementContent>} */\n    const backReferences = [];\n    while (++referenceIndex <= state.footnoteCounts[id]) {\n      /** @type {Element} */\n      const backReference = {\n        type: 'element',\n        tagName: 'a',\n        properties: {\n          href: '#' + state.clobberPrefix + 'fnref-' + safeId + (referenceIndex > 1 ? '-' + referenceIndex : ''),\n          dataFootnoteBackref: true,\n          className: ['data-footnote-backref'],\n          ariaLabel: state.footnoteBackLabel\n        },\n        children: [{\n          type: 'text',\n          value: '↩'\n        }]\n      };\n      if (referenceIndex > 1) {\n        backReference.children.push({\n          type: 'element',\n          tagName: 'sup',\n          children: [{\n            type: 'text',\n            value: String(referenceIndex)\n          }]\n        });\n      }\n      if (backReferences.length > 0) {\n        backReferences.push({\n          type: 'text',\n          value: ' '\n        });\n      }\n      backReferences.push(backReference);\n    }\n    const tail = content[content.length - 1];\n    if (tail && tail.type === 'element' && tail.tagName === 'p') {\n      const tailTail = tail.children[tail.children.length - 1];\n      if (tailTail && tailTail.type === 'text') {\n        tailTail.value += ' ';\n      } else {\n        tail.children.push({\n          type: 'text',\n          value: ' '\n        });\n      }\n      tail.children.push(...backReferences);\n    } else {\n      content.push(...backReferences);\n    }\n\n    /** @type {Element} */\n    const listItem = {\n      type: 'element',\n      tagName: 'li',\n      properties: {\n        id: state.clobberPrefix + 'fn-' + safeId\n      },\n      children: state.wrap(content, true)\n    };\n    state.patch(def, listItem);\n    listItems.push(listItem);\n  }\n  if (listItems.length === 0) {\n    return;\n  }\n  return {\n    type: 'element',\n    tagName: 'section',\n    properties: {\n      dataFootnotes: true,\n      className: ['footnotes']\n    },\n    children: [{\n      type: 'element',\n      tagName: state.footnoteLabelTagName,\n      properties: {\n        // To do: use structured clone.\n        ...JSON.parse(JSON.stringify(state.footnoteLabelProperties)),\n        id: 'footnote-label'\n      },\n      children: [{\n        type: 'text',\n        value: state.footnoteLabel\n      }]\n    }, {\n      type: 'text',\n      value: '\\n'\n    }, {\n      type: 'element',\n      tagName: 'ol',\n      properties: {},\n      children: state.wrap(listItems, true)\n    }, {\n      type: 'text',\n      value: '\\n'\n    }]\n  };\n}", "map": {"version": 3, "names": ["normalizeUri", "footer", "state", "listItems", "index", "footnoteOrder", "length", "def", "footnoteById", "content", "all", "id", "String", "identifier", "toUpperCase", "safeId", "toLowerCase", "referenceIndex", "backReferences", "footnoteCounts", "backReference", "type", "tagName", "properties", "href", "clobberPrefix", "dataFootnoteBackref", "className", "aria<PERSON><PERSON><PERSON>", "footnoteBackLabel", "children", "value", "push", "tail", "tailTail", "listItem", "wrap", "patch", "dataFootnotes", "footnoteLabelTagName", "JSON", "parse", "stringify", "footnoteLabelProperties", "footnote<PERSON>abel"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/mdast-util-to-hast/lib/footer.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('./state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Generate a hast footer for called footnote definitions.\n *\n * @param {State} state\n *   Info passed around.\n * @returns {Element | undefined}\n *   `section` element or `undefined`.\n */\nexport function footer(state) {\n  /** @type {Array<ElementContent>} */\n  const listItems = []\n  let index = -1\n\n  while (++index < state.footnoteOrder.length) {\n    const def = state.footnoteById[state.footnoteOrder[index]]\n\n    if (!def) {\n      continue\n    }\n\n    const content = state.all(def)\n    const id = String(def.identifier).toUpperCase()\n    const safeId = normalizeUri(id.toLowerCase())\n    let referenceIndex = 0\n    /** @type {Array<ElementContent>} */\n    const backReferences = []\n\n    while (++referenceIndex <= state.footnoteCounts[id]) {\n      /** @type {Element} */\n      const backReference = {\n        type: 'element',\n        tagName: 'a',\n        properties: {\n          href:\n            '#' +\n            state.clobberPrefix +\n            'fnref-' +\n            safeId +\n            (referenceIndex > 1 ? '-' + referenceIndex : ''),\n          dataFootnoteBackref: true,\n          className: ['data-footnote-backref'],\n          ariaLabel: state.footnoteBackLabel\n        },\n        children: [{type: 'text', value: '↩'}]\n      }\n\n      if (referenceIndex > 1) {\n        backReference.children.push({\n          type: 'element',\n          tagName: 'sup',\n          children: [{type: 'text', value: String(referenceIndex)}]\n        })\n      }\n\n      if (backReferences.length > 0) {\n        backReferences.push({type: 'text', value: ' '})\n      }\n\n      backReferences.push(backReference)\n    }\n\n    const tail = content[content.length - 1]\n\n    if (tail && tail.type === 'element' && tail.tagName === 'p') {\n      const tailTail = tail.children[tail.children.length - 1]\n      if (tailTail && tailTail.type === 'text') {\n        tailTail.value += ' '\n      } else {\n        tail.children.push({type: 'text', value: ' '})\n      }\n\n      tail.children.push(...backReferences)\n    } else {\n      content.push(...backReferences)\n    }\n\n    /** @type {Element} */\n    const listItem = {\n      type: 'element',\n      tagName: 'li',\n      properties: {id: state.clobberPrefix + 'fn-' + safeId},\n      children: state.wrap(content, true)\n    }\n\n    state.patch(def, listItem)\n\n    listItems.push(listItem)\n  }\n\n  if (listItems.length === 0) {\n    return\n  }\n\n  return {\n    type: 'element',\n    tagName: 'section',\n    properties: {dataFootnotes: true, className: ['footnotes']},\n    children: [\n      {\n        type: 'element',\n        tagName: state.footnoteLabelTagName,\n        properties: {\n          // To do: use structured clone.\n          ...JSON.parse(JSON.stringify(state.footnoteLabelProperties)),\n          id: 'footnote-label'\n        },\n        children: [{type: 'text', value: state.footnoteLabel}]\n      },\n      {type: 'text', value: '\\n'},\n      {\n        type: 'element',\n        tagName: 'ol',\n        properties: {},\n        children: state.wrap(listItems, true)\n      },\n      {type: 'text', value: '\\n'}\n    ]\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,6BAA6B;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,KAAK,EAAE;EAC5B;EACA,MAAMC,SAAS,GAAG,EAAE;EACpB,IAAIC,KAAK,GAAG,CAAC,CAAC;EAEd,OAAO,EAAEA,KAAK,GAAGF,KAAK,CAACG,aAAa,CAACC,MAAM,EAAE;IAC3C,MAAMC,GAAG,GAAGL,KAAK,CAACM,YAAY,CAACN,KAAK,CAACG,aAAa,CAACD,KAAK,CAAC,CAAC;IAE1D,IAAI,CAACG,GAAG,EAAE;MACR;IACF;IAEA,MAAME,OAAO,GAAGP,KAAK,CAACQ,GAAG,CAACH,GAAG,CAAC;IAC9B,MAAMI,EAAE,GAAGC,MAAM,CAACL,GAAG,CAACM,UAAU,CAAC,CAACC,WAAW,CAAC,CAAC;IAC/C,MAAMC,MAAM,GAAGf,YAAY,CAACW,EAAE,CAACK,WAAW,CAAC,CAAC,CAAC;IAC7C,IAAIC,cAAc,GAAG,CAAC;IACtB;IACA,MAAMC,cAAc,GAAG,EAAE;IAEzB,OAAO,EAAED,cAAc,IAAIf,KAAK,CAACiB,cAAc,CAACR,EAAE,CAAC,EAAE;MACnD;MACA,MAAMS,aAAa,GAAG;QACpBC,IAAI,EAAE,SAAS;QACfC,OAAO,EAAE,GAAG;QACZC,UAAU,EAAE;UACVC,IAAI,EACF,GAAG,GACHtB,KAAK,CAACuB,aAAa,GACnB,QAAQ,GACRV,MAAM,IACLE,cAAc,GAAG,CAAC,GAAG,GAAG,GAAGA,cAAc,GAAG,EAAE,CAAC;UAClDS,mBAAmB,EAAE,IAAI;UACzBC,SAAS,EAAE,CAAC,uBAAuB,CAAC;UACpCC,SAAS,EAAE1B,KAAK,CAAC2B;QACnB,CAAC;QACDC,QAAQ,EAAE,CAAC;UAACT,IAAI,EAAE,MAAM;UAAEU,KAAK,EAAE;QAAG,CAAC;MACvC,CAAC;MAED,IAAId,cAAc,GAAG,CAAC,EAAE;QACtBG,aAAa,CAACU,QAAQ,CAACE,IAAI,CAAC;UAC1BX,IAAI,EAAE,SAAS;UACfC,OAAO,EAAE,KAAK;UACdQ,QAAQ,EAAE,CAAC;YAACT,IAAI,EAAE,MAAM;YAAEU,KAAK,EAAEnB,MAAM,CAACK,cAAc;UAAC,CAAC;QAC1D,CAAC,CAAC;MACJ;MAEA,IAAIC,cAAc,CAACZ,MAAM,GAAG,CAAC,EAAE;QAC7BY,cAAc,CAACc,IAAI,CAAC;UAACX,IAAI,EAAE,MAAM;UAAEU,KAAK,EAAE;QAAG,CAAC,CAAC;MACjD;MAEAb,cAAc,CAACc,IAAI,CAACZ,aAAa,CAAC;IACpC;IAEA,MAAMa,IAAI,GAAGxB,OAAO,CAACA,OAAO,CAACH,MAAM,GAAG,CAAC,CAAC;IAExC,IAAI2B,IAAI,IAAIA,IAAI,CAACZ,IAAI,KAAK,SAAS,IAAIY,IAAI,CAACX,OAAO,KAAK,GAAG,EAAE;MAC3D,MAAMY,QAAQ,GAAGD,IAAI,CAACH,QAAQ,CAACG,IAAI,CAACH,QAAQ,CAACxB,MAAM,GAAG,CAAC,CAAC;MACxD,IAAI4B,QAAQ,IAAIA,QAAQ,CAACb,IAAI,KAAK,MAAM,EAAE;QACxCa,QAAQ,CAACH,KAAK,IAAI,GAAG;MACvB,CAAC,MAAM;QACLE,IAAI,CAACH,QAAQ,CAACE,IAAI,CAAC;UAACX,IAAI,EAAE,MAAM;UAAEU,KAAK,EAAE;QAAG,CAAC,CAAC;MAChD;MAEAE,IAAI,CAACH,QAAQ,CAACE,IAAI,CAAC,GAAGd,cAAc,CAAC;IACvC,CAAC,MAAM;MACLT,OAAO,CAACuB,IAAI,CAAC,GAAGd,cAAc,CAAC;IACjC;;IAEA;IACA,MAAMiB,QAAQ,GAAG;MACfd,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE;QAACZ,EAAE,EAAET,KAAK,CAACuB,aAAa,GAAG,KAAK,GAAGV;MAAM,CAAC;MACtDe,QAAQ,EAAE5B,KAAK,CAACkC,IAAI,CAAC3B,OAAO,EAAE,IAAI;IACpC,CAAC;IAEDP,KAAK,CAACmC,KAAK,CAAC9B,GAAG,EAAE4B,QAAQ,CAAC;IAE1BhC,SAAS,CAAC6B,IAAI,CAACG,QAAQ,CAAC;EAC1B;EAEA,IAAIhC,SAAS,CAACG,MAAM,KAAK,CAAC,EAAE;IAC1B;EACF;EAEA,OAAO;IACLe,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE;MAACe,aAAa,EAAE,IAAI;MAAEX,SAAS,EAAE,CAAC,WAAW;IAAC,CAAC;IAC3DG,QAAQ,EAAE,CACR;MACET,IAAI,EAAE,SAAS;MACfC,OAAO,EAAEpB,KAAK,CAACqC,oBAAoB;MACnChB,UAAU,EAAE;QACV;QACA,GAAGiB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACxC,KAAK,CAACyC,uBAAuB,CAAC,CAAC;QAC5DhC,EAAE,EAAE;MACN,CAAC;MACDmB,QAAQ,EAAE,CAAC;QAACT,IAAI,EAAE,MAAM;QAAEU,KAAK,EAAE7B,KAAK,CAAC0C;MAAa,CAAC;IACvD,CAAC,EACD;MAACvB,IAAI,EAAE,MAAM;MAAEU,KAAK,EAAE;IAAI,CAAC,EAC3B;MACEV,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,CAAC,CAAC;MACdO,QAAQ,EAAE5B,KAAK,CAACkC,IAAI,CAACjC,SAAS,EAAE,IAAI;IACtC,CAAC,EACD;MAACkB,IAAI,EAAE,MAAM;MAAEU,KAAK,EAAE;IAAI,CAAC;EAE/B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}