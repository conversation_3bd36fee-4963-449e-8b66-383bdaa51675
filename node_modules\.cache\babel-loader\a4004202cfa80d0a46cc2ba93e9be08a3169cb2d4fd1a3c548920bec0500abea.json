{"ast": null, "code": "import { keyframes } from '../../generators/keyframes.mjs';\nimport { spring } from '../../generators/spring/index.mjs';\nimport { inertia } from '../../generators/inertia.mjs';\nimport { frameloopDriver } from './driver-frameloop.mjs';\nimport { interpolate } from '../../../utils/interpolate.mjs';\nimport { clamp } from '../../../utils/clamp.mjs';\nimport { millisecondsToSeconds, secondsToMilliseconds } from '../../../utils/time-conversion.mjs';\nimport { calcGeneratorDuration } from '../../generators/utils/calc-duration.mjs';\nimport { invariant } from '../../../utils/errors.mjs';\nconst types = {\n  decay: inertia,\n  inertia,\n  tween: keyframes,\n  keyframes: keyframes,\n  spring\n};\n/**\n * Animate a single value on the main thread.\n *\n * This function is written, where functionality overlaps,\n * to be largely spec-compliant with WAAPI to allow fungibility\n * between the two.\n */\nfunction animateValue({\n  autoplay = true,\n  delay = 0,\n  driver = frameloopDriver,\n  keyframes: keyframes$1,\n  type = \"keyframes\",\n  repeat = 0,\n  repeatDelay = 0,\n  repeatType = \"loop\",\n  onPlay,\n  onStop,\n  onComplete,\n  onUpdate,\n  ...options\n}) {\n  let speed = 1;\n  let hasStopped = false;\n  let resolveFinishedPromise;\n  let currentFinishedPromise;\n  /**\n   * Resolve the current Promise every time we enter the\n   * finished state. This is WAAPI-compatible behaviour.\n   */\n  const updateFinishedPromise = () => {\n    currentFinishedPromise = new Promise(resolve => {\n      resolveFinishedPromise = resolve;\n    });\n  };\n  // Create the first finished promise\n  updateFinishedPromise();\n  let animationDriver;\n  const generatorFactory = types[type] || keyframes;\n  /**\n   * If this isn't the keyframes generator and we've been provided\n   * strings as keyframes, we need to interpolate these.\n   */\n  let mapNumbersToKeyframes;\n  if (generatorFactory !== keyframes && typeof keyframes$1[0] !== \"number\") {\n    if (process.env.NODE_ENV !== \"production\") {\n      invariant(keyframes$1.length === 2, `Only two keyframes currently supported with spring and inertia animations. Trying to animate ${keyframes$1}`);\n    }\n    mapNumbersToKeyframes = interpolate([0, 100], keyframes$1, {\n      clamp: false\n    });\n    keyframes$1 = [0, 100];\n  }\n  const generator = generatorFactory({\n    ...options,\n    keyframes: keyframes$1\n  });\n  let mirroredGenerator;\n  if (repeatType === \"mirror\") {\n    mirroredGenerator = generatorFactory({\n      ...options,\n      keyframes: [...keyframes$1].reverse(),\n      velocity: -(options.velocity || 0)\n    });\n  }\n  let playState = \"idle\";\n  let holdTime = null;\n  let startTime = null;\n  let cancelTime = null;\n  /**\n   * If duration is undefined and we have repeat options,\n   * we need to calculate a duration from the generator.\n   *\n   * We set it to the generator itself to cache the duration.\n   * Any timeline resolver will need to have already precalculated\n   * the duration by this step.\n   */\n  if (generator.calculatedDuration === null && repeat) {\n    generator.calculatedDuration = calcGeneratorDuration(generator);\n  }\n  const {\n    calculatedDuration\n  } = generator;\n  let resolvedDuration = Infinity;\n  let totalDuration = Infinity;\n  if (calculatedDuration !== null) {\n    resolvedDuration = calculatedDuration + repeatDelay;\n    totalDuration = resolvedDuration * (repeat + 1) - repeatDelay;\n  }\n  let currentTime = 0;\n  const tick = timestamp => {\n    if (startTime === null) return;\n    /**\n     * requestAnimationFrame timestamps can come through as lower than\n     * the startTime as set by performance.now(). Here we prevent this,\n     * though in the future it could be possible to make setting startTime\n     * a pending operation that gets resolved here.\n     */\n    if (speed > 0) startTime = Math.min(startTime, timestamp);\n    if (speed < 0) startTime = Math.min(timestamp - totalDuration / speed, startTime);\n    if (holdTime !== null) {\n      currentTime = holdTime;\n    } else {\n      // Rounding the time because floating point arithmetic is not always accurate, e.g. 3000.367 - 1000.367 =\n      // 2000.0000000000002. This is a problem when we are comparing the currentTime with the duration, for\n      // example.\n      currentTime = Math.round(timestamp - startTime) * speed;\n    }\n    // Rebase on delay\n    const timeWithoutDelay = currentTime - delay * (speed >= 0 ? 1 : -1);\n    const isInDelayPhase = speed >= 0 ? timeWithoutDelay < 0 : timeWithoutDelay > totalDuration;\n    currentTime = Math.max(timeWithoutDelay, 0);\n    /**\n     * If this animation has finished, set the current time\n     * to the total duration.\n     */\n    if (playState === \"finished\" && holdTime === null) {\n      currentTime = totalDuration;\n    }\n    let elapsed = currentTime;\n    let frameGenerator = generator;\n    if (repeat) {\n      /**\n       * Get the current progress (0-1) of the animation. If t is >\n       * than duration we'll get values like 2.5 (midway through the\n       * third iteration)\n       */\n      const progress = Math.min(currentTime, totalDuration) / resolvedDuration;\n      /**\n       * Get the current iteration (0 indexed). For instance the floor of\n       * 2.5 is 2.\n       */\n      let currentIteration = Math.floor(progress);\n      /**\n       * Get the current progress of the iteration by taking the remainder\n       * so 2.5 is 0.5 through iteration 2\n       */\n      let iterationProgress = progress % 1.0;\n      /**\n       * If iteration progress is 1 we count that as the end\n       * of the previous iteration.\n       */\n      if (!iterationProgress && progress >= 1) {\n        iterationProgress = 1;\n      }\n      iterationProgress === 1 && currentIteration--;\n      currentIteration = Math.min(currentIteration, repeat + 1);\n      /**\n       * Reverse progress if we're not running in \"normal\" direction\n       */\n      const isOddIteration = Boolean(currentIteration % 2);\n      if (isOddIteration) {\n        if (repeatType === \"reverse\") {\n          iterationProgress = 1 - iterationProgress;\n          if (repeatDelay) {\n            iterationProgress -= repeatDelay / resolvedDuration;\n          }\n        } else if (repeatType === \"mirror\") {\n          frameGenerator = mirroredGenerator;\n        }\n      }\n      elapsed = clamp(0, 1, iterationProgress) * resolvedDuration;\n    }\n    /**\n     * If we're in negative time, set state as the initial keyframe.\n     * This prevents delay: x, duration: 0 animations from finishing\n     * instantly.\n     */\n    const state = isInDelayPhase ? {\n      done: false,\n      value: keyframes$1[0]\n    } : frameGenerator.next(elapsed);\n    if (mapNumbersToKeyframes) {\n      state.value = mapNumbersToKeyframes(state.value);\n    }\n    let {\n      done\n    } = state;\n    if (!isInDelayPhase && calculatedDuration !== null) {\n      done = speed >= 0 ? currentTime >= totalDuration : currentTime <= 0;\n    }\n    const isAnimationFinished = holdTime === null && (playState === \"finished\" || playState === \"running\" && done);\n    if (onUpdate) {\n      onUpdate(state.value);\n    }\n    if (isAnimationFinished) {\n      finish();\n    }\n    return state;\n  };\n  const stopAnimationDriver = () => {\n    animationDriver && animationDriver.stop();\n    animationDriver = undefined;\n  };\n  const cancel = () => {\n    playState = \"idle\";\n    stopAnimationDriver();\n    resolveFinishedPromise();\n    updateFinishedPromise();\n    startTime = cancelTime = null;\n  };\n  const finish = () => {\n    playState = \"finished\";\n    onComplete && onComplete();\n    stopAnimationDriver();\n    resolveFinishedPromise();\n  };\n  const play = () => {\n    if (hasStopped) return;\n    if (!animationDriver) animationDriver = driver(tick);\n    const now = animationDriver.now();\n    onPlay && onPlay();\n    if (holdTime !== null) {\n      startTime = now - holdTime;\n    } else if (!startTime || playState === \"finished\") {\n      startTime = now;\n    }\n    if (playState === \"finished\") {\n      updateFinishedPromise();\n    }\n    cancelTime = startTime;\n    holdTime = null;\n    /**\n     * Set playState to running only after we've used it in\n     * the previous logic.\n     */\n    playState = \"running\";\n    animationDriver.start();\n  };\n  if (autoplay) {\n    play();\n  }\n  const controls = {\n    then(resolve, reject) {\n      return currentFinishedPromise.then(resolve, reject);\n    },\n    get time() {\n      return millisecondsToSeconds(currentTime);\n    },\n    set time(newTime) {\n      newTime = secondsToMilliseconds(newTime);\n      currentTime = newTime;\n      if (holdTime !== null || !animationDriver || speed === 0) {\n        holdTime = newTime;\n      } else {\n        startTime = animationDriver.now() - newTime / speed;\n      }\n    },\n    get duration() {\n      const duration = generator.calculatedDuration === null ? calcGeneratorDuration(generator) : generator.calculatedDuration;\n      return millisecondsToSeconds(duration);\n    },\n    get speed() {\n      return speed;\n    },\n    set speed(newSpeed) {\n      if (newSpeed === speed || !animationDriver) return;\n      speed = newSpeed;\n      controls.time = millisecondsToSeconds(currentTime);\n    },\n    get state() {\n      return playState;\n    },\n    play,\n    pause: () => {\n      playState = \"paused\";\n      holdTime = currentTime;\n    },\n    stop: () => {\n      hasStopped = true;\n      if (playState === \"idle\") return;\n      playState = \"idle\";\n      onStop && onStop();\n      cancel();\n    },\n    cancel: () => {\n      if (cancelTime !== null) tick(cancelTime);\n      cancel();\n    },\n    complete: () => {\n      playState = \"finished\";\n    },\n    sample: elapsed => {\n      startTime = 0;\n      return tick(elapsed);\n    }\n  };\n  return controls;\n}\nexport { animateValue };", "map": {"version": 3, "names": ["keyframes", "spring", "inertia", "frameloopDriver", "interpolate", "clamp", "millisecondsToSeconds", "secondsToMilliseconds", "calcGeneratorDuration", "invariant", "types", "decay", "tween", "animateValue", "autoplay", "delay", "driver", "keyframes$1", "type", "repeat", "repeatDelay", "repeatType", "onPlay", "onStop", "onComplete", "onUpdate", "options", "speed", "hasStopped", "resolveFinishedPromise", "currentFinishedPromise", "updateFinishedPromise", "Promise", "resolve", "animationDriver", "generatorFactory", "mapNumbersToKeyframes", "process", "env", "NODE_ENV", "length", "generator", "mirroredGenerator", "reverse", "velocity", "playState", "holdTime", "startTime", "cancelTime", "calculatedDuration", "resolvedDuration", "Infinity", "totalDuration", "currentTime", "tick", "timestamp", "Math", "min", "round", "timeWithoutDelay", "isInDelayPhase", "max", "elapsed", "frameGenerator", "progress", "currentIteration", "floor", "iterationProgress", "isOddIteration", "Boolean", "state", "done", "value", "next", "isAnimationFinished", "finish", "stopAnimationDriver", "stop", "undefined", "cancel", "play", "now", "start", "controls", "then", "reject", "time", "newTime", "duration", "newSpeed", "pause", "complete", "sample"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/framer-motion/dist/es/animation/animators/js/index.mjs"], "sourcesContent": ["import { keyframes } from '../../generators/keyframes.mjs';\nimport { spring } from '../../generators/spring/index.mjs';\nimport { inertia } from '../../generators/inertia.mjs';\nimport { frameloopDriver } from './driver-frameloop.mjs';\nimport { interpolate } from '../../../utils/interpolate.mjs';\nimport { clamp } from '../../../utils/clamp.mjs';\nimport { millisecondsToSeconds, secondsToMilliseconds } from '../../../utils/time-conversion.mjs';\nimport { calcGeneratorDuration } from '../../generators/utils/calc-duration.mjs';\nimport { invariant } from '../../../utils/errors.mjs';\n\nconst types = {\n    decay: inertia,\n    inertia,\n    tween: keyframes,\n    keyframes: keyframes,\n    spring,\n};\n/**\n * Animate a single value on the main thread.\n *\n * This function is written, where functionality overlaps,\n * to be largely spec-compliant with WAAPI to allow fungibility\n * between the two.\n */\nfunction animateValue({ autoplay = true, delay = 0, driver = frameloopDriver, keyframes: keyframes$1, type = \"keyframes\", repeat = 0, repeatDelay = 0, repeatType = \"loop\", onPlay, onStop, onComplete, onUpdate, ...options }) {\n    let speed = 1;\n    let hasStopped = false;\n    let resolveFinishedPromise;\n    let currentFinishedPromise;\n    /**\n     * Resolve the current Promise every time we enter the\n     * finished state. This is WAAPI-compatible behaviour.\n     */\n    const updateFinishedPromise = () => {\n        currentFinishedPromise = new Promise((resolve) => {\n            resolveFinishedPromise = resolve;\n        });\n    };\n    // Create the first finished promise\n    updateFinishedPromise();\n    let animationDriver;\n    const generatorFactory = types[type] || keyframes;\n    /**\n     * If this isn't the keyframes generator and we've been provided\n     * strings as keyframes, we need to interpolate these.\n     */\n    let mapNumbersToKeyframes;\n    if (generatorFactory !== keyframes &&\n        typeof keyframes$1[0] !== \"number\") {\n        if (process.env.NODE_ENV !== \"production\") {\n            invariant(keyframes$1.length === 2, `Only two keyframes currently supported with spring and inertia animations. Trying to animate ${keyframes$1}`);\n        }\n        mapNumbersToKeyframes = interpolate([0, 100], keyframes$1, {\n            clamp: false,\n        });\n        keyframes$1 = [0, 100];\n    }\n    const generator = generatorFactory({ ...options, keyframes: keyframes$1 });\n    let mirroredGenerator;\n    if (repeatType === \"mirror\") {\n        mirroredGenerator = generatorFactory({\n            ...options,\n            keyframes: [...keyframes$1].reverse(),\n            velocity: -(options.velocity || 0),\n        });\n    }\n    let playState = \"idle\";\n    let holdTime = null;\n    let startTime = null;\n    let cancelTime = null;\n    /**\n     * If duration is undefined and we have repeat options,\n     * we need to calculate a duration from the generator.\n     *\n     * We set it to the generator itself to cache the duration.\n     * Any timeline resolver will need to have already precalculated\n     * the duration by this step.\n     */\n    if (generator.calculatedDuration === null && repeat) {\n        generator.calculatedDuration = calcGeneratorDuration(generator);\n    }\n    const { calculatedDuration } = generator;\n    let resolvedDuration = Infinity;\n    let totalDuration = Infinity;\n    if (calculatedDuration !== null) {\n        resolvedDuration = calculatedDuration + repeatDelay;\n        totalDuration = resolvedDuration * (repeat + 1) - repeatDelay;\n    }\n    let currentTime = 0;\n    const tick = (timestamp) => {\n        if (startTime === null)\n            return;\n        /**\n         * requestAnimationFrame timestamps can come through as lower than\n         * the startTime as set by performance.now(). Here we prevent this,\n         * though in the future it could be possible to make setting startTime\n         * a pending operation that gets resolved here.\n         */\n        if (speed > 0)\n            startTime = Math.min(startTime, timestamp);\n        if (speed < 0)\n            startTime = Math.min(timestamp - totalDuration / speed, startTime);\n        if (holdTime !== null) {\n            currentTime = holdTime;\n        }\n        else {\n            // Rounding the time because floating point arithmetic is not always accurate, e.g. 3000.367 - 1000.367 =\n            // 2000.0000000000002. This is a problem when we are comparing the currentTime with the duration, for\n            // example.\n            currentTime = Math.round(timestamp - startTime) * speed;\n        }\n        // Rebase on delay\n        const timeWithoutDelay = currentTime - delay * (speed >= 0 ? 1 : -1);\n        const isInDelayPhase = speed >= 0 ? timeWithoutDelay < 0 : timeWithoutDelay > totalDuration;\n        currentTime = Math.max(timeWithoutDelay, 0);\n        /**\n         * If this animation has finished, set the current time\n         * to the total duration.\n         */\n        if (playState === \"finished\" && holdTime === null) {\n            currentTime = totalDuration;\n        }\n        let elapsed = currentTime;\n        let frameGenerator = generator;\n        if (repeat) {\n            /**\n             * Get the current progress (0-1) of the animation. If t is >\n             * than duration we'll get values like 2.5 (midway through the\n             * third iteration)\n             */\n            const progress = Math.min(currentTime, totalDuration) / resolvedDuration;\n            /**\n             * Get the current iteration (0 indexed). For instance the floor of\n             * 2.5 is 2.\n             */\n            let currentIteration = Math.floor(progress);\n            /**\n             * Get the current progress of the iteration by taking the remainder\n             * so 2.5 is 0.5 through iteration 2\n             */\n            let iterationProgress = progress % 1.0;\n            /**\n             * If iteration progress is 1 we count that as the end\n             * of the previous iteration.\n             */\n            if (!iterationProgress && progress >= 1) {\n                iterationProgress = 1;\n            }\n            iterationProgress === 1 && currentIteration--;\n            currentIteration = Math.min(currentIteration, repeat + 1);\n            /**\n             * Reverse progress if we're not running in \"normal\" direction\n             */\n            const isOddIteration = Boolean(currentIteration % 2);\n            if (isOddIteration) {\n                if (repeatType === \"reverse\") {\n                    iterationProgress = 1 - iterationProgress;\n                    if (repeatDelay) {\n                        iterationProgress -= repeatDelay / resolvedDuration;\n                    }\n                }\n                else if (repeatType === \"mirror\") {\n                    frameGenerator = mirroredGenerator;\n                }\n            }\n            elapsed = clamp(0, 1, iterationProgress) * resolvedDuration;\n        }\n        /**\n         * If we're in negative time, set state as the initial keyframe.\n         * This prevents delay: x, duration: 0 animations from finishing\n         * instantly.\n         */\n        const state = isInDelayPhase\n            ? { done: false, value: keyframes$1[0] }\n            : frameGenerator.next(elapsed);\n        if (mapNumbersToKeyframes) {\n            state.value = mapNumbersToKeyframes(state.value);\n        }\n        let { done } = state;\n        if (!isInDelayPhase && calculatedDuration !== null) {\n            done = speed >= 0 ? currentTime >= totalDuration : currentTime <= 0;\n        }\n        const isAnimationFinished = holdTime === null &&\n            (playState === \"finished\" || (playState === \"running\" && done));\n        if (onUpdate) {\n            onUpdate(state.value);\n        }\n        if (isAnimationFinished) {\n            finish();\n        }\n        return state;\n    };\n    const stopAnimationDriver = () => {\n        animationDriver && animationDriver.stop();\n        animationDriver = undefined;\n    };\n    const cancel = () => {\n        playState = \"idle\";\n        stopAnimationDriver();\n        resolveFinishedPromise();\n        updateFinishedPromise();\n        startTime = cancelTime = null;\n    };\n    const finish = () => {\n        playState = \"finished\";\n        onComplete && onComplete();\n        stopAnimationDriver();\n        resolveFinishedPromise();\n    };\n    const play = () => {\n        if (hasStopped)\n            return;\n        if (!animationDriver)\n            animationDriver = driver(tick);\n        const now = animationDriver.now();\n        onPlay && onPlay();\n        if (holdTime !== null) {\n            startTime = now - holdTime;\n        }\n        else if (!startTime || playState === \"finished\") {\n            startTime = now;\n        }\n        if (playState === \"finished\") {\n            updateFinishedPromise();\n        }\n        cancelTime = startTime;\n        holdTime = null;\n        /**\n         * Set playState to running only after we've used it in\n         * the previous logic.\n         */\n        playState = \"running\";\n        animationDriver.start();\n    };\n    if (autoplay) {\n        play();\n    }\n    const controls = {\n        then(resolve, reject) {\n            return currentFinishedPromise.then(resolve, reject);\n        },\n        get time() {\n            return millisecondsToSeconds(currentTime);\n        },\n        set time(newTime) {\n            newTime = secondsToMilliseconds(newTime);\n            currentTime = newTime;\n            if (holdTime !== null || !animationDriver || speed === 0) {\n                holdTime = newTime;\n            }\n            else {\n                startTime = animationDriver.now() - newTime / speed;\n            }\n        },\n        get duration() {\n            const duration = generator.calculatedDuration === null\n                ? calcGeneratorDuration(generator)\n                : generator.calculatedDuration;\n            return millisecondsToSeconds(duration);\n        },\n        get speed() {\n            return speed;\n        },\n        set speed(newSpeed) {\n            if (newSpeed === speed || !animationDriver)\n                return;\n            speed = newSpeed;\n            controls.time = millisecondsToSeconds(currentTime);\n        },\n        get state() {\n            return playState;\n        },\n        play,\n        pause: () => {\n            playState = \"paused\";\n            holdTime = currentTime;\n        },\n        stop: () => {\n            hasStopped = true;\n            if (playState === \"idle\")\n                return;\n            playState = \"idle\";\n            onStop && onStop();\n            cancel();\n        },\n        cancel: () => {\n            if (cancelTime !== null)\n                tick(cancelTime);\n            cancel();\n        },\n        complete: () => {\n            playState = \"finished\";\n        },\n        sample: (elapsed) => {\n            startTime = 0;\n            return tick(elapsed);\n        },\n    };\n    return controls;\n}\n\nexport { animateValue };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,gCAAgC;AAC1D,SAASC,MAAM,QAAQ,mCAAmC;AAC1D,SAASC,OAAO,QAAQ,8BAA8B;AACtD,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,KAAK,QAAQ,0BAA0B;AAChD,SAASC,qBAAqB,EAAEC,qBAAqB,QAAQ,oCAAoC;AACjG,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,SAASC,SAAS,QAAQ,2BAA2B;AAErD,MAAMC,KAAK,GAAG;EACVC,KAAK,EAAET,OAAO;EACdA,OAAO;EACPU,KAAK,EAAEZ,SAAS;EAChBA,SAAS,EAAEA,SAAS;EACpBC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,YAAYA,CAAC;EAAEC,QAAQ,GAAG,IAAI;EAAEC,KAAK,GAAG,CAAC;EAAEC,MAAM,GAAGb,eAAe;EAAEH,SAAS,EAAEiB,WAAW;EAAEC,IAAI,GAAG,WAAW;EAAEC,MAAM,GAAG,CAAC;EAAEC,WAAW,GAAG,CAAC;EAAEC,UAAU,GAAG,MAAM;EAAEC,MAAM;EAAEC,MAAM;EAAEC,UAAU;EAAEC,QAAQ;EAAE,GAAGC;AAAQ,CAAC,EAAE;EAC5N,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,sBAAsB;EAC1B,IAAIC,sBAAsB;EAC1B;AACJ;AACA;AACA;EACI,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAChCD,sBAAsB,GAAG,IAAIE,OAAO,CAAEC,OAAO,IAAK;MAC9CJ,sBAAsB,GAAGI,OAAO;IACpC,CAAC,CAAC;EACN,CAAC;EACD;EACAF,qBAAqB,CAAC,CAAC;EACvB,IAAIG,eAAe;EACnB,MAAMC,gBAAgB,GAAGzB,KAAK,CAACQ,IAAI,CAAC,IAAIlB,SAAS;EACjD;AACJ;AACA;AACA;EACI,IAAIoC,qBAAqB;EACzB,IAAID,gBAAgB,KAAKnC,SAAS,IAC9B,OAAOiB,WAAW,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IACpC,IAAIoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvC9B,SAAS,CAACQ,WAAW,CAACuB,MAAM,KAAK,CAAC,EAAE,gGAAgGvB,WAAW,EAAE,CAAC;IACtJ;IACAmB,qBAAqB,GAAGhC,WAAW,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,EAAEa,WAAW,EAAE;MACvDZ,KAAK,EAAE;IACX,CAAC,CAAC;IACFY,WAAW,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC;EAC1B;EACA,MAAMwB,SAAS,GAAGN,gBAAgB,CAAC;IAAE,GAAGT,OAAO;IAAE1B,SAAS,EAAEiB;EAAY,CAAC,CAAC;EAC1E,IAAIyB,iBAAiB;EACrB,IAAIrB,UAAU,KAAK,QAAQ,EAAE;IACzBqB,iBAAiB,GAAGP,gBAAgB,CAAC;MACjC,GAAGT,OAAO;MACV1B,SAAS,EAAE,CAAC,GAAGiB,WAAW,CAAC,CAAC0B,OAAO,CAAC,CAAC;MACrCC,QAAQ,EAAE,EAAElB,OAAO,CAACkB,QAAQ,IAAI,CAAC;IACrC,CAAC,CAAC;EACN;EACA,IAAIC,SAAS,GAAG,MAAM;EACtB,IAAIC,QAAQ,GAAG,IAAI;EACnB,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAIC,UAAU,GAAG,IAAI;EACrB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIP,SAAS,CAACQ,kBAAkB,KAAK,IAAI,IAAI9B,MAAM,EAAE;IACjDsB,SAAS,CAACQ,kBAAkB,GAAGzC,qBAAqB,CAACiC,SAAS,CAAC;EACnE;EACA,MAAM;IAAEQ;EAAmB,CAAC,GAAGR,SAAS;EACxC,IAAIS,gBAAgB,GAAGC,QAAQ;EAC/B,IAAIC,aAAa,GAAGD,QAAQ;EAC5B,IAAIF,kBAAkB,KAAK,IAAI,EAAE;IAC7BC,gBAAgB,GAAGD,kBAAkB,GAAG7B,WAAW;IACnDgC,aAAa,GAAGF,gBAAgB,IAAI/B,MAAM,GAAG,CAAC,CAAC,GAAGC,WAAW;EACjE;EACA,IAAIiC,WAAW,GAAG,CAAC;EACnB,MAAMC,IAAI,GAAIC,SAAS,IAAK;IACxB,IAAIR,SAAS,KAAK,IAAI,EAClB;IACJ;AACR;AACA;AACA;AACA;AACA;IACQ,IAAIpB,KAAK,GAAG,CAAC,EACToB,SAAS,GAAGS,IAAI,CAACC,GAAG,CAACV,SAAS,EAAEQ,SAAS,CAAC;IAC9C,IAAI5B,KAAK,GAAG,CAAC,EACToB,SAAS,GAAGS,IAAI,CAACC,GAAG,CAACF,SAAS,GAAGH,aAAa,GAAGzB,KAAK,EAAEoB,SAAS,CAAC;IACtE,IAAID,QAAQ,KAAK,IAAI,EAAE;MACnBO,WAAW,GAAGP,QAAQ;IAC1B,CAAC,MACI;MACD;MACA;MACA;MACAO,WAAW,GAAGG,IAAI,CAACE,KAAK,CAACH,SAAS,GAAGR,SAAS,CAAC,GAAGpB,KAAK;IAC3D;IACA;IACA,MAAMgC,gBAAgB,GAAGN,WAAW,GAAGtC,KAAK,IAAIY,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACpE,MAAMiC,cAAc,GAAGjC,KAAK,IAAI,CAAC,GAAGgC,gBAAgB,GAAG,CAAC,GAAGA,gBAAgB,GAAGP,aAAa;IAC3FC,WAAW,GAAGG,IAAI,CAACK,GAAG,CAACF,gBAAgB,EAAE,CAAC,CAAC;IAC3C;AACR;AACA;AACA;IACQ,IAAId,SAAS,KAAK,UAAU,IAAIC,QAAQ,KAAK,IAAI,EAAE;MAC/CO,WAAW,GAAGD,aAAa;IAC/B;IACA,IAAIU,OAAO,GAAGT,WAAW;IACzB,IAAIU,cAAc,GAAGtB,SAAS;IAC9B,IAAItB,MAAM,EAAE;MACR;AACZ;AACA;AACA;AACA;MACY,MAAM6C,QAAQ,GAAGR,IAAI,CAACC,GAAG,CAACJ,WAAW,EAAED,aAAa,CAAC,GAAGF,gBAAgB;MACxE;AACZ;AACA;AACA;MACY,IAAIe,gBAAgB,GAAGT,IAAI,CAACU,KAAK,CAACF,QAAQ,CAAC;MAC3C;AACZ;AACA;AACA;MACY,IAAIG,iBAAiB,GAAGH,QAAQ,GAAG,GAAG;MACtC;AACZ;AACA;AACA;MACY,IAAI,CAACG,iBAAiB,IAAIH,QAAQ,IAAI,CAAC,EAAE;QACrCG,iBAAiB,GAAG,CAAC;MACzB;MACAA,iBAAiB,KAAK,CAAC,IAAIF,gBAAgB,EAAE;MAC7CA,gBAAgB,GAAGT,IAAI,CAACC,GAAG,CAACQ,gBAAgB,EAAE9C,MAAM,GAAG,CAAC,CAAC;MACzD;AACZ;AACA;MACY,MAAMiD,cAAc,GAAGC,OAAO,CAACJ,gBAAgB,GAAG,CAAC,CAAC;MACpD,IAAIG,cAAc,EAAE;QAChB,IAAI/C,UAAU,KAAK,SAAS,EAAE;UAC1B8C,iBAAiB,GAAG,CAAC,GAAGA,iBAAiB;UACzC,IAAI/C,WAAW,EAAE;YACb+C,iBAAiB,IAAI/C,WAAW,GAAG8B,gBAAgB;UACvD;QACJ,CAAC,MACI,IAAI7B,UAAU,KAAK,QAAQ,EAAE;UAC9B0C,cAAc,GAAGrB,iBAAiB;QACtC;MACJ;MACAoB,OAAO,GAAGzD,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE8D,iBAAiB,CAAC,GAAGjB,gBAAgB;IAC/D;IACA;AACR;AACA;AACA;AACA;IACQ,MAAMoB,KAAK,GAAGV,cAAc,GACtB;MAAEW,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAEvD,WAAW,CAAC,CAAC;IAAE,CAAC,GACtC8C,cAAc,CAACU,IAAI,CAACX,OAAO,CAAC;IAClC,IAAI1B,qBAAqB,EAAE;MACvBkC,KAAK,CAACE,KAAK,GAAGpC,qBAAqB,CAACkC,KAAK,CAACE,KAAK,CAAC;IACpD;IACA,IAAI;MAAED;IAAK,CAAC,GAAGD,KAAK;IACpB,IAAI,CAACV,cAAc,IAAIX,kBAAkB,KAAK,IAAI,EAAE;MAChDsB,IAAI,GAAG5C,KAAK,IAAI,CAAC,GAAG0B,WAAW,IAAID,aAAa,GAAGC,WAAW,IAAI,CAAC;IACvE;IACA,MAAMqB,mBAAmB,GAAG5B,QAAQ,KAAK,IAAI,KACxCD,SAAS,KAAK,UAAU,IAAKA,SAAS,KAAK,SAAS,IAAI0B,IAAK,CAAC;IACnE,IAAI9C,QAAQ,EAAE;MACVA,QAAQ,CAAC6C,KAAK,CAACE,KAAK,CAAC;IACzB;IACA,IAAIE,mBAAmB,EAAE;MACrBC,MAAM,CAAC,CAAC;IACZ;IACA,OAAOL,KAAK;EAChB,CAAC;EACD,MAAMM,mBAAmB,GAAGA,CAAA,KAAM;IAC9B1C,eAAe,IAAIA,eAAe,CAAC2C,IAAI,CAAC,CAAC;IACzC3C,eAAe,GAAG4C,SAAS;EAC/B,CAAC;EACD,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACjBlC,SAAS,GAAG,MAAM;IAClB+B,mBAAmB,CAAC,CAAC;IACrB/C,sBAAsB,CAAC,CAAC;IACxBE,qBAAqB,CAAC,CAAC;IACvBgB,SAAS,GAAGC,UAAU,GAAG,IAAI;EACjC,CAAC;EACD,MAAM2B,MAAM,GAAGA,CAAA,KAAM;IACjB9B,SAAS,GAAG,UAAU;IACtBrB,UAAU,IAAIA,UAAU,CAAC,CAAC;IAC1BoD,mBAAmB,CAAC,CAAC;IACrB/C,sBAAsB,CAAC,CAAC;EAC5B,CAAC;EACD,MAAMmD,IAAI,GAAGA,CAAA,KAAM;IACf,IAAIpD,UAAU,EACV;IACJ,IAAI,CAACM,eAAe,EAChBA,eAAe,GAAGlB,MAAM,CAACsC,IAAI,CAAC;IAClC,MAAM2B,GAAG,GAAG/C,eAAe,CAAC+C,GAAG,CAAC,CAAC;IACjC3D,MAAM,IAAIA,MAAM,CAAC,CAAC;IAClB,IAAIwB,QAAQ,KAAK,IAAI,EAAE;MACnBC,SAAS,GAAGkC,GAAG,GAAGnC,QAAQ;IAC9B,CAAC,MACI,IAAI,CAACC,SAAS,IAAIF,SAAS,KAAK,UAAU,EAAE;MAC7CE,SAAS,GAAGkC,GAAG;IACnB;IACA,IAAIpC,SAAS,KAAK,UAAU,EAAE;MAC1Bd,qBAAqB,CAAC,CAAC;IAC3B;IACAiB,UAAU,GAAGD,SAAS;IACtBD,QAAQ,GAAG,IAAI;IACf;AACR;AACA;AACA;IACQD,SAAS,GAAG,SAAS;IACrBX,eAAe,CAACgD,KAAK,CAAC,CAAC;EAC3B,CAAC;EACD,IAAIpE,QAAQ,EAAE;IACVkE,IAAI,CAAC,CAAC;EACV;EACA,MAAMG,QAAQ,GAAG;IACbC,IAAIA,CAACnD,OAAO,EAAEoD,MAAM,EAAE;MAClB,OAAOvD,sBAAsB,CAACsD,IAAI,CAACnD,OAAO,EAAEoD,MAAM,CAAC;IACvD,CAAC;IACD,IAAIC,IAAIA,CAAA,EAAG;MACP,OAAOhF,qBAAqB,CAAC+C,WAAW,CAAC;IAC7C,CAAC;IACD,IAAIiC,IAAIA,CAACC,OAAO,EAAE;MACdA,OAAO,GAAGhF,qBAAqB,CAACgF,OAAO,CAAC;MACxClC,WAAW,GAAGkC,OAAO;MACrB,IAAIzC,QAAQ,KAAK,IAAI,IAAI,CAACZ,eAAe,IAAIP,KAAK,KAAK,CAAC,EAAE;QACtDmB,QAAQ,GAAGyC,OAAO;MACtB,CAAC,MACI;QACDxC,SAAS,GAAGb,eAAe,CAAC+C,GAAG,CAAC,CAAC,GAAGM,OAAO,GAAG5D,KAAK;MACvD;IACJ,CAAC;IACD,IAAI6D,QAAQA,CAAA,EAAG;MACX,MAAMA,QAAQ,GAAG/C,SAAS,CAACQ,kBAAkB,KAAK,IAAI,GAChDzC,qBAAqB,CAACiC,SAAS,CAAC,GAChCA,SAAS,CAACQ,kBAAkB;MAClC,OAAO3C,qBAAqB,CAACkF,QAAQ,CAAC;IAC1C,CAAC;IACD,IAAI7D,KAAKA,CAAA,EAAG;MACR,OAAOA,KAAK;IAChB,CAAC;IACD,IAAIA,KAAKA,CAAC8D,QAAQ,EAAE;MAChB,IAAIA,QAAQ,KAAK9D,KAAK,IAAI,CAACO,eAAe,EACtC;MACJP,KAAK,GAAG8D,QAAQ;MAChBN,QAAQ,CAACG,IAAI,GAAGhF,qBAAqB,CAAC+C,WAAW,CAAC;IACtD,CAAC;IACD,IAAIiB,KAAKA,CAAA,EAAG;MACR,OAAOzB,SAAS;IACpB,CAAC;IACDmC,IAAI;IACJU,KAAK,EAAEA,CAAA,KAAM;MACT7C,SAAS,GAAG,QAAQ;MACpBC,QAAQ,GAAGO,WAAW;IAC1B,CAAC;IACDwB,IAAI,EAAEA,CAAA,KAAM;MACRjD,UAAU,GAAG,IAAI;MACjB,IAAIiB,SAAS,KAAK,MAAM,EACpB;MACJA,SAAS,GAAG,MAAM;MAClBtB,MAAM,IAAIA,MAAM,CAAC,CAAC;MAClBwD,MAAM,CAAC,CAAC;IACZ,CAAC;IACDA,MAAM,EAAEA,CAAA,KAAM;MACV,IAAI/B,UAAU,KAAK,IAAI,EACnBM,IAAI,CAACN,UAAU,CAAC;MACpB+B,MAAM,CAAC,CAAC;IACZ,CAAC;IACDY,QAAQ,EAAEA,CAAA,KAAM;MACZ9C,SAAS,GAAG,UAAU;IAC1B,CAAC;IACD+C,MAAM,EAAG9B,OAAO,IAAK;MACjBf,SAAS,GAAG,CAAC;MACb,OAAOO,IAAI,CAACQ,OAAO,CAAC;IACxB;EACJ,CAAC;EACD,OAAOqB,QAAQ;AACnB;AAEA,SAAStE,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}