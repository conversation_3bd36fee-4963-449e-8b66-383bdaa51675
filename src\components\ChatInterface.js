import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaPaperPlane, FaMicrophone, FaStop, FaRobot, FaUser } from 'react-icons/fa';
import { useChat } from '../context/ChatContext';
import ChatMessage from './ChatMessage';
import SuggestedQuestions from './SuggestedQuestions';

const ChatInterface = ({ conversationId, invoiceData }) => {
  const [inputMessage, setInputMessage] = useState('');
  const [isListening, setIsListening] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  
  const { 
    messages, 
    isLoading, 
    sendMessage, 
    setConversationId,
    setInvoiceData 
  } = useChat();

  // Update context when props change
  useEffect(() => {
    if (conversationId) {
      setConversationId(conversationId);
    }
  }, [conversationId, setConversationId]);

  useEffect(() => {
    if (invoiceData) {
      setInvoiceData(invoiceData);
    }
  }, [invoiceData, setInvoiceData]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (!inputMessage.trim() || isLoading) return;

    const message = inputMessage.trim();
    setInputMessage('');
    await sendMessage(message);
  };

  const handleSuggestedQuestion = async (question) => {
    await sendMessage(question);
  };

  const handleVoiceInput = () => {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      alert('Speech recognition is not supported in your browser');
      return;
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();

    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.lang = 'en-US';

    recognition.onstart = () => {
      setIsListening(true);
    };

    recognition.onresult = (event) => {
      const transcript = event.results[0][0].transcript;
      setInputMessage(transcript);
      setIsListening(false);
    };

    recognition.onerror = () => {
      setIsListening(false);
    };

    recognition.onend = () => {
      setIsListening(false);
    };

    if (isListening) {
      recognition.stop();
    } else {
      recognition.start();
    }
  };

  return (
    <div className="flex flex-col h-full max-h-[calc(100vh-200px)]">
      {/* Chat Header */}
      <motion.div
        className="glass-dark rounded-t-xl p-4 border-b border-dark-700"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center space-x-3">
          <motion.div
            className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center"
            animate={{ 
              boxShadow: [
                "0 0 20px rgba(14, 165, 233, 0.3)",
                "0 0 40px rgba(14, 165, 233, 0.6)",
                "0 0 20px rgba(14, 165, 233, 0.3)"
              ]
            }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <FaRobot className="text-white" />
          </motion.div>
          <div>
            <h3 className="font-semibold text-white">AI Assistant</h3>
            <p className="text-sm text-gray-400">
              {isLoading ? 'Thinking...' : 'Ready to help'}
            </p>
          </div>
        </div>
      </motion.div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-dark-900/50">
        <AnimatePresence>
          {messages.length === 0 ? (
            <motion.div
              className="text-center py-8"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <motion.div
                className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4"
                animate={{ 
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0]
                }}
                transition={{ duration: 4, repeat: Infinity }}
              >
                <FaRobot className="text-2xl text-white" />
              </motion.div>
              <h3 className="text-xl font-semibold text-white mb-2">
                Welcome! How can I help you today?
              </h3>
              <p className="text-gray-400 max-w-md mx-auto">
                I can help you with your bills, answer questions about market trends, 
                or assist with any general inquiries you might have.
              </p>
            </motion.div>
          ) : (
            messages.map((message) => (
              <ChatMessage key={message.id} message={message} />
            ))
          )}
        </AnimatePresence>

        {/* Loading indicator */}
        {isLoading && (
          <motion.div
            className="flex items-center space-x-3 p-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
              <FaRobot className="text-white text-sm" />
            </div>
            <div className="glass rounded-lg p-3">
              <div className="flex space-x-1">
                {[0, 1, 2].map((i) => (
                  <motion.div
                    key={i}
                    className="w-2 h-2 bg-primary-500 rounded-full"
                    animate={{
                      scale: [1, 1.5, 1],
                      opacity: [0.5, 1, 0.5]
                    }}
                    transition={{
                      duration: 1,
                      repeat: Infinity,
                      delay: i * 0.2
                    }}
                  />
                ))}
              </div>
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Suggested Questions */}
      {messages.length === 0 && (
        <SuggestedQuestions 
          onQuestionClick={handleSuggestedQuestion}
          hasInvoice={!!invoiceData}
        />
      )}

      {/* Input Area */}
      <motion.div
        className="glass-dark rounded-b-xl p-4 border-t border-dark-700"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <form onSubmit={handleSendMessage} className="flex items-center space-x-3">
          <div className="flex-1 relative">
            <input
              ref={inputRef}
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              placeholder="Ask me anything about your bills or general questions..."
              className="w-full input-primary pr-12"
              disabled={isLoading}
            />
            
            {/* Voice input button */}
            <motion.button
              type="button"
              onClick={handleVoiceInput}
              className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-2 rounded-lg transition-colors ${
                isListening 
                  ? 'text-red-400 bg-red-400/10' 
                  : 'text-gray-400 hover:text-white hover:bg-dark-700'
              }`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              disabled={isLoading}
            >
              {isListening ? <FaStop /> : <FaMicrophone />}
            </motion.button>
          </div>

          <motion.button
            type="submit"
            disabled={!inputMessage.trim() || isLoading}
            className="btn-primary p-3 disabled:opacity-50 disabled:cursor-not-allowed"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <FaPaperPlane />
          </motion.button>
        </form>

        {/* Voice status */}
        {isListening && (
          <motion.p
            className="text-center text-red-400 text-sm mt-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            🎤 Listening... Speak now
          </motion.p>
        )}
      </motion.div>
    </div>
  );
};

export default ChatInterface;
