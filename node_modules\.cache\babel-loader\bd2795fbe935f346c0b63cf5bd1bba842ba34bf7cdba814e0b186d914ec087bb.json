{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Chunk} Chunk\n * @typedef {import('micromark-util-types').Event} Event\n * @typedef {import('micromark-util-types').Token} Token\n */\n\nimport { splice } from 'micromark-util-chunked';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\n\n/**\n * Tokenize subcontent.\n *\n * @param {Array<Event>} events\n *   List of events.\n * @returns {boolean}\n *   Whether subtokens were found.\n */\nexport function subtokenize(events) {\n  /** @type {Record<string, number>} */\n  const jumps = {};\n  let index = -1;\n  /** @type {Event} */\n  let event;\n  /** @type {number | undefined} */\n  let lineIndex;\n  /** @type {number} */\n  let otherIndex;\n  /** @type {Event} */\n  let otherEvent;\n  /** @type {Array<Event>} */\n  let parameters;\n  /** @type {Array<Event>} */\n  let subevents;\n  /** @type {boolean | undefined} */\n  let more;\n  while (++index < events.length) {\n    while (index in jumps) {\n      index = jumps[index];\n    }\n    event = events[index];\n\n    // Add a hook for the GFM tasklist extension, which needs to know if text\n    // is in the first content of a list item.\n    if (index && event[1].type === types.chunkFlow && events[index - 1][1].type === types.listItemPrefix) {\n      assert(event[1]._tokenizer, 'expected `_tokenizer` on subtokens');\n      subevents = event[1]._tokenizer.events;\n      otherIndex = 0;\n      if (otherIndex < subevents.length && subevents[otherIndex][1].type === types.lineEndingBlank) {\n        otherIndex += 2;\n      }\n      if (otherIndex < subevents.length && subevents[otherIndex][1].type === types.content) {\n        while (++otherIndex < subevents.length) {\n          if (subevents[otherIndex][1].type === types.content) {\n            break;\n          }\n          if (subevents[otherIndex][1].type === types.chunkText) {\n            subevents[otherIndex][1]._isInFirstContentOfListItem = true;\n            otherIndex++;\n          }\n        }\n      }\n    }\n\n    // Enter.\n    if (event[0] === 'enter') {\n      if (event[1].contentType) {\n        Object.assign(jumps, subcontent(events, index));\n        index = jumps[index];\n        more = true;\n      }\n    }\n    // Exit.\n    else if (event[1]._container) {\n      otherIndex = index;\n      lineIndex = undefined;\n      while (otherIndex--) {\n        otherEvent = events[otherIndex];\n        if (otherEvent[1].type === types.lineEnding || otherEvent[1].type === types.lineEndingBlank) {\n          if (otherEvent[0] === 'enter') {\n            if (lineIndex) {\n              events[lineIndex][1].type = types.lineEndingBlank;\n            }\n            otherEvent[1].type = types.lineEnding;\n            lineIndex = otherIndex;\n          }\n        } else {\n          break;\n        }\n      }\n      if (lineIndex) {\n        // Fix position.\n        event[1].end = Object.assign({}, events[lineIndex][1].start);\n\n        // Switch container exit w/ line endings.\n        parameters = events.slice(lineIndex, index);\n        parameters.unshift(event);\n        splice(events, lineIndex, index - lineIndex + 1, parameters);\n      }\n    }\n  }\n  return !more;\n}\n\n/**\n * Tokenize embedded tokens.\n *\n * @param {Array<Event>} events\n * @param {number} eventIndex\n * @returns {Record<string, number>}\n */\nfunction subcontent(events, eventIndex) {\n  const token = events[eventIndex][1];\n  const context = events[eventIndex][2];\n  let startPosition = eventIndex - 1;\n  /** @type {Array<number>} */\n  const startPositions = [];\n  assert(token.contentType, 'expected `contentType` on subtokens');\n  const tokenizer = token._tokenizer || context.parser[token.contentType](token.start);\n  const childEvents = tokenizer.events;\n  /** @type {Array<[number, number]>} */\n  const jumps = [];\n  /** @type {Record<string, number>} */\n  const gaps = {};\n  /** @type {Array<Chunk>} */\n  let stream;\n  /** @type {Token | undefined} */\n  let previous;\n  let index = -1;\n  /** @type {Token | undefined} */\n  let current = token;\n  let adjust = 0;\n  let start = 0;\n  const breaks = [start];\n\n  // Loop forward through the linked tokens to pass them in order to the\n  // subtokenizer.\n  while (current) {\n    // Find the position of the event for this token.\n    while (events[++startPosition][1] !== current) {\n      // Empty.\n    }\n    assert(!previous || current.previous === previous, 'expected previous to match');\n    assert(!previous || previous.next === current, 'expected next to match');\n    startPositions.push(startPosition);\n    if (!current._tokenizer) {\n      stream = context.sliceStream(current);\n      if (!current.next) {\n        stream.push(codes.eof);\n      }\n      if (previous) {\n        tokenizer.defineSkip(current.start);\n      }\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = true;\n      }\n      tokenizer.write(stream);\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = undefined;\n      }\n    }\n\n    // Unravel the next token.\n    previous = current;\n    current = current.next;\n  }\n\n  // Now, loop back through all events (and linked tokens), to figure out which\n  // parts belong where.\n  current = token;\n  while (++index < childEvents.length) {\n    if (\n    // Find a void token that includes a break.\n    childEvents[index][0] === 'exit' && childEvents[index - 1][0] === 'enter' && childEvents[index][1].type === childEvents[index - 1][1].type && childEvents[index][1].start.line !== childEvents[index][1].end.line) {\n      assert(current, 'expected a current token');\n      start = index + 1;\n      breaks.push(start);\n      // Help GC.\n      current._tokenizer = undefined;\n      current.previous = undefined;\n      current = current.next;\n    }\n  }\n\n  // Help GC.\n  tokenizer.events = [];\n\n  // If there’s one more token (which is the cases for lines that end in an\n  // EOF), that’s perfect: the last point we found starts it.\n  // If there isn’t then make sure any remaining content is added to it.\n  if (current) {\n    // Help GC.\n    current._tokenizer = undefined;\n    current.previous = undefined;\n    assert(!current.next, 'expected no next token');\n  } else {\n    breaks.pop();\n  }\n\n  // Now splice the events from the subtokenizer into the current events,\n  // moving back to front so that splice indices aren’t affected.\n  index = breaks.length;\n  while (index--) {\n    const slice = childEvents.slice(breaks[index], breaks[index + 1]);\n    const start = startPositions.pop();\n    assert(start !== undefined, 'expected a start position when splicing');\n    jumps.unshift([start, start + slice.length - 1]);\n    splice(events, start, 2, slice);\n  }\n  index = -1;\n  while (++index < jumps.length) {\n    gaps[adjust + jumps[index][0]] = adjust + jumps[index][1];\n    adjust += jumps[index][1] - jumps[index][0] - 1;\n  }\n  return gaps;\n}", "map": {"version": 3, "names": ["splice", "codes", "types", "ok", "assert", "subtokenize", "events", "jumps", "index", "event", "lineIndex", "otherIndex", "otherEvent", "parameters", "subevents", "more", "length", "type", "chunkFlow", "listItemPrefix", "_tokenizer", "lineEndingBlank", "content", "chunkText", "_isInFirstContentOfListItem", "contentType", "Object", "assign", "subcontent", "_container", "undefined", "lineEnding", "end", "start", "slice", "unshift", "eventIndex", "token", "context", "startPosition", "startPositions", "tokenizer", "parser", "childEvents", "gaps", "stream", "previous", "current", "adjust", "breaks", "next", "push", "sliceStream", "eof", "defineSkip", "_gfmTasklistFirstContentOfListItem", "write", "line", "pop"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/micromark-util-subtokenize/dev/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Chunk} Chunk\n * @typedef {import('micromark-util-types').Event} Event\n * @typedef {import('micromark-util-types').Token} Token\n */\n\nimport {splice} from 'micromark-util-chunked'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/**\n * Tokenize subcontent.\n *\n * @param {Array<Event>} events\n *   List of events.\n * @returns {boolean}\n *   Whether subtokens were found.\n */\nexport function subtokenize(events) {\n  /** @type {Record<string, number>} */\n  const jumps = {}\n  let index = -1\n  /** @type {Event} */\n  let event\n  /** @type {number | undefined} */\n  let lineIndex\n  /** @type {number} */\n  let otherIndex\n  /** @type {Event} */\n  let otherEvent\n  /** @type {Array<Event>} */\n  let parameters\n  /** @type {Array<Event>} */\n  let subevents\n  /** @type {boolean | undefined} */\n  let more\n\n  while (++index < events.length) {\n    while (index in jumps) {\n      index = jumps[index]\n    }\n\n    event = events[index]\n\n    // Add a hook for the GFM tasklist extension, which needs to know if text\n    // is in the first content of a list item.\n    if (\n      index &&\n      event[1].type === types.chunkFlow &&\n      events[index - 1][1].type === types.listItemPrefix\n    ) {\n      assert(event[1]._tokenizer, 'expected `_tokenizer` on subtokens')\n      subevents = event[1]._tokenizer.events\n      otherIndex = 0\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === types.lineEndingBlank\n      ) {\n        otherIndex += 2\n      }\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === types.content\n      ) {\n        while (++otherIndex < subevents.length) {\n          if (subevents[otherIndex][1].type === types.content) {\n            break\n          }\n\n          if (subevents[otherIndex][1].type === types.chunkText) {\n            subevents[otherIndex][1]._isInFirstContentOfListItem = true\n            otherIndex++\n          }\n        }\n      }\n    }\n\n    // Enter.\n    if (event[0] === 'enter') {\n      if (event[1].contentType) {\n        Object.assign(jumps, subcontent(events, index))\n        index = jumps[index]\n        more = true\n      }\n    }\n    // Exit.\n    else if (event[1]._container) {\n      otherIndex = index\n      lineIndex = undefined\n\n      while (otherIndex--) {\n        otherEvent = events[otherIndex]\n\n        if (\n          otherEvent[1].type === types.lineEnding ||\n          otherEvent[1].type === types.lineEndingBlank\n        ) {\n          if (otherEvent[0] === 'enter') {\n            if (lineIndex) {\n              events[lineIndex][1].type = types.lineEndingBlank\n            }\n\n            otherEvent[1].type = types.lineEnding\n            lineIndex = otherIndex\n          }\n        } else {\n          break\n        }\n      }\n\n      if (lineIndex) {\n        // Fix position.\n        event[1].end = Object.assign({}, events[lineIndex][1].start)\n\n        // Switch container exit w/ line endings.\n        parameters = events.slice(lineIndex, index)\n        parameters.unshift(event)\n        splice(events, lineIndex, index - lineIndex + 1, parameters)\n      }\n    }\n  }\n\n  return !more\n}\n\n/**\n * Tokenize embedded tokens.\n *\n * @param {Array<Event>} events\n * @param {number} eventIndex\n * @returns {Record<string, number>}\n */\nfunction subcontent(events, eventIndex) {\n  const token = events[eventIndex][1]\n  const context = events[eventIndex][2]\n  let startPosition = eventIndex - 1\n  /** @type {Array<number>} */\n  const startPositions = []\n  assert(token.contentType, 'expected `contentType` on subtokens')\n  const tokenizer =\n    token._tokenizer || context.parser[token.contentType](token.start)\n  const childEvents = tokenizer.events\n  /** @type {Array<[number, number]>} */\n  const jumps = []\n  /** @type {Record<string, number>} */\n  const gaps = {}\n  /** @type {Array<Chunk>} */\n  let stream\n  /** @type {Token | undefined} */\n  let previous\n  let index = -1\n  /** @type {Token | undefined} */\n  let current = token\n  let adjust = 0\n  let start = 0\n  const breaks = [start]\n\n  // Loop forward through the linked tokens to pass them in order to the\n  // subtokenizer.\n  while (current) {\n    // Find the position of the event for this token.\n    while (events[++startPosition][1] !== current) {\n      // Empty.\n    }\n\n    assert(\n      !previous || current.previous === previous,\n      'expected previous to match'\n    )\n    assert(!previous || previous.next === current, 'expected next to match')\n\n    startPositions.push(startPosition)\n\n    if (!current._tokenizer) {\n      stream = context.sliceStream(current)\n\n      if (!current.next) {\n        stream.push(codes.eof)\n      }\n\n      if (previous) {\n        tokenizer.defineSkip(current.start)\n      }\n\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = true\n      }\n\n      tokenizer.write(stream)\n\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = undefined\n      }\n    }\n\n    // Unravel the next token.\n    previous = current\n    current = current.next\n  }\n\n  // Now, loop back through all events (and linked tokens), to figure out which\n  // parts belong where.\n  current = token\n\n  while (++index < childEvents.length) {\n    if (\n      // Find a void token that includes a break.\n      childEvents[index][0] === 'exit' &&\n      childEvents[index - 1][0] === 'enter' &&\n      childEvents[index][1].type === childEvents[index - 1][1].type &&\n      childEvents[index][1].start.line !== childEvents[index][1].end.line\n    ) {\n      assert(current, 'expected a current token')\n      start = index + 1\n      breaks.push(start)\n      // Help GC.\n      current._tokenizer = undefined\n      current.previous = undefined\n      current = current.next\n    }\n  }\n\n  // Help GC.\n  tokenizer.events = []\n\n  // If there’s one more token (which is the cases for lines that end in an\n  // EOF), that’s perfect: the last point we found starts it.\n  // If there isn’t then make sure any remaining content is added to it.\n  if (current) {\n    // Help GC.\n    current._tokenizer = undefined\n    current.previous = undefined\n    assert(!current.next, 'expected no next token')\n  } else {\n    breaks.pop()\n  }\n\n  // Now splice the events from the subtokenizer into the current events,\n  // moving back to front so that splice indices aren’t affected.\n  index = breaks.length\n\n  while (index--) {\n    const slice = childEvents.slice(breaks[index], breaks[index + 1])\n    const start = startPositions.pop()\n    assert(start !== undefined, 'expected a start position when splicing')\n    jumps.unshift([start, start + slice.length - 1])\n    splice(events, start, 2, slice)\n  }\n\n  index = -1\n\n  while (++index < jumps.length) {\n    gaps[adjust + jumps[index][0]] = adjust + jumps[index][1]\n    adjust += jumps[index][1] - jumps[index][0] - 1\n  }\n\n  return gaps\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,MAAM,QAAO,wBAAwB;AAC7C,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,MAAM,EAAE;EAClC;EACA,MAAMC,KAAK,GAAG,CAAC,CAAC;EAChB,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,KAAK;EACT;EACA,IAAIC,SAAS;EACb;EACA,IAAIC,UAAU;EACd;EACA,IAAIC,UAAU;EACd;EACA,IAAIC,UAAU;EACd;EACA,IAAIC,SAAS;EACb;EACA,IAAIC,IAAI;EAER,OAAO,EAAEP,KAAK,GAAGF,MAAM,CAACU,MAAM,EAAE;IAC9B,OAAOR,KAAK,IAAID,KAAK,EAAE;MACrBC,KAAK,GAAGD,KAAK,CAACC,KAAK,CAAC;IACtB;IAEAC,KAAK,GAAGH,MAAM,CAACE,KAAK,CAAC;;IAErB;IACA;IACA,IACEA,KAAK,IACLC,KAAK,CAAC,CAAC,CAAC,CAACQ,IAAI,KAAKf,KAAK,CAACgB,SAAS,IACjCZ,MAAM,CAACE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACS,IAAI,KAAKf,KAAK,CAACiB,cAAc,EAClD;MACAf,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC,CAACW,UAAU,EAAE,oCAAoC,CAAC;MACjEN,SAAS,GAAGL,KAAK,CAAC,CAAC,CAAC,CAACW,UAAU,CAACd,MAAM;MACtCK,UAAU,GAAG,CAAC;MAEd,IACEA,UAAU,GAAGG,SAAS,CAACE,MAAM,IAC7BF,SAAS,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,KAAKf,KAAK,CAACmB,eAAe,EACvD;QACAV,UAAU,IAAI,CAAC;MACjB;MAEA,IACEA,UAAU,GAAGG,SAAS,CAACE,MAAM,IAC7BF,SAAS,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,KAAKf,KAAK,CAACoB,OAAO,EAC/C;QACA,OAAO,EAAEX,UAAU,GAAGG,SAAS,CAACE,MAAM,EAAE;UACtC,IAAIF,SAAS,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,KAAKf,KAAK,CAACoB,OAAO,EAAE;YACnD;UACF;UAEA,IAAIR,SAAS,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,KAAKf,KAAK,CAACqB,SAAS,EAAE;YACrDT,SAAS,CAACH,UAAU,CAAC,CAAC,CAAC,CAAC,CAACa,2BAA2B,GAAG,IAAI;YAC3Db,UAAU,EAAE;UACd;QACF;MACF;IACF;;IAEA;IACA,IAAIF,KAAK,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;MACxB,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACgB,WAAW,EAAE;QACxBC,MAAM,CAACC,MAAM,CAACpB,KAAK,EAAEqB,UAAU,CAACtB,MAAM,EAAEE,KAAK,CAAC,CAAC;QAC/CA,KAAK,GAAGD,KAAK,CAACC,KAAK,CAAC;QACpBO,IAAI,GAAG,IAAI;MACb;IACF;IACA;IAAA,KACK,IAAIN,KAAK,CAAC,CAAC,CAAC,CAACoB,UAAU,EAAE;MAC5BlB,UAAU,GAAGH,KAAK;MAClBE,SAAS,GAAGoB,SAAS;MAErB,OAAOnB,UAAU,EAAE,EAAE;QACnBC,UAAU,GAAGN,MAAM,CAACK,UAAU,CAAC;QAE/B,IACEC,UAAU,CAAC,CAAC,CAAC,CAACK,IAAI,KAAKf,KAAK,CAAC6B,UAAU,IACvCnB,UAAU,CAAC,CAAC,CAAC,CAACK,IAAI,KAAKf,KAAK,CAACmB,eAAe,EAC5C;UACA,IAAIT,UAAU,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;YAC7B,IAAIF,SAAS,EAAE;cACbJ,MAAM,CAACI,SAAS,CAAC,CAAC,CAAC,CAAC,CAACO,IAAI,GAAGf,KAAK,CAACmB,eAAe;YACnD;YAEAT,UAAU,CAAC,CAAC,CAAC,CAACK,IAAI,GAAGf,KAAK,CAAC6B,UAAU;YACrCrB,SAAS,GAAGC,UAAU;UACxB;QACF,CAAC,MAAM;UACL;QACF;MACF;MAEA,IAAID,SAAS,EAAE;QACb;QACAD,KAAK,CAAC,CAAC,CAAC,CAACuB,GAAG,GAAGN,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAErB,MAAM,CAACI,SAAS,CAAC,CAAC,CAAC,CAAC,CAACuB,KAAK,CAAC;;QAE5D;QACApB,UAAU,GAAGP,MAAM,CAAC4B,KAAK,CAACxB,SAAS,EAAEF,KAAK,CAAC;QAC3CK,UAAU,CAACsB,OAAO,CAAC1B,KAAK,CAAC;QACzBT,MAAM,CAACM,MAAM,EAAEI,SAAS,EAAEF,KAAK,GAAGE,SAAS,GAAG,CAAC,EAAEG,UAAU,CAAC;MAC9D;IACF;EACF;EAEA,OAAO,CAACE,IAAI;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,UAAUA,CAACtB,MAAM,EAAE8B,UAAU,EAAE;EACtC,MAAMC,KAAK,GAAG/B,MAAM,CAAC8B,UAAU,CAAC,CAAC,CAAC,CAAC;EACnC,MAAME,OAAO,GAAGhC,MAAM,CAAC8B,UAAU,CAAC,CAAC,CAAC,CAAC;EACrC,IAAIG,aAAa,GAAGH,UAAU,GAAG,CAAC;EAClC;EACA,MAAMI,cAAc,GAAG,EAAE;EACzBpC,MAAM,CAACiC,KAAK,CAACZ,WAAW,EAAE,qCAAqC,CAAC;EAChE,MAAMgB,SAAS,GACbJ,KAAK,CAACjB,UAAU,IAAIkB,OAAO,CAACI,MAAM,CAACL,KAAK,CAACZ,WAAW,CAAC,CAACY,KAAK,CAACJ,KAAK,CAAC;EACpE,MAAMU,WAAW,GAAGF,SAAS,CAACnC,MAAM;EACpC;EACA,MAAMC,KAAK,GAAG,EAAE;EAChB;EACA,MAAMqC,IAAI,GAAG,CAAC,CAAC;EACf;EACA,IAAIC,MAAM;EACV;EACA,IAAIC,QAAQ;EACZ,IAAItC,KAAK,GAAG,CAAC,CAAC;EACd;EACA,IAAIuC,OAAO,GAAGV,KAAK;EACnB,IAAIW,MAAM,GAAG,CAAC;EACd,IAAIf,KAAK,GAAG,CAAC;EACb,MAAMgB,MAAM,GAAG,CAAChB,KAAK,CAAC;;EAEtB;EACA;EACA,OAAOc,OAAO,EAAE;IACd;IACA,OAAOzC,MAAM,CAAC,EAAEiC,aAAa,CAAC,CAAC,CAAC,CAAC,KAAKQ,OAAO,EAAE;MAC7C;IAAA;IAGF3C,MAAM,CACJ,CAAC0C,QAAQ,IAAIC,OAAO,CAACD,QAAQ,KAAKA,QAAQ,EAC1C,4BACF,CAAC;IACD1C,MAAM,CAAC,CAAC0C,QAAQ,IAAIA,QAAQ,CAACI,IAAI,KAAKH,OAAO,EAAE,wBAAwB,CAAC;IAExEP,cAAc,CAACW,IAAI,CAACZ,aAAa,CAAC;IAElC,IAAI,CAACQ,OAAO,CAAC3B,UAAU,EAAE;MACvByB,MAAM,GAAGP,OAAO,CAACc,WAAW,CAACL,OAAO,CAAC;MAErC,IAAI,CAACA,OAAO,CAACG,IAAI,EAAE;QACjBL,MAAM,CAACM,IAAI,CAAClD,KAAK,CAACoD,GAAG,CAAC;MACxB;MAEA,IAAIP,QAAQ,EAAE;QACZL,SAAS,CAACa,UAAU,CAACP,OAAO,CAACd,KAAK,CAAC;MACrC;MAEA,IAAIc,OAAO,CAACvB,2BAA2B,EAAE;QACvCiB,SAAS,CAACc,kCAAkC,GAAG,IAAI;MACrD;MAEAd,SAAS,CAACe,KAAK,CAACX,MAAM,CAAC;MAEvB,IAAIE,OAAO,CAACvB,2BAA2B,EAAE;QACvCiB,SAAS,CAACc,kCAAkC,GAAGzB,SAAS;MAC1D;IACF;;IAEA;IACAgB,QAAQ,GAAGC,OAAO;IAClBA,OAAO,GAAGA,OAAO,CAACG,IAAI;EACxB;;EAEA;EACA;EACAH,OAAO,GAAGV,KAAK;EAEf,OAAO,EAAE7B,KAAK,GAAGmC,WAAW,CAAC3B,MAAM,EAAE;IACnC;IACE;IACA2B,WAAW,CAACnC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,IAChCmC,WAAW,CAACnC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,IACrCmC,WAAW,CAACnC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACS,IAAI,KAAK0B,WAAW,CAACnC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACS,IAAI,IAC7D0B,WAAW,CAACnC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACyB,KAAK,CAACwB,IAAI,KAAKd,WAAW,CAACnC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACwB,GAAG,CAACyB,IAAI,EACnE;MACArD,MAAM,CAAC2C,OAAO,EAAE,0BAA0B,CAAC;MAC3Cd,KAAK,GAAGzB,KAAK,GAAG,CAAC;MACjByC,MAAM,CAACE,IAAI,CAAClB,KAAK,CAAC;MAClB;MACAc,OAAO,CAAC3B,UAAU,GAAGU,SAAS;MAC9BiB,OAAO,CAACD,QAAQ,GAAGhB,SAAS;MAC5BiB,OAAO,GAAGA,OAAO,CAACG,IAAI;IACxB;EACF;;EAEA;EACAT,SAAS,CAACnC,MAAM,GAAG,EAAE;;EAErB;EACA;EACA;EACA,IAAIyC,OAAO,EAAE;IACX;IACAA,OAAO,CAAC3B,UAAU,GAAGU,SAAS;IAC9BiB,OAAO,CAACD,QAAQ,GAAGhB,SAAS;IAC5B1B,MAAM,CAAC,CAAC2C,OAAO,CAACG,IAAI,EAAE,wBAAwB,CAAC;EACjD,CAAC,MAAM;IACLD,MAAM,CAACS,GAAG,CAAC,CAAC;EACd;;EAEA;EACA;EACAlD,KAAK,GAAGyC,MAAM,CAACjC,MAAM;EAErB,OAAOR,KAAK,EAAE,EAAE;IACd,MAAM0B,KAAK,GAAGS,WAAW,CAACT,KAAK,CAACe,MAAM,CAACzC,KAAK,CAAC,EAAEyC,MAAM,CAACzC,KAAK,GAAG,CAAC,CAAC,CAAC;IACjE,MAAMyB,KAAK,GAAGO,cAAc,CAACkB,GAAG,CAAC,CAAC;IAClCtD,MAAM,CAAC6B,KAAK,KAAKH,SAAS,EAAE,yCAAyC,CAAC;IACtEvB,KAAK,CAAC4B,OAAO,CAAC,CAACF,KAAK,EAAEA,KAAK,GAAGC,KAAK,CAAClB,MAAM,GAAG,CAAC,CAAC,CAAC;IAChDhB,MAAM,CAACM,MAAM,EAAE2B,KAAK,EAAE,CAAC,EAAEC,KAAK,CAAC;EACjC;EAEA1B,KAAK,GAAG,CAAC,CAAC;EAEV,OAAO,EAAEA,KAAK,GAAGD,KAAK,CAACS,MAAM,EAAE;IAC7B4B,IAAI,CAACI,MAAM,GAAGzC,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGwC,MAAM,GAAGzC,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IACzDwC,MAAM,IAAIzC,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGD,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EACjD;EAEA,OAAOoC,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}