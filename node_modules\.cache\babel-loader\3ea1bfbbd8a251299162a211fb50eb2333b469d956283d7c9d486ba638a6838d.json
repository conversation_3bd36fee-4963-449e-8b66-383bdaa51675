{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoice-extracting-agent\\\\src\\\\components\\\\ChatMessage.js\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aExclamation<PERSON>riangle } from 'react-icons/fa';\nimport ReactMarkdown from 'react-markdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatMessage = ({\n  message\n}) => {\n  const isUser = message.sender === 'user';\n  const isError = message.isError;\n  const messageVariants = {\n    initial: {\n      opacity: 0,\n      y: 20,\n      scale: 0.95\n    },\n    animate: {\n      opacity: 1,\n      y: 0,\n      scale: 1\n    },\n    exit: {\n      opacity: 0,\n      y: -20,\n      scale: 0.95\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: `flex items-start space-x-3 ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`,\n    variants: messageVariants,\n    initial: \"initial\",\n    animate: \"animate\",\n    exit: \"exit\",\n    transition: {\n      duration: 0.3\n    },\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      className: `w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${isUser ? 'bg-gradient-to-br from-primary-500 to-primary-600' : isError ? 'bg-gradient-to-br from-red-500 to-red-600' : 'bg-gradient-to-br from-purple-500 to-purple-600'}`,\n      whileHover: {\n        scale: 1.1\n      },\n      transition: {\n        type: \"spring\",\n        stiffness: 400,\n        damping: 10\n      },\n      children: isUser ? /*#__PURE__*/_jsxDEV(FaUser, {\n        className: \"text-white text-sm\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }, this) : isError ? /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n        className: \"text-white text-sm\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(FaRobot, {\n        className: \"text-white text-sm\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `flex-1 max-w-[80%] ${isUser ? 'text-right' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: `inline-block p-4 rounded-2xl ${isUser ? 'bg-gradient-to-br from-primary-500 to-primary-600 text-white' : isError ? 'bg-red-500/10 border border-red-500/20 text-red-300' : 'glass text-gray-100'} ${isUser ? 'rounded-br-md' : 'rounded-bl-md'}`,\n        whileHover: {\n          scale: 1.02\n        },\n        transition: {\n          type: \"spring\",\n          stiffness: 400,\n          damping: 10\n        },\n        children: isUser ? /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"whitespace-pre-wrap\",\n          children: message.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"prose prose-invert prose-sm max-w-none\",\n          children: /*#__PURE__*/_jsxDEV(ReactMarkdown, {\n            children: message.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), message.action && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: `mt-2 p-3 rounded-lg border ${message.action.success ? 'bg-green-500/10 border-green-500/20 text-green-300' : 'bg-red-500/10 border-red-500/20 text-red-300'}`,\n        initial: {\n          opacity: 0,\n          scale: 0.95\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        transition: {\n          delay: 0.2\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [message.action.success ? /*#__PURE__*/_jsxDEV(FaCheck, {\n            className: \"text-green-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(FaExclamationTriangle, {\n            className: \"text-red-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: message.action.type.replace('_', ' ').toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-sm\",\n          children: message.action.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this), message.action.referenceId && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-1 text-xs opacity-75\",\n          children: [\"Reference: \", message.action.referenceId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n        className: `text-xs text-gray-500 mt-2 ${isUser ? 'text-right' : ''}`,\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        transition: {\n          delay: 0.3\n        },\n        children: new Date(message.timestamp).toLocaleTimeString()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_c = ChatMessage;\nexport default ChatMessage;\nvar _c;\n$RefreshReg$(_c, \"ChatMessage\");", "map": {"version": 3, "names": ["React", "motion", "FaUser", "FaRobot", "FaCheck", "FaExclamationTriangle", "ReactMarkdown", "jsxDEV", "_jsxDEV", "ChatMessage", "message", "isUser", "sender", "isError", "messageVariants", "initial", "opacity", "y", "scale", "animate", "exit", "div", "className", "variants", "transition", "duration", "children", "whileHover", "type", "stiffness", "damping", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "action", "success", "delay", "replace", "toUpperCase", "referenceId", "p", "Date", "timestamp", "toLocaleTimeString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/src/components/ChatMessage.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaExclamationTriangle } from 'react-icons/fa';\nimport ReactMarkdown from 'react-markdown';\n\nconst ChatMessage = ({ message }) => {\n  const isUser = message.sender === 'user';\n  const isError = message.isError;\n\n  const messageVariants = {\n    initial: { opacity: 0, y: 20, scale: 0.95 },\n    animate: { opacity: 1, y: 0, scale: 1 },\n    exit: { opacity: 0, y: -20, scale: 0.95 }\n  };\n\n  return (\n    <motion.div\n      className={`flex items-start space-x-3 ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`}\n      variants={messageVariants}\n      initial=\"initial\"\n      animate=\"animate\"\n      exit=\"exit\"\n      transition={{ duration: 0.3 }}\n    >\n      {/* Avatar */}\n      <motion.div\n        className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${\n          isUser \n            ? 'bg-gradient-to-br from-primary-500 to-primary-600' \n            : isError\n            ? 'bg-gradient-to-br from-red-500 to-red-600'\n            : 'bg-gradient-to-br from-purple-500 to-purple-600'\n        }`}\n        whileHover={{ scale: 1.1 }}\n        transition={{ type: \"spring\", stiffness: 400, damping: 10 }}\n      >\n        {isUser ? (\n          <FaUser className=\"text-white text-sm\" />\n        ) : isError ? (\n          <FaExclamationTriangle className=\"text-white text-sm\" />\n        ) : (\n          <FaRobot className=\"text-white text-sm\" />\n        )}\n      </motion.div>\n\n      {/* Message Content */}\n      <div className={`flex-1 max-w-[80%] ${isUser ? 'text-right' : ''}`}>\n        <motion.div\n          className={`inline-block p-4 rounded-2xl ${\n            isUser\n              ? 'bg-gradient-to-br from-primary-500 to-primary-600 text-white'\n              : isError\n              ? 'bg-red-500/10 border border-red-500/20 text-red-300'\n              : 'glass text-gray-100'\n          } ${isUser ? 'rounded-br-md' : 'rounded-bl-md'}`}\n          whileHover={{ scale: 1.02 }}\n          transition={{ type: \"spring\", stiffness: 400, damping: 10 }}\n        >\n          {isUser ? (\n            <p className=\"whitespace-pre-wrap\">{message.content}</p>\n          ) : (\n            <div className=\"prose prose-invert prose-sm max-w-none\">\n              <ReactMarkdown>{message.content}</ReactMarkdown>\n            </div>\n          )}\n        </motion.div>\n\n        {/* Action Result */}\n        {message.action && (\n          <motion.div\n            className={`mt-2 p-3 rounded-lg border ${\n              message.action.success\n                ? 'bg-green-500/10 border-green-500/20 text-green-300'\n                : 'bg-red-500/10 border-red-500/20 text-red-300'\n            }`}\n            initial={{ opacity: 0, scale: 0.95 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2 }}\n          >\n            <div className=\"flex items-center space-x-2\">\n              {message.action.success ? (\n                <FaCheck className=\"text-green-400\" />\n              ) : (\n                <FaExclamationTriangle className=\"text-red-400\" />\n              )}\n              <span className=\"font-medium\">\n                {message.action.type.replace('_', ' ').toUpperCase()}\n              </span>\n            </div>\n            <p className=\"mt-1 text-sm\">{message.action.message}</p>\n            {message.action.referenceId && (\n              <p className=\"mt-1 text-xs opacity-75\">\n                Reference: {message.action.referenceId}\n              </p>\n            )}\n          </motion.div>\n        )}\n\n        {/* Timestamp */}\n        <motion.p\n          className={`text-xs text-gray-500 mt-2 ${isUser ? 'text-right' : ''}`}\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.3 }}\n        >\n          {new Date(message.timestamp).toLocaleTimeString()}\n        </motion.p>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default ChatMessage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,qBAAqB,QAAQ,gBAAgB;AAChF,OAAOC,aAAa,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EACnC,MAAMC,MAAM,GAAGD,OAAO,CAACE,MAAM,KAAK,MAAM;EACxC,MAAMC,OAAO,GAAGH,OAAO,CAACG,OAAO;EAE/B,MAAMC,eAAe,GAAG;IACtBC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAK,CAAC;IAC3CC,OAAO,EAAE;MAAEH,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAC;IACvCE,IAAI,EAAE;MAAEJ,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC,EAAE;MAAEC,KAAK,EAAE;IAAK;EAC1C,CAAC;EAED,oBACEV,OAAA,CAACP,MAAM,CAACoB,GAAG;IACTC,SAAS,EAAE,8BAA8BX,MAAM,GAAG,kCAAkC,GAAG,EAAE,EAAG;IAC5FY,QAAQ,EAAET,eAAgB;IAC1BC,OAAO,EAAC,SAAS;IACjBI,OAAO,EAAC,SAAS;IACjBC,IAAI,EAAC,MAAM;IACXI,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,gBAG9BlB,OAAA,CAACP,MAAM,CAACoB,GAAG;MACTC,SAAS,EAAE,uEACTX,MAAM,GACF,mDAAmD,GACnDE,OAAO,GACP,2CAA2C,GAC3C,iDAAiD,EACpD;MACHc,UAAU,EAAE;QAAET,KAAK,EAAE;MAAI,CAAE;MAC3BM,UAAU,EAAE;QAAEI,IAAI,EAAE,QAAQ;QAAEC,SAAS,EAAE,GAAG;QAAEC,OAAO,EAAE;MAAG,CAAE;MAAAJ,QAAA,EAE3Df,MAAM,gBACLH,OAAA,CAACN,MAAM;QAACoB,SAAS,EAAC;MAAoB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GACvCrB,OAAO,gBACTL,OAAA,CAACH,qBAAqB;QAACiB,SAAS,EAAC;MAAoB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAExD1B,OAAA,CAACL,OAAO;QAACmB,SAAS,EAAC;MAAoB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAC1C;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,eAGb1B,OAAA;MAAKc,SAAS,EAAE,sBAAsBX,MAAM,GAAG,YAAY,GAAG,EAAE,EAAG;MAAAe,QAAA,gBACjElB,OAAA,CAACP,MAAM,CAACoB,GAAG;QACTC,SAAS,EAAE,gCACTX,MAAM,GACF,8DAA8D,GAC9DE,OAAO,GACP,qDAAqD,GACrD,qBAAqB,IACvBF,MAAM,GAAG,eAAe,GAAG,eAAe,EAAG;QACjDgB,UAAU,EAAE;UAAET,KAAK,EAAE;QAAK,CAAE;QAC5BM,UAAU,EAAE;UAAEI,IAAI,EAAE,QAAQ;UAAEC,SAAS,EAAE,GAAG;UAAEC,OAAO,EAAE;QAAG,CAAE;QAAAJ,QAAA,EAE3Df,MAAM,gBACLH,OAAA;UAAGc,SAAS,EAAC,qBAAqB;UAAAI,QAAA,EAAEhB,OAAO,CAACyB;QAAO;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,gBAExD1B,OAAA;UAAKc,SAAS,EAAC,wCAAwC;UAAAI,QAAA,eACrDlB,OAAA,CAACF,aAAa;YAAAoB,QAAA,EAAEhB,OAAO,CAACyB;UAAO;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAgB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,EAGZxB,OAAO,CAAC0B,MAAM,iBACb5B,OAAA,CAACP,MAAM,CAACoB,GAAG;QACTC,SAAS,EAAE,8BACTZ,OAAO,CAAC0B,MAAM,CAACC,OAAO,GAClB,oDAAoD,GACpD,8CAA8C,EACjD;QACHtB,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAK,CAAE;QACrCC,OAAO,EAAE;UAAEH,OAAO,EAAE,CAAC;UAAEE,KAAK,EAAE;QAAE,CAAE;QAClCM,UAAU,EAAE;UAAEc,KAAK,EAAE;QAAI,CAAE;QAAAZ,QAAA,gBAE3BlB,OAAA;UAAKc,SAAS,EAAC,6BAA6B;UAAAI,QAAA,GACzChB,OAAO,CAAC0B,MAAM,CAACC,OAAO,gBACrB7B,OAAA,CAACJ,OAAO;YAACkB,SAAS,EAAC;UAAgB;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEtC1B,OAAA,CAACH,qBAAqB;YAACiB,SAAS,EAAC;UAAc;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAClD,eACD1B,OAAA;YAAMc,SAAS,EAAC,aAAa;YAAAI,QAAA,EAC1BhB,OAAO,CAAC0B,MAAM,CAACR,IAAI,CAACW,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC;UAAC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN1B,OAAA;UAAGc,SAAS,EAAC,cAAc;UAAAI,QAAA,EAAEhB,OAAO,CAAC0B,MAAM,CAAC1B;QAAO;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EACvDxB,OAAO,CAAC0B,MAAM,CAACK,WAAW,iBACzBjC,OAAA;UAAGc,SAAS,EAAC,yBAAyB;UAAAI,QAAA,GAAC,aAC1B,EAAChB,OAAO,CAAC0B,MAAM,CAACK,WAAW;QAAA;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CACb,eAGD1B,OAAA,CAACP,MAAM,CAACyC,CAAC;QACPpB,SAAS,EAAE,8BAA8BX,MAAM,GAAG,YAAY,GAAG,EAAE,EAAG;QACtEI,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBG,OAAO,EAAE;UAAEH,OAAO,EAAE;QAAE,CAAE;QACxBQ,UAAU,EAAE;UAAEc,KAAK,EAAE;QAAI,CAAE;QAAAZ,QAAA,EAE1B,IAAIiB,IAAI,CAACjC,OAAO,CAACkC,SAAS,CAAC,CAACC,kBAAkB,CAAC;MAAC;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEjB,CAAC;AAACY,EAAA,GAzGIrC,WAAW;AA2GjB,eAAeA,WAAW;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}