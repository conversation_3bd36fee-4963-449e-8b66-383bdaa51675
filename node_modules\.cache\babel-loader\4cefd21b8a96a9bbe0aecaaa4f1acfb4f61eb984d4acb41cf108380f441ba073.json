{"ast": null, "code": "import { mix } from '../../utils/mix.mjs';\nimport { percent } from '../../value/types/numbers/units.mjs';\nimport { scalePoint } from './delta-apply.mjs';\n\n/**\n * Remove a delta from a point. This is essentially the steps of applyPointDelta in reverse\n */\nfunction removePointDelta(point, translate, scale, originPoint, boxScale) {\n  point -= translate;\n  point = scalePoint(point, 1 / scale, originPoint);\n  if (boxScale !== undefined) {\n    point = scalePoint(point, 1 / boxScale, originPoint);\n  }\n  return point;\n}\n/**\n * Remove a delta from an axis. This is essentially the steps of applyAxisDelta in reverse\n */\nfunction removeAxisDelta(axis, translate = 0, scale = 1, origin = 0.5, boxScale, originAxis = axis, sourceAxis = axis) {\n  if (percent.test(translate)) {\n    translate = parseFloat(translate);\n    const relativeProgress = mix(sourceAxis.min, sourceAxis.max, translate / 100);\n    translate = relativeProgress - sourceAxis.min;\n  }\n  if (typeof translate !== \"number\") return;\n  let originPoint = mix(originAxis.min, originAxis.max, origin);\n  if (axis === originAxis) originPoint -= translate;\n  axis.min = removePointDelta(axis.min, translate, scale, originPoint, boxScale);\n  axis.max = removePointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Remove a transforms from an axis. This is essentially the steps of applyAxisTransforms in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeAxisTransforms(axis, transforms, [key, scaleKey, originKey], origin, sourceAxis) {\n  removeAxisDelta(axis, transforms[key], transforms[scaleKey], transforms[originKey], transforms.scale, origin, sourceAxis);\n}\n/**\n * The names of the motion values we want to apply as translation, scale and origin.\n */\nconst xKeys = [\"x\", \"scaleX\", \"originX\"];\nconst yKeys = [\"y\", \"scaleY\", \"originY\"];\n/**\n * Remove a transforms from an box. This is essentially the steps of applyAxisBox in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeBoxTransforms(box, transforms, originBox, sourceBox) {\n  removeAxisTransforms(box.x, transforms, xKeys, originBox ? originBox.x : undefined, sourceBox ? sourceBox.x : undefined);\n  removeAxisTransforms(box.y, transforms, yKeys, originBox ? originBox.y : undefined, sourceBox ? sourceBox.y : undefined);\n}\nexport { removeAxisDelta, removeAxisTransforms, removeBoxTransforms, removePointDelta };", "map": {"version": 3, "names": ["mix", "percent", "scalePoint", "removePoint<PERSON><PERSON><PERSON>", "point", "translate", "scale", "originPoint", "boxScale", "undefined", "remove<PERSON>xis<PERSON><PERSON><PERSON>", "axis", "origin", "originAxis", "sourceAxis", "test", "parseFloat", "relativeProgress", "min", "max", "removeAxisTransforms", "transforms", "key", "scaleKey", "<PERSON><PERSON><PERSON>", "xKeys", "y<PERSON><PERSON><PERSON>", "removeBoxTransforms", "box", "originBox", "sourceBox", "x", "y"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/framer-motion/dist/es/projection/geometry/delta-remove.mjs"], "sourcesContent": ["import { mix } from '../../utils/mix.mjs';\nimport { percent } from '../../value/types/numbers/units.mjs';\nimport { scalePoint } from './delta-apply.mjs';\n\n/**\n * Remove a delta from a point. This is essentially the steps of applyPointDelta in reverse\n */\nfunction removePointDelta(point, translate, scale, originPoint, boxScale) {\n    point -= translate;\n    point = scalePoint(point, 1 / scale, originPoint);\n    if (boxScale !== undefined) {\n        point = scalePoint(point, 1 / boxScale, originPoint);\n    }\n    return point;\n}\n/**\n * Remove a delta from an axis. This is essentially the steps of applyAxisDelta in reverse\n */\nfunction removeAxisDelta(axis, translate = 0, scale = 1, origin = 0.5, boxScale, originAxis = axis, sourceAxis = axis) {\n    if (percent.test(translate)) {\n        translate = parseFloat(translate);\n        const relativeProgress = mix(sourceAxis.min, sourceAxis.max, translate / 100);\n        translate = relativeProgress - sourceAxis.min;\n    }\n    if (typeof translate !== \"number\")\n        return;\n    let originPoint = mix(originAxis.min, originAxis.max, origin);\n    if (axis === originAxis)\n        originPoint -= translate;\n    axis.min = removePointDelta(axis.min, translate, scale, originPoint, boxScale);\n    axis.max = removePointDelta(axis.max, translate, scale, originPoint, boxScale);\n}\n/**\n * Remove a transforms from an axis. This is essentially the steps of applyAxisTransforms in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeAxisTransforms(axis, transforms, [key, scaleKey, originKey], origin, sourceAxis) {\n    removeAxisDelta(axis, transforms[key], transforms[scaleKey], transforms[originKey], transforms.scale, origin, sourceAxis);\n}\n/**\n * The names of the motion values we want to apply as translation, scale and origin.\n */\nconst xKeys = [\"x\", \"scaleX\", \"originX\"];\nconst yKeys = [\"y\", \"scaleY\", \"originY\"];\n/**\n * Remove a transforms from an box. This is essentially the steps of applyAxisBox in reverse\n * and acts as a bridge between motion values and removeAxisDelta\n */\nfunction removeBoxTransforms(box, transforms, originBox, sourceBox) {\n    removeAxisTransforms(box.x, transforms, xKeys, originBox ? originBox.x : undefined, sourceBox ? sourceBox.x : undefined);\n    removeAxisTransforms(box.y, transforms, yKeys, originBox ? originBox.y : undefined, sourceBox ? sourceBox.y : undefined);\n}\n\nexport { removeAxisDelta, removeAxisTransforms, removeBoxTransforms, removePointDelta };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,qBAAqB;AACzC,SAASC,OAAO,QAAQ,qCAAqC;AAC7D,SAASC,UAAU,QAAQ,mBAAmB;;AAE9C;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,EAAE;EACtEJ,KAAK,IAAIC,SAAS;EAClBD,KAAK,GAAGF,UAAU,CAACE,KAAK,EAAE,CAAC,GAAGE,KAAK,EAAEC,WAAW,CAAC;EACjD,IAAIC,QAAQ,KAAKC,SAAS,EAAE;IACxBL,KAAK,GAAGF,UAAU,CAACE,KAAK,EAAE,CAAC,GAAGI,QAAQ,EAAED,WAAW,CAAC;EACxD;EACA,OAAOH,KAAK;AAChB;AACA;AACA;AACA;AACA,SAASM,eAAeA,CAACC,IAAI,EAAEN,SAAS,GAAG,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEM,MAAM,GAAG,GAAG,EAAEJ,QAAQ,EAAEK,UAAU,GAAGF,IAAI,EAAEG,UAAU,GAAGH,IAAI,EAAE;EACnH,IAAIV,OAAO,CAACc,IAAI,CAACV,SAAS,CAAC,EAAE;IACzBA,SAAS,GAAGW,UAAU,CAACX,SAAS,CAAC;IACjC,MAAMY,gBAAgB,GAAGjB,GAAG,CAACc,UAAU,CAACI,GAAG,EAAEJ,UAAU,CAACK,GAAG,EAAEd,SAAS,GAAG,GAAG,CAAC;IAC7EA,SAAS,GAAGY,gBAAgB,GAAGH,UAAU,CAACI,GAAG;EACjD;EACA,IAAI,OAAOb,SAAS,KAAK,QAAQ,EAC7B;EACJ,IAAIE,WAAW,GAAGP,GAAG,CAACa,UAAU,CAACK,GAAG,EAAEL,UAAU,CAACM,GAAG,EAAEP,MAAM,CAAC;EAC7D,IAAID,IAAI,KAAKE,UAAU,EACnBN,WAAW,IAAIF,SAAS;EAC5BM,IAAI,CAACO,GAAG,GAAGf,gBAAgB,CAACQ,IAAI,CAACO,GAAG,EAAEb,SAAS,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,CAAC;EAC9EG,IAAI,CAACQ,GAAG,GAAGhB,gBAAgB,CAACQ,IAAI,CAACQ,GAAG,EAAEd,SAAS,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,CAAC;AAClF;AACA;AACA;AACA;AACA;AACA,SAASY,oBAAoBA,CAACT,IAAI,EAAEU,UAAU,EAAE,CAACC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,CAAC,EAAEZ,MAAM,EAAEE,UAAU,EAAE;EAC5FJ,eAAe,CAACC,IAAI,EAAEU,UAAU,CAACC,GAAG,CAAC,EAAED,UAAU,CAACE,QAAQ,CAAC,EAAEF,UAAU,CAACG,SAAS,CAAC,EAAEH,UAAU,CAACf,KAAK,EAAEM,MAAM,EAAEE,UAAU,CAAC;AAC7H;AACA;AACA;AACA;AACA,MAAMW,KAAK,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,SAAS,CAAC;AACxC,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,QAAQ,EAAE,SAAS,CAAC;AACxC;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACC,GAAG,EAAEP,UAAU,EAAEQ,SAAS,EAAEC,SAAS,EAAE;EAChEV,oBAAoB,CAACQ,GAAG,CAACG,CAAC,EAAEV,UAAU,EAAEI,KAAK,EAAEI,SAAS,GAAGA,SAAS,CAACE,CAAC,GAAGtB,SAAS,EAAEqB,SAAS,GAAGA,SAAS,CAACC,CAAC,GAAGtB,SAAS,CAAC;EACxHW,oBAAoB,CAACQ,GAAG,CAACI,CAAC,EAAEX,UAAU,EAAEK,KAAK,EAAEG,SAAS,GAAGA,SAAS,CAACG,CAAC,GAAGvB,SAAS,EAAEqB,SAAS,GAAGA,SAAS,CAACE,CAAC,GAAGvB,SAAS,CAAC;AAC5H;AAEA,SAASC,eAAe,EAAEU,oBAAoB,EAAEO,mBAAmB,EAAExB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}