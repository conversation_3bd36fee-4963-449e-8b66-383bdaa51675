{"ast": null, "code": "/**\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `root` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastRoot} node\n *   mdast node.\n * @returns {HastRoot | HastElement}\n *   hast node.\n */\nexport function root(state, node) {\n  /** @type {HastRoot} */\n  const result = {\n    type: 'root',\n    children: state.wrap(state.all(node))\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": {"version": 3, "names": ["root", "state", "node", "result", "type", "children", "wrap", "all", "patch", "applyData"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/mdast-util-to-hast/lib/handlers/root.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('../state.js').State} State\n */\n\n/**\n * Turn an mdast `root` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastRoot} node\n *   mdast node.\n * @returns {HastRoot | HastElement}\n *   hast node.\n */\nexport function root(state, node) {\n  /** @type {HastRoot} */\n  const result = {type: 'root', children: state.wrap(state.all(node))}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,IAAIA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAChC;EACA,MAAMC,MAAM,GAAG;IAACC,IAAI,EAAE,MAAM;IAAEC,QAAQ,EAAEJ,KAAK,CAACK,IAAI,CAACL,KAAK,CAACM,GAAG,CAACL,IAAI,CAAC;EAAC,CAAC;EACpED,KAAK,CAACO,KAAK,CAACN,IAAI,EAAEC,MAAM,CAAC;EACzB,OAAOF,KAAK,CAACQ,SAAS,CAACP,IAAI,EAAEC,MAAM,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}