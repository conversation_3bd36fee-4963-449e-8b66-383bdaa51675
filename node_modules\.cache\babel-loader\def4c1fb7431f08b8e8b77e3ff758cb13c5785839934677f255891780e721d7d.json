{"ast": null, "code": "/**\n * @typedef URL\n * @property {string} hash\n * @property {string} host\n * @property {string} hostname\n * @property {string} href\n * @property {string} origin\n * @property {string} password\n * @property {string} pathname\n * @property {string} port\n * @property {string} protocol\n * @property {string} search\n * @property {any} searchParams\n * @property {string} username\n * @property {() => string} toString\n * @property {() => string} toJSON\n */\n\n/**\n * Check if `fileUrlOrPath` looks like a URL.\n *\n * @param {unknown} fileUrlOrPath\n *   File path or URL.\n * @returns {fileUrlOrPath is URL}\n *   Whether it’s a URL.\n */\n// From: <https://github.com/nodejs/node/blob/fcf8ba4/lib/internal/url.js#L1501>\nexport function isUrl(fileUrlOrPath) {\n  return fileUrlOrPath !== null && typeof fileUrlOrPath === 'object' &&\n  // @ts-expect-error: indexable.\n  fileUrlOrPath.href &&\n  // @ts-expect-error: indexable.\n  fileUrlOrPath.origin;\n}", "map": {"version": 3, "names": ["isUrl", "fileUrlOrPath", "href", "origin"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/vfile/lib/minurl.shared.js"], "sourcesContent": ["/**\n * @typedef URL\n * @property {string} hash\n * @property {string} host\n * @property {string} hostname\n * @property {string} href\n * @property {string} origin\n * @property {string} password\n * @property {string} pathname\n * @property {string} port\n * @property {string} protocol\n * @property {string} search\n * @property {any} searchParams\n * @property {string} username\n * @property {() => string} toString\n * @property {() => string} toJSON\n */\n\n/**\n * Check if `fileUrlOrPath` looks like a URL.\n *\n * @param {unknown} fileUrlOrPath\n *   File path or URL.\n * @returns {fileUrlOrPath is URL}\n *   Whether it’s a URL.\n */\n// From: <https://github.com/nodejs/node/blob/fcf8ba4/lib/internal/url.js#L1501>\nexport function isUrl(fileUrlOrPath) {\n  return (\n    fileUrlOrPath !== null &&\n    typeof fileUrlOrPath === 'object' &&\n    // @ts-expect-error: indexable.\n    fileUrlOrPath.href &&\n    // @ts-expect-error: indexable.\n    fileUrlOrPath.origin\n  )\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,KAAKA,CAACC,aAAa,EAAE;EACnC,OACEA,aAAa,KAAK,IAAI,IACtB,OAAOA,aAAa,KAAK,QAAQ;EACjC;EACAA,aAAa,CAACC,IAAI;EAClB;EACAD,aAAa,CAACE,MAAM;AAExB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}