!function(n,r){"object"==typeof exports&&"undefined"!=typeof module?module.exports=r():"function"==typeof define&&define.amd?define(r):(n=n||self).InlineStyleParser=r()}(this,function(){"use strict";var v=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,d=/\n/g,n=/^\s*/,g=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,y=/^:\s*/,w=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,A=/^[;\s]*/,r=/^\s+|\s+$/g,x="";function E(n){return n?n.replace(r,x):x}return function(t,e){if("string"!=typeof t)throw new TypeError("First argument must be a string");if(!t)return[];e=e||{};var o=1,i=1;function u(n){var r=n.match(d);r&&(o+=r.length);var e=n.lastIndexOf("\n");i=~e?n.length-e:i+n.length}function c(){var r={line:o,column:i};return function(n){return n.position=new f(r),l(),n}}function f(n){this.start=n,this.end={line:o,column:i},this.source=e.source}function s(n){var r=new Error(e.source+":"+o+":"+i+": "+n);if(r.reason=n,r.filename=e.source,r.line=o,r.column=i,r.source=t,!e.silent)throw r}function a(n){var r=n.exec(t);if(r){var e=r[0];return u(e),t=t.slice(e.length),r}}function l(){a(n)}function p(n){var r;for(n=n||[];r=h();)!1!==r&&n.push(r);return n}function h(){var n=c();if("/"==t.charAt(0)&&"*"==t.charAt(1)){for(var r=2;x!=t.charAt(r)&&("*"!=t.charAt(r)||"/"!=t.charAt(r+1));)++r;if(r+=2,x===t.charAt(r-1))return s("End of comment missing");var e=t.slice(2,r-2);return i+=2,u(e),t=t.slice(r),i+=2,n({type:"comment",comment:e})}}function m(){var n=c(),r=a(g);if(r){if(h(),!a(y))return s("property missing ':'");var e=a(w),t=n({type:"declaration",property:E(r[0].replace(v,x)),value:e?E(e[0].replace(v,x)):x});return a(A),t}}return f.prototype.content=t,l(),function(){var n,r=[];for(p(r);n=m();)!1!==n&&(r.push(n),p(r));return r}()}});
//# sourceMappingURL=inline-style-parser.min.js.map
