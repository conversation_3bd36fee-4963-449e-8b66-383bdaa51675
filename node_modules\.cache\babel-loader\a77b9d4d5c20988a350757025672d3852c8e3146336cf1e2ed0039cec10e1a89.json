{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding, markdownSpace } from 'micromark-util-character';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { constants } from 'micromark-util-symbol/constants.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\n\n/** @type {Construct} */\nconst nonLazyContinuation = {\n  tokenize: tokenizeNonLazyContinuation,\n  partial: true\n};\n\n/** @type {Construct} */\nexport const codeFenced = {\n  name: 'codeFenced',\n  tokenize: tokenizeCodeFenced,\n  concrete: true\n};\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeCodeFenced(effects, ok, nok) {\n  const self = this;\n  /** @type {Construct} */\n  const closeStart = {\n    tokenize: tokenizeCloseStart,\n    partial: true\n  };\n  let initialPrefix = 0;\n  let sizeOpen = 0;\n  /** @type {NonNullable<Code>} */\n  let marker;\n  return start;\n\n  /**\n   * Start of code.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse whitespace like `markdown-rs`.\n    return beforeSequenceOpen(code);\n  }\n\n  /**\n   * In opening fence, after prefix, at sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeSequenceOpen(code) {\n    assert(code === codes.graveAccent || code === codes.tilde, 'expected `` ` `` or `~`');\n    const tail = self.events[self.events.length - 1];\n    initialPrefix = tail && tail[1].type === types.linePrefix ? tail[2].sliceSerialize(tail[1], true).length : 0;\n    marker = code;\n    effects.enter(types.codeFenced);\n    effects.enter(types.codeFencedFence);\n    effects.enter(types.codeFencedFenceSequence);\n    return sequenceOpen(code);\n  }\n\n  /**\n   * In opening fence sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *      ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === marker) {\n      sizeOpen++;\n      effects.consume(code);\n      return sequenceOpen;\n    }\n    if (sizeOpen < constants.codeFencedSequenceSizeMin) {\n      return nok(code);\n    }\n    effects.exit(types.codeFencedFenceSequence);\n    return markdownSpace(code) ? factorySpace(effects, infoBefore, types.whitespace)(code) : infoBefore(code);\n  }\n\n  /**\n   * In opening fence, after the sequence (and optional whitespace), before info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function infoBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFencedFence);\n      return self.interrupt ? ok(code) : effects.check(nonLazyContinuation, atNonLazyBreak, after)(code);\n    }\n    effects.enter(types.codeFencedFenceInfo);\n    effects.enter(types.chunkString, {\n      contentType: constants.contentTypeString\n    });\n    return info(code);\n  }\n\n  /**\n   * In info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function info(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString);\n      effects.exit(types.codeFencedFenceInfo);\n      return infoBefore(code);\n    }\n    if (markdownSpace(code)) {\n      effects.exit(types.chunkString);\n      effects.exit(types.codeFencedFenceInfo);\n      return factorySpace(effects, metaBefore, types.whitespace)(code);\n    }\n    if (code === codes.graveAccent && code === marker) {\n      return nok(code);\n    }\n    effects.consume(code);\n    return info;\n  }\n\n  /**\n   * In opening fence, after info and whitespace, before meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function metaBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return infoBefore(code);\n    }\n    effects.enter(types.codeFencedFenceMeta);\n    effects.enter(types.chunkString, {\n      contentType: constants.contentTypeString\n    });\n    return meta(code);\n  }\n\n  /**\n   * In meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function meta(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString);\n      effects.exit(types.codeFencedFenceMeta);\n      return infoBefore(code);\n    }\n    if (code === codes.graveAccent && code === marker) {\n      return nok(code);\n    }\n    effects.consume(code);\n    return meta;\n  }\n\n  /**\n   * At eol/eof in code, before a non-lazy closing fence or content.\n   *\n   * ```markdown\n   * > | ~~~js\n   *          ^\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function atNonLazyBreak(code) {\n    assert(markdownLineEnding(code), 'expected eol');\n    return effects.attempt(closeStart, after, contentBefore)(code);\n  }\n\n  /**\n   * Before code content, not a closing fence, at eol.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentBefore(code) {\n    assert(markdownLineEnding(code), 'expected eol');\n    effects.enter(types.lineEnding);\n    effects.consume(code);\n    effects.exit(types.lineEnding);\n    return contentStart;\n  }\n\n  /**\n   * Before code content, not a closing fence.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentStart(code) {\n    return initialPrefix > 0 && markdownSpace(code) ? factorySpace(effects, beforeContentChunk, types.linePrefix, initialPrefix + 1)(code) : beforeContentChunk(code);\n  }\n\n  /**\n   * Before code content, after optional prefix.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeContentChunk(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return effects.check(nonLazyContinuation, atNonLazyBreak, after)(code);\n    }\n    effects.enter(types.codeFlowValue);\n    return contentChunk(code);\n  }\n\n  /**\n   * In code content.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^^^^^^^^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentChunk(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFlowValue);\n      return beforeContentChunk(code);\n    }\n    effects.consume(code);\n    return contentChunk;\n  }\n\n  /**\n   * After code.\n   *\n   * ```markdown\n   *   | ~~~js\n   *   | alert(1)\n   * > | ~~~\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    effects.exit(types.codeFenced);\n    return ok(code);\n  }\n\n  /**\n   * @this {TokenizeContext}\n   * @type {Tokenizer}\n   */\n  function tokenizeCloseStart(effects, ok, nok) {\n    let size = 0;\n    return startBefore;\n\n    /**\n     *\n     *\n     * @type {State}\n     */\n    function startBefore(code) {\n      assert(markdownLineEnding(code), 'expected eol');\n      effects.enter(types.lineEnding);\n      effects.consume(code);\n      effects.exit(types.lineEnding);\n      return start;\n    }\n\n    /**\n     * Before closing fence, at optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function start(code) {\n      // Always populated by defaults.\n      assert(self.parser.constructs.disable.null, 'expected `disable.null` to be populated');\n\n      // To do: `enter` here or in next state?\n      effects.enter(types.codeFencedFence);\n      return markdownSpace(code) ? factorySpace(effects, beforeSequenceClose, types.linePrefix, self.parser.constructs.disable.null.includes('codeIndented') ? undefined : constants.tabSize)(code) : beforeSequenceClose(code);\n    }\n\n    /**\n     * In closing fence, after optional whitespace, at sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function beforeSequenceClose(code) {\n      if (code === marker) {\n        effects.enter(types.codeFencedFenceSequence);\n        return sequenceClose(code);\n      }\n      return nok(code);\n    }\n\n    /**\n     * In closing fence sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceClose(code) {\n      if (code === marker) {\n        size++;\n        effects.consume(code);\n        return sequenceClose;\n      }\n      if (size >= sizeOpen) {\n        effects.exit(types.codeFencedFenceSequence);\n        return markdownSpace(code) ? factorySpace(effects, sequenceCloseAfter, types.whitespace)(code) : sequenceCloseAfter(code);\n      }\n      return nok(code);\n    }\n\n    /**\n     * After closing fence sequence, after optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *        ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceCloseAfter(code) {\n      if (code === codes.eof || markdownLineEnding(code)) {\n        effects.exit(types.codeFencedFence);\n        return ok(code);\n      }\n      return nok(code);\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuation(effects, ok, nok) {\n  const self = this;\n  return start;\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.eof) {\n      return nok(code);\n    }\n    assert(markdownLineEnding(code), 'expected eol');\n    effects.enter(types.lineEnding);\n    effects.consume(code);\n    effects.exit(types.lineEnding);\n    return lineStart;\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function lineStart(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code);\n  }\n}", "map": {"version": 3, "names": ["factorySpace", "markdownLineEnding", "markdownSpace", "codes", "constants", "types", "ok", "assert", "nonLazyContinuation", "tokenize", "tokenizeNonLazyContinuation", "partial", "codeFenced", "name", "tokenizeCodeFenced", "concrete", "effects", "nok", "self", "closeStart", "tokenizeCloseStart", "initialPrefix", "sizeOpen", "marker", "start", "code", "beforeSequenceOpen", "graveAccent", "tilde", "tail", "events", "length", "type", "linePrefix", "sliceSerialize", "enter", "codeFencedFence", "codeFencedFenceSequence", "sequenceOpen", "consume", "codeFencedSequenceSizeMin", "exit", "infoBefore", "whitespace", "eof", "interrupt", "check", "atNonLazyBreak", "after", "codeFencedFenceInfo", "chunkString", "contentType", "contentTypeString", "info", "metaBefore", "codeFencedFenceMeta", "meta", "attempt", "contentBefore", "lineEnding", "contentStart", "beforeContentChunk", "codeFlowValue", "contentChunk", "size", "startBefore", "parser", "constructs", "disable", "null", "beforeSequenceClose", "includes", "undefined", "tabSize", "sequenceClose", "sequenceCloseAfter", "lineStart", "lazy", "now", "line"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/micromark-core-commonmark/dev/lib/code-fenced.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/** @type {Construct} */\nconst nonLazyContinuation = {\n  tokenize: tokenizeNonLazyContinuation,\n  partial: true\n}\n\n/** @type {Construct} */\nexport const codeFenced = {\n  name: 'codeFenced',\n  tokenize: tokenizeCodeFenced,\n  concrete: true\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeCodeFenced(effects, ok, nok) {\n  const self = this\n  /** @type {Construct} */\n  const closeStart = {tokenize: tokenizeCloseStart, partial: true}\n  let initialPrefix = 0\n  let sizeOpen = 0\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of code.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse whitespace like `markdown-rs`.\n    return beforeSequenceOpen(code)\n  }\n\n  /**\n   * In opening fence, after prefix, at sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeSequenceOpen(code) {\n    assert(\n      code === codes.graveAccent || code === codes.tilde,\n      'expected `` ` `` or `~`'\n    )\n\n    const tail = self.events[self.events.length - 1]\n    initialPrefix =\n      tail && tail[1].type === types.linePrefix\n        ? tail[2].sliceSerialize(tail[1], true).length\n        : 0\n\n    marker = code\n    effects.enter(types.codeFenced)\n    effects.enter(types.codeFencedFence)\n    effects.enter(types.codeFencedFenceSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening fence sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *      ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === marker) {\n      sizeOpen++\n      effects.consume(code)\n      return sequenceOpen\n    }\n\n    if (sizeOpen < constants.codeFencedSequenceSizeMin) {\n      return nok(code)\n    }\n\n    effects.exit(types.codeFencedFenceSequence)\n    return markdownSpace(code)\n      ? factorySpace(effects, infoBefore, types.whitespace)(code)\n      : infoBefore(code)\n  }\n\n  /**\n   * In opening fence, after the sequence (and optional whitespace), before info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function infoBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFencedFence)\n      return self.interrupt\n        ? ok(code)\n        : effects.check(nonLazyContinuation, atNonLazyBreak, after)(code)\n    }\n\n    effects.enter(types.codeFencedFenceInfo)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return info(code)\n  }\n\n  /**\n   * In info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function info(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString)\n      effects.exit(types.codeFencedFenceInfo)\n      return infoBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.exit(types.chunkString)\n      effects.exit(types.codeFencedFenceInfo)\n      return factorySpace(effects, metaBefore, types.whitespace)(code)\n    }\n\n    if (code === codes.graveAccent && code === marker) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return info\n  }\n\n  /**\n   * In opening fence, after info and whitespace, before meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function metaBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return infoBefore(code)\n    }\n\n    effects.enter(types.codeFencedFenceMeta)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return meta(code)\n  }\n\n  /**\n   * In meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function meta(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString)\n      effects.exit(types.codeFencedFenceMeta)\n      return infoBefore(code)\n    }\n\n    if (code === codes.graveAccent && code === marker) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return meta\n  }\n\n  /**\n   * At eol/eof in code, before a non-lazy closing fence or content.\n   *\n   * ```markdown\n   * > | ~~~js\n   *          ^\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function atNonLazyBreak(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    return effects.attempt(closeStart, after, contentBefore)(code)\n  }\n\n  /**\n   * Before code content, not a closing fence, at eol.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentBefore(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return contentStart\n  }\n\n  /**\n   * Before code content, not a closing fence.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentStart(code) {\n    return initialPrefix > 0 && markdownSpace(code)\n      ? factorySpace(\n          effects,\n          beforeContentChunk,\n          types.linePrefix,\n          initialPrefix + 1\n        )(code)\n      : beforeContentChunk(code)\n  }\n\n  /**\n   * Before code content, after optional prefix.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeContentChunk(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return effects.check(nonLazyContinuation, atNonLazyBreak, after)(code)\n    }\n\n    effects.enter(types.codeFlowValue)\n    return contentChunk(code)\n  }\n\n  /**\n   * In code content.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^^^^^^^^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentChunk(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFlowValue)\n      return beforeContentChunk(code)\n    }\n\n    effects.consume(code)\n    return contentChunk\n  }\n\n  /**\n   * After code.\n   *\n   * ```markdown\n   *   | ~~~js\n   *   | alert(1)\n   * > | ~~~\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    effects.exit(types.codeFenced)\n    return ok(code)\n  }\n\n  /**\n   * @this {TokenizeContext}\n   * @type {Tokenizer}\n   */\n  function tokenizeCloseStart(effects, ok, nok) {\n    let size = 0\n\n    return startBefore\n\n    /**\n     *\n     *\n     * @type {State}\n     */\n    function startBefore(code) {\n      assert(markdownLineEnding(code), 'expected eol')\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return start\n    }\n\n    /**\n     * Before closing fence, at optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function start(code) {\n      // Always populated by defaults.\n      assert(\n        self.parser.constructs.disable.null,\n        'expected `disable.null` to be populated'\n      )\n\n      // To do: `enter` here or in next state?\n      effects.enter(types.codeFencedFence)\n      return markdownSpace(code)\n        ? factorySpace(\n            effects,\n            beforeSequenceClose,\n            types.linePrefix,\n            self.parser.constructs.disable.null.includes('codeIndented')\n              ? undefined\n              : constants.tabSize\n          )(code)\n        : beforeSequenceClose(code)\n    }\n\n    /**\n     * In closing fence, after optional whitespace, at sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function beforeSequenceClose(code) {\n      if (code === marker) {\n        effects.enter(types.codeFencedFenceSequence)\n        return sequenceClose(code)\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * In closing fence sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceClose(code) {\n      if (code === marker) {\n        size++\n        effects.consume(code)\n        return sequenceClose\n      }\n\n      if (size >= sizeOpen) {\n        effects.exit(types.codeFencedFenceSequence)\n        return markdownSpace(code)\n          ? factorySpace(effects, sequenceCloseAfter, types.whitespace)(code)\n          : sequenceCloseAfter(code)\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * After closing fence sequence, after optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *        ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceCloseAfter(code) {\n      if (code === codes.eof || markdownLineEnding(code)) {\n        effects.exit(types.codeFencedFence)\n        return ok(code)\n      }\n\n      return nok(code)\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuation(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return lineStart\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function lineStart(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,yBAAyB;AACpD,SAAQC,kBAAkB,EAAEC,aAAa,QAAO,0BAA0B;AAC1E,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,SAAS,QAAO,oCAAoC;AAC5D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;;AAEvC;AACA,MAAMC,mBAAmB,GAAG;EAC1BC,QAAQ,EAAEC,2BAA2B;EACrCC,OAAO,EAAE;AACX,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,IAAI,EAAE,YAAY;EAClBJ,QAAQ,EAAEK,kBAAkB;EAC5BC,QAAQ,EAAE;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASD,kBAAkBA,CAACE,OAAO,EAAEV,EAAE,EAAEW,GAAG,EAAE;EAC5C,MAAMC,IAAI,GAAG,IAAI;EACjB;EACA,MAAMC,UAAU,GAAG;IAACV,QAAQ,EAAEW,kBAAkB;IAAET,OAAO,EAAE;EAAI,CAAC;EAChE,IAAIU,aAAa,GAAG,CAAC;EACrB,IAAIC,QAAQ,GAAG,CAAC;EAChB;EACA,IAAIC,MAAM;EAEV,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnB;IACA,OAAOC,kBAAkB,CAACD,IAAI,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,kBAAkBA,CAACD,IAAI,EAAE;IAChClB,MAAM,CACJkB,IAAI,KAAKtB,KAAK,CAACwB,WAAW,IAAIF,IAAI,KAAKtB,KAAK,CAACyB,KAAK,EAClD,yBACF,CAAC;IAED,MAAMC,IAAI,GAAGX,IAAI,CAACY,MAAM,CAACZ,IAAI,CAACY,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;IAChDV,aAAa,GACXQ,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACG,IAAI,KAAK3B,KAAK,CAAC4B,UAAU,GACrCJ,IAAI,CAAC,CAAC,CAAC,CAACK,cAAc,CAACL,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAACE,MAAM,GAC5C,CAAC;IAEPR,MAAM,GAAGE,IAAI;IACbT,OAAO,CAACmB,KAAK,CAAC9B,KAAK,CAACO,UAAU,CAAC;IAC/BI,OAAO,CAACmB,KAAK,CAAC9B,KAAK,CAAC+B,eAAe,CAAC;IACpCpB,OAAO,CAACmB,KAAK,CAAC9B,KAAK,CAACgC,uBAAuB,CAAC;IAC5C,OAAOC,YAAY,CAACb,IAAI,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASa,YAAYA,CAACb,IAAI,EAAE;IAC1B,IAAIA,IAAI,KAAKF,MAAM,EAAE;MACnBD,QAAQ,EAAE;MACVN,OAAO,CAACuB,OAAO,CAACd,IAAI,CAAC;MACrB,OAAOa,YAAY;IACrB;IAEA,IAAIhB,QAAQ,GAAGlB,SAAS,CAACoC,yBAAyB,EAAE;MAClD,OAAOvB,GAAG,CAACQ,IAAI,CAAC;IAClB;IAEAT,OAAO,CAACyB,IAAI,CAACpC,KAAK,CAACgC,uBAAuB,CAAC;IAC3C,OAAOnC,aAAa,CAACuB,IAAI,CAAC,GACtBzB,YAAY,CAACgB,OAAO,EAAE0B,UAAU,EAAErC,KAAK,CAACsC,UAAU,CAAC,CAAClB,IAAI,CAAC,GACzDiB,UAAU,CAACjB,IAAI,CAAC;EACtB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASiB,UAAUA,CAACjB,IAAI,EAAE;IACxB,IAAIA,IAAI,KAAKtB,KAAK,CAACyC,GAAG,IAAI3C,kBAAkB,CAACwB,IAAI,CAAC,EAAE;MAClDT,OAAO,CAACyB,IAAI,CAACpC,KAAK,CAAC+B,eAAe,CAAC;MACnC,OAAOlB,IAAI,CAAC2B,SAAS,GACjBvC,EAAE,CAACmB,IAAI,CAAC,GACRT,OAAO,CAAC8B,KAAK,CAACtC,mBAAmB,EAAEuC,cAAc,EAAEC,KAAK,CAAC,CAACvB,IAAI,CAAC;IACrE;IAEAT,OAAO,CAACmB,KAAK,CAAC9B,KAAK,CAAC4C,mBAAmB,CAAC;IACxCjC,OAAO,CAACmB,KAAK,CAAC9B,KAAK,CAAC6C,WAAW,EAAE;MAACC,WAAW,EAAE/C,SAAS,CAACgD;IAAiB,CAAC,CAAC;IAC5E,OAAOC,IAAI,CAAC5B,IAAI,CAAC;EACnB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS4B,IAAIA,CAAC5B,IAAI,EAAE;IAClB,IAAIA,IAAI,KAAKtB,KAAK,CAACyC,GAAG,IAAI3C,kBAAkB,CAACwB,IAAI,CAAC,EAAE;MAClDT,OAAO,CAACyB,IAAI,CAACpC,KAAK,CAAC6C,WAAW,CAAC;MAC/BlC,OAAO,CAACyB,IAAI,CAACpC,KAAK,CAAC4C,mBAAmB,CAAC;MACvC,OAAOP,UAAU,CAACjB,IAAI,CAAC;IACzB;IAEA,IAAIvB,aAAa,CAACuB,IAAI,CAAC,EAAE;MACvBT,OAAO,CAACyB,IAAI,CAACpC,KAAK,CAAC6C,WAAW,CAAC;MAC/BlC,OAAO,CAACyB,IAAI,CAACpC,KAAK,CAAC4C,mBAAmB,CAAC;MACvC,OAAOjD,YAAY,CAACgB,OAAO,EAAEsC,UAAU,EAAEjD,KAAK,CAACsC,UAAU,CAAC,CAAClB,IAAI,CAAC;IAClE;IAEA,IAAIA,IAAI,KAAKtB,KAAK,CAACwB,WAAW,IAAIF,IAAI,KAAKF,MAAM,EAAE;MACjD,OAAON,GAAG,CAACQ,IAAI,CAAC;IAClB;IAEAT,OAAO,CAACuB,OAAO,CAACd,IAAI,CAAC;IACrB,OAAO4B,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,UAAUA,CAAC7B,IAAI,EAAE;IACxB,IAAIA,IAAI,KAAKtB,KAAK,CAACyC,GAAG,IAAI3C,kBAAkB,CAACwB,IAAI,CAAC,EAAE;MAClD,OAAOiB,UAAU,CAACjB,IAAI,CAAC;IACzB;IAEAT,OAAO,CAACmB,KAAK,CAAC9B,KAAK,CAACkD,mBAAmB,CAAC;IACxCvC,OAAO,CAACmB,KAAK,CAAC9B,KAAK,CAAC6C,WAAW,EAAE;MAACC,WAAW,EAAE/C,SAAS,CAACgD;IAAiB,CAAC,CAAC;IAC5E,OAAOI,IAAI,CAAC/B,IAAI,CAAC;EACnB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS+B,IAAIA,CAAC/B,IAAI,EAAE;IAClB,IAAIA,IAAI,KAAKtB,KAAK,CAACyC,GAAG,IAAI3C,kBAAkB,CAACwB,IAAI,CAAC,EAAE;MAClDT,OAAO,CAACyB,IAAI,CAACpC,KAAK,CAAC6C,WAAW,CAAC;MAC/BlC,OAAO,CAACyB,IAAI,CAACpC,KAAK,CAACkD,mBAAmB,CAAC;MACvC,OAAOb,UAAU,CAACjB,IAAI,CAAC;IACzB;IAEA,IAAIA,IAAI,KAAKtB,KAAK,CAACwB,WAAW,IAAIF,IAAI,KAAKF,MAAM,EAAE;MACjD,OAAON,GAAG,CAACQ,IAAI,CAAC;IAClB;IAEAT,OAAO,CAACuB,OAAO,CAACd,IAAI,CAAC;IACrB,OAAO+B,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAST,cAAcA,CAACtB,IAAI,EAAE;IAC5BlB,MAAM,CAACN,kBAAkB,CAACwB,IAAI,CAAC,EAAE,cAAc,CAAC;IAChD,OAAOT,OAAO,CAACyC,OAAO,CAACtC,UAAU,EAAE6B,KAAK,EAAEU,aAAa,CAAC,CAACjC,IAAI,CAAC;EAChE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASiC,aAAaA,CAACjC,IAAI,EAAE;IAC3BlB,MAAM,CAACN,kBAAkB,CAACwB,IAAI,CAAC,EAAE,cAAc,CAAC;IAChDT,OAAO,CAACmB,KAAK,CAAC9B,KAAK,CAACsD,UAAU,CAAC;IAC/B3C,OAAO,CAACuB,OAAO,CAACd,IAAI,CAAC;IACrBT,OAAO,CAACyB,IAAI,CAACpC,KAAK,CAACsD,UAAU,CAAC;IAC9B,OAAOC,YAAY;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,YAAYA,CAACnC,IAAI,EAAE;IAC1B,OAAOJ,aAAa,GAAG,CAAC,IAAInB,aAAa,CAACuB,IAAI,CAAC,GAC3CzB,YAAY,CACVgB,OAAO,EACP6C,kBAAkB,EAClBxD,KAAK,CAAC4B,UAAU,EAChBZ,aAAa,GAAG,CAClB,CAAC,CAACI,IAAI,CAAC,GACPoC,kBAAkB,CAACpC,IAAI,CAAC;EAC9B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASoC,kBAAkBA,CAACpC,IAAI,EAAE;IAChC,IAAIA,IAAI,KAAKtB,KAAK,CAACyC,GAAG,IAAI3C,kBAAkB,CAACwB,IAAI,CAAC,EAAE;MAClD,OAAOT,OAAO,CAAC8B,KAAK,CAACtC,mBAAmB,EAAEuC,cAAc,EAAEC,KAAK,CAAC,CAACvB,IAAI,CAAC;IACxE;IAEAT,OAAO,CAACmB,KAAK,CAAC9B,KAAK,CAACyD,aAAa,CAAC;IAClC,OAAOC,YAAY,CAACtC,IAAI,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASsC,YAAYA,CAACtC,IAAI,EAAE;IAC1B,IAAIA,IAAI,KAAKtB,KAAK,CAACyC,GAAG,IAAI3C,kBAAkB,CAACwB,IAAI,CAAC,EAAE;MAClDT,OAAO,CAACyB,IAAI,CAACpC,KAAK,CAACyD,aAAa,CAAC;MACjC,OAAOD,kBAAkB,CAACpC,IAAI,CAAC;IACjC;IAEAT,OAAO,CAACuB,OAAO,CAACd,IAAI,CAAC;IACrB,OAAOsC,YAAY;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASf,KAAKA,CAACvB,IAAI,EAAE;IACnBT,OAAO,CAACyB,IAAI,CAACpC,KAAK,CAACO,UAAU,CAAC;IAC9B,OAAON,EAAE,CAACmB,IAAI,CAAC;EACjB;;EAEA;AACF;AACA;AACA;EACE,SAASL,kBAAkBA,CAACJ,OAAO,EAAEV,EAAE,EAAEW,GAAG,EAAE;IAC5C,IAAI+C,IAAI,GAAG,CAAC;IAEZ,OAAOC,WAAW;;IAElB;AACJ;AACA;AACA;AACA;IACI,SAASA,WAAWA,CAACxC,IAAI,EAAE;MACzBlB,MAAM,CAACN,kBAAkB,CAACwB,IAAI,CAAC,EAAE,cAAc,CAAC;MAChDT,OAAO,CAACmB,KAAK,CAAC9B,KAAK,CAACsD,UAAU,CAAC;MAC/B3C,OAAO,CAACuB,OAAO,CAACd,IAAI,CAAC;MACrBT,OAAO,CAACyB,IAAI,CAACpC,KAAK,CAACsD,UAAU,CAAC;MAC9B,OAAOnC,KAAK;IACd;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAASA,KAAKA,CAACC,IAAI,EAAE;MACnB;MACAlB,MAAM,CACJW,IAAI,CAACgD,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,EACnC,yCACF,CAAC;;MAED;MACArD,OAAO,CAACmB,KAAK,CAAC9B,KAAK,CAAC+B,eAAe,CAAC;MACpC,OAAOlC,aAAa,CAACuB,IAAI,CAAC,GACtBzB,YAAY,CACVgB,OAAO,EACPsD,mBAAmB,EACnBjE,KAAK,CAAC4B,UAAU,EAChBf,IAAI,CAACgD,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,CAACE,QAAQ,CAAC,cAAc,CAAC,GACxDC,SAAS,GACTpE,SAAS,CAACqE,OAChB,CAAC,CAAChD,IAAI,CAAC,GACP6C,mBAAmB,CAAC7C,IAAI,CAAC;IAC/B;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAAS6C,mBAAmBA,CAAC7C,IAAI,EAAE;MACjC,IAAIA,IAAI,KAAKF,MAAM,EAAE;QACnBP,OAAO,CAACmB,KAAK,CAAC9B,KAAK,CAACgC,uBAAuB,CAAC;QAC5C,OAAOqC,aAAa,CAACjD,IAAI,CAAC;MAC5B;MAEA,OAAOR,GAAG,CAACQ,IAAI,CAAC;IAClB;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAASiD,aAAaA,CAACjD,IAAI,EAAE;MAC3B,IAAIA,IAAI,KAAKF,MAAM,EAAE;QACnByC,IAAI,EAAE;QACNhD,OAAO,CAACuB,OAAO,CAACd,IAAI,CAAC;QACrB,OAAOiD,aAAa;MACtB;MAEA,IAAIV,IAAI,IAAI1C,QAAQ,EAAE;QACpBN,OAAO,CAACyB,IAAI,CAACpC,KAAK,CAACgC,uBAAuB,CAAC;QAC3C,OAAOnC,aAAa,CAACuB,IAAI,CAAC,GACtBzB,YAAY,CAACgB,OAAO,EAAE2D,kBAAkB,EAAEtE,KAAK,CAACsC,UAAU,CAAC,CAAClB,IAAI,CAAC,GACjEkD,kBAAkB,CAAClD,IAAI,CAAC;MAC9B;MAEA,OAAOR,GAAG,CAACQ,IAAI,CAAC;IAClB;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAASkD,kBAAkBA,CAAClD,IAAI,EAAE;MAChC,IAAIA,IAAI,KAAKtB,KAAK,CAACyC,GAAG,IAAI3C,kBAAkB,CAACwB,IAAI,CAAC,EAAE;QAClDT,OAAO,CAACyB,IAAI,CAACpC,KAAK,CAAC+B,eAAe,CAAC;QACnC,OAAO9B,EAAE,CAACmB,IAAI,CAAC;MACjB;MAEA,OAAOR,GAAG,CAACQ,IAAI,CAAC;IAClB;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASf,2BAA2BA,CAACM,OAAO,EAAEV,EAAE,EAAEW,GAAG,EAAE;EACrD,MAAMC,IAAI,GAAG,IAAI;EAEjB,OAAOM,KAAK;;EAEZ;AACF;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnB,IAAIA,IAAI,KAAKtB,KAAK,CAACyC,GAAG,EAAE;MACtB,OAAO3B,GAAG,CAACQ,IAAI,CAAC;IAClB;IAEAlB,MAAM,CAACN,kBAAkB,CAACwB,IAAI,CAAC,EAAE,cAAc,CAAC;IAChDT,OAAO,CAACmB,KAAK,CAAC9B,KAAK,CAACsD,UAAU,CAAC;IAC/B3C,OAAO,CAACuB,OAAO,CAACd,IAAI,CAAC;IACrBT,OAAO,CAACyB,IAAI,CAACpC,KAAK,CAACsD,UAAU,CAAC;IAC9B,OAAOiB,SAAS;EAClB;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASA,SAASA,CAACnD,IAAI,EAAE;IACvB,OAAOP,IAAI,CAACgD,MAAM,CAACW,IAAI,CAAC3D,IAAI,CAAC4D,GAAG,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG9D,GAAG,CAACQ,IAAI,CAAC,GAAGnB,EAAE,CAACmB,IAAI,CAAC;EACjE;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}