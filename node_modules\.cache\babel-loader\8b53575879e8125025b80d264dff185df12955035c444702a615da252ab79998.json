{"ast": null, "code": "import { booleanish, number, spaceSeparated } from './util/types.js';\nimport { create } from './util/create.js';\nexport const aria = create({\n  transform(_, prop) {\n    return prop === 'role' ? prop : 'aria-' + prop.slice(4).toLowerCase();\n  },\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: booleanish,\n    ariaChecked: booleanish,\n    ariaColCount: number,\n    ariaColIndex: number,\n    ariaColSpan: number,\n    ariaControls: spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: booleanish,\n    ariaDropEffect: spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: booleanish,\n    ariaFlowTo: spaceSeparated,\n    ariaGrabbed: booleanish,\n    ariaHasPopup: null,\n    ariaHidden: booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: spaceSeparated,\n    ariaLevel: number,\n    ariaLive: null,\n    ariaModal: booleanish,\n    ariaMultiLine: booleanish,\n    ariaMultiSelectable: booleanish,\n    ariaOrientation: null,\n    ariaOwns: spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: number,\n    ariaPressed: booleanish,\n    ariaReadOnly: booleanish,\n    ariaRelevant: null,\n    ariaRequired: booleanish,\n    ariaRoleDescription: spaceSeparated,\n    ariaRowCount: number,\n    ariaRowIndex: number,\n    ariaRowSpan: number,\n    ariaSelected: booleanish,\n    ariaSetSize: number,\n    ariaSort: null,\n    ariaValueMax: number,\n    ariaValueMin: number,\n    ariaValueNow: number,\n    ariaValueText: null,\n    role: null\n  }\n});", "map": {"version": 3, "names": ["booleanish", "number", "spaceSeparated", "create", "aria", "transform", "_", "prop", "slice", "toLowerCase", "properties", "ariaActiveDescendant", "ariaAtomic", "ariaAutoComplete", "ariaBusy", "ariaChe<PERSON>", "ariaColCount", "ariaColIndex", "ariaColSpan", "ariaControls", "aria<PERSON>urrent", "ariaDescribedBy", "ariaDetails", "ariaDisabled", "ariaDropEffect", "ariaErrorMessage", "ariaExpanded", "ariaFlowTo", "ariaGrabbed", "aria<PERSON>as<PERSON><PERSON><PERSON>", "ariaHidden", "ariaInvalid", "ariaKeyShortcuts", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "ariaLevel", "ariaLive", "ariaModal", "ariaMultiLine", "ariaMultiSelectable", "ariaOrientation", "ariaOwns", "ariaPlaceholder", "ariaPosInSet", "ariaPressed", "ariaReadOnly", "ariaRelevant", "ariaRequired", "ariaRoleDescription", "ariaRowCount", "ariaRowIndex", "ariaRowSpan", "ariaSelected", "ariaSetSize", "ariaSort", "ariaValueMax", "ariaValueMin", "ariaValueNow", "ariaValueText", "role"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/property-information/lib/aria.js"], "sourcesContent": ["import {booleanish, number, spaceSeparated} from './util/types.js'\nimport {create} from './util/create.js'\n\nexport const aria = create({\n  transform(_, prop) {\n    return prop === 'role' ? prop : 'aria-' + prop.slice(4).toLowerCase()\n  },\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: booleanish,\n    ariaChecked: booleanish,\n    ariaColCount: number,\n    ariaColIndex: number,\n    ariaColSpan: number,\n    ariaControls: spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: booleanish,\n    ariaDropEffect: spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: booleanish,\n    ariaFlowTo: spaceSeparated,\n    ariaGrabbed: booleanish,\n    ariaHasPopup: null,\n    ariaHidden: booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: spaceSeparated,\n    ariaLevel: number,\n    ariaLive: null,\n    ariaModal: booleanish,\n    ariaMultiLine: booleanish,\n    ariaMultiSelectable: booleanish,\n    ariaOrientation: null,\n    ariaOwns: spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: number,\n    ariaPressed: booleanish,\n    ariaReadOnly: booleanish,\n    ariaRelevant: null,\n    ariaRequired: booleanish,\n    ariaRoleDescription: spaceSeparated,\n    ariaRowCount: number,\n    ariaRowIndex: number,\n    ariaRowSpan: number,\n    ariaSelected: booleanish,\n    ariaSetSize: number,\n    ariaSort: null,\n    ariaValueMax: number,\n    ariaValueMin: number,\n    ariaValueNow: number,\n    ariaValueText: null,\n    role: null\n  }\n})\n"], "mappings": "AAAA,SAAQA,UAAU,EAAEC,MAAM,EAAEC,cAAc,QAAO,iBAAiB;AAClE,SAAQC,MAAM,QAAO,kBAAkB;AAEvC,OAAO,MAAMC,IAAI,GAAGD,MAAM,CAAC;EACzBE,SAASA,CAACC,CAAC,EAAEC,IAAI,EAAE;IACjB,OAAOA,IAAI,KAAK,MAAM,GAAGA,IAAI,GAAG,OAAO,GAAGA,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACvE,CAAC;EACDC,UAAU,EAAE;IACVC,oBAAoB,EAAE,IAAI;IAC1BC,UAAU,EAAEZ,UAAU;IACtBa,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAEd,UAAU;IACpBe,WAAW,EAAEf,UAAU;IACvBgB,YAAY,EAAEf,MAAM;IACpBgB,YAAY,EAAEhB,MAAM;IACpBiB,WAAW,EAAEjB,MAAM;IACnBkB,YAAY,EAAEjB,cAAc;IAC5BkB,WAAW,EAAE,IAAI;IACjBC,eAAe,EAAEnB,cAAc;IAC/BoB,WAAW,EAAE,IAAI;IACjBC,YAAY,EAAEvB,UAAU;IACxBwB,cAAc,EAAEtB,cAAc;IAC9BuB,gBAAgB,EAAE,IAAI;IACtBC,YAAY,EAAE1B,UAAU;IACxB2B,UAAU,EAAEzB,cAAc;IAC1B0B,WAAW,EAAE5B,UAAU;IACvB6B,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE9B,UAAU;IACtB+B,WAAW,EAAE,IAAI;IACjBC,gBAAgB,EAAE,IAAI;IACtBC,SAAS,EAAE,IAAI;IACfC,cAAc,EAAEhC,cAAc;IAC9BiC,SAAS,EAAElC,MAAM;IACjBmC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAErC,UAAU;IACrBsC,aAAa,EAAEtC,UAAU;IACzBuC,mBAAmB,EAAEvC,UAAU;IAC/BwC,eAAe,EAAE,IAAI;IACrBC,QAAQ,EAAEvC,cAAc;IACxBwC,eAAe,EAAE,IAAI;IACrBC,YAAY,EAAE1C,MAAM;IACpB2C,WAAW,EAAE5C,UAAU;IACvB6C,YAAY,EAAE7C,UAAU;IACxB8C,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE/C,UAAU;IACxBgD,mBAAmB,EAAE9C,cAAc;IACnC+C,YAAY,EAAEhD,MAAM;IACpBiD,YAAY,EAAEjD,MAAM;IACpBkD,WAAW,EAAElD,MAAM;IACnBmD,YAAY,EAAEpD,UAAU;IACxBqD,WAAW,EAAEpD,MAAM;IACnBqD,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAEtD,MAAM;IACpBuD,YAAY,EAAEvD,MAAM;IACpBwD,YAAY,EAAExD,MAAM;IACpByD,aAAa,EAAE,IAAI;IACnBC,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}