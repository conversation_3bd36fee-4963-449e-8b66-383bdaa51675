{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\nimport { labelEnd } from './label-end.js';\n\n/** @type {Construct} */\nexport const labelStartLink = {\n  name: 'labelStartLink',\n  tokenize: tokenizeLabelStartLink,\n  resolveAll: labelEnd.resolveAll\n};\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeLabelStartLink(effects, ok, nok) {\n  const self = this;\n  return start;\n\n  /**\n   * Start of label (link) start.\n   *\n   * ```markdown\n   * > | a [b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`');\n    effects.enter(types.labelLink);\n    effects.enter(types.labelMarker);\n    effects.consume(code);\n    effects.exit(types.labelMarker);\n    effects.exit(types.labelLink);\n    return after;\n  }\n\n  /** @type {State} */\n  function after(code) {\n    // To do: this isn’t needed in `micromark-extension-gfm-footnote`,\n    // remove.\n    // Hidden footnotes hook.\n    /* c8 ignore next 3 */\n    return code === codes.caret && '_hiddenFootnoteSupport' in self.parser.constructs ? nok(code) : ok(code);\n  }\n}", "map": {"version": 3, "names": ["codes", "types", "ok", "assert", "labelEnd", "labelStartLink", "name", "tokenize", "tokenizeLabelStartLink", "resolveAll", "effects", "nok", "self", "start", "code", "leftSquareBracket", "enter", "labelLink", "labelMarker", "consume", "exit", "after", "caret", "parser", "constructs"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/micromark-core-commonmark/dev/lib/label-start-link.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\nimport {labelEnd} from './label-end.js'\n\n/** @type {Construct} */\nexport const labelStartLink = {\n  name: 'labelStartLink',\n  tokenize: tokenizeLabelStartLink,\n  resolveAll: labelEnd.resolveAll\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeLabelStartLink(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of label (link) start.\n   *\n   * ```markdown\n   * > | a [b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    effects.enter(types.labelLink)\n    effects.enter(types.labelMarker)\n    effects.consume(code)\n    effects.exit(types.labelMarker)\n    effects.exit(types.labelLink)\n    return after\n  }\n\n  /** @type {State} */\n  function after(code) {\n    // To do: this isn’t needed in `micromark-extension-gfm-footnote`,\n    // remove.\n    // Hidden footnotes hook.\n    /* c8 ignore next 3 */\n    return code === codes.caret &&\n      '_hiddenFootnoteSupport' in self.parser.constructs\n      ? nok(code)\n      : ok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,KAAK,QAAO,gCAAgC;AACpD,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;AACvC,SAAQC,QAAQ,QAAO,gBAAgB;;AAEvC;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5BC,IAAI,EAAE,gBAAgB;EACtBC,QAAQ,EAAEC,sBAAsB;EAChCC,UAAU,EAAEL,QAAQ,CAACK;AACvB,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASD,sBAAsBA,CAACE,OAAO,EAAER,EAAE,EAAES,GAAG,EAAE;EAChD,MAAMC,IAAI,GAAG,IAAI;EAEjB,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnBX,MAAM,CAACW,IAAI,KAAKd,KAAK,CAACe,iBAAiB,EAAE,cAAc,CAAC;IACxDL,OAAO,CAACM,KAAK,CAACf,KAAK,CAACgB,SAAS,CAAC;IAC9BP,OAAO,CAACM,KAAK,CAACf,KAAK,CAACiB,WAAW,CAAC;IAChCR,OAAO,CAACS,OAAO,CAACL,IAAI,CAAC;IACrBJ,OAAO,CAACU,IAAI,CAACnB,KAAK,CAACiB,WAAW,CAAC;IAC/BR,OAAO,CAACU,IAAI,CAACnB,KAAK,CAACgB,SAAS,CAAC;IAC7B,OAAOI,KAAK;EACd;;EAEA;EACA,SAASA,KAAKA,CAACP,IAAI,EAAE;IACnB;IACA;IACA;IACA;IACA,OAAOA,IAAI,KAAKd,KAAK,CAACsB,KAAK,IACzB,wBAAwB,IAAIV,IAAI,CAACW,MAAM,CAACC,UAAU,GAChDb,GAAG,CAACG,IAAI,CAAC,GACTZ,EAAE,CAACY,IAAI,CAAC;EACd;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}