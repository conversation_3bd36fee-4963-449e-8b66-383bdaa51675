{"ast": null, "code": "const visualElementStore = new WeakMap();\nexport { visualElementStore };", "map": {"version": 3, "names": ["visualElementStore", "WeakMap"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/framer-motion/dist/es/render/store.mjs"], "sourcesContent": ["const visualElementStore = new WeakMap();\n\nexport { visualElementStore };\n"], "mappings": "AAAA,MAAMA,kBAAkB,GAAG,IAAIC,OAAO,CAAC,CAAC;AAExC,SAASD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}