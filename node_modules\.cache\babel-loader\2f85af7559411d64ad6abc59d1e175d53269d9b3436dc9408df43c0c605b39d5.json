{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').InitialConstruct} InitialConstruct\n * @typedef {import('micromark-util-types').Initializer} Initializer\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n */\n\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { constants } from 'micromark-util-symbol/constants.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\nexport const resolver = {\n  resolveAll: createResolver()\n};\nexport const string = initializeFactory('string');\nexport const text = initializeFactory('text');\n\n/**\n * @param {'string' | 'text'} field\n * @returns {InitialConstruct}\n */\nfunction initializeFactory(field) {\n  return {\n    tokenize: initializeText,\n    resolveAll: createResolver(field === 'text' ? resolveAllLineSuffixes : undefined)\n  };\n\n  /**\n   * @this {TokenizeContext}\n   * @type {Initializer}\n   */\n  function initializeText(effects) {\n    const self = this;\n    const constructs = this.parser.constructs[field];\n    const text = effects.attempt(constructs, start, notText);\n    return start;\n\n    /** @type {State} */\n    function start(code) {\n      return atBreak(code) ? text(code) : notText(code);\n    }\n\n    /** @type {State} */\n    function notText(code) {\n      if (code === codes.eof) {\n        effects.consume(code);\n        return;\n      }\n      effects.enter(types.data);\n      effects.consume(code);\n      return data;\n    }\n\n    /** @type {State} */\n    function data(code) {\n      if (atBreak(code)) {\n        effects.exit(types.data);\n        return text(code);\n      }\n\n      // Data.\n      effects.consume(code);\n      return data;\n    }\n\n    /**\n     * @param {Code} code\n     * @returns {boolean}\n     */\n    function atBreak(code) {\n      if (code === codes.eof) {\n        return true;\n      }\n      const list = constructs[code];\n      let index = -1;\n      if (list) {\n        // Always populated by defaults.\n        assert(Array.isArray(list), 'expected `disable.null` to be populated');\n        while (++index < list.length) {\n          const item = list[index];\n          if (!item.previous || item.previous.call(self, self.previous)) {\n            return true;\n          }\n        }\n      }\n      return false;\n    }\n  }\n}\n\n/**\n * @param {Resolver | undefined} [extraResolver]\n * @returns {Resolver}\n */\nfunction createResolver(extraResolver) {\n  return resolveAllText;\n\n  /** @type {Resolver} */\n  function resolveAllText(events, context) {\n    let index = -1;\n    /** @type {number | undefined} */\n    let enter;\n\n    // A rather boring computation (to merge adjacent `data` events) which\n    // improves mm performance by 29%.\n    while (++index <= events.length) {\n      if (enter === undefined) {\n        if (events[index] && events[index][1].type === types.data) {\n          enter = index;\n          index++;\n        }\n      } else if (!events[index] || events[index][1].type !== types.data) {\n        // Don’t do anything if there is one data token.\n        if (index !== enter + 2) {\n          events[enter][1].end = events[index - 1][1].end;\n          events.splice(enter + 2, index - enter - 2);\n          index = enter + 2;\n        }\n        enter = undefined;\n      }\n    }\n    return extraResolver ? extraResolver(events, context) : events;\n  }\n}\n\n/**\n * A rather ugly set of instructions which again looks at chunks in the input\n * stream.\n * The reason to do this here is that it is *much* faster to parse in reverse.\n * And that we can’t hook into `null` to split the line suffix before an EOF.\n * To do: figure out if we can make this into a clean utility, or even in core.\n * As it will be useful for GFMs literal autolink extension (and maybe even\n * tables?)\n *\n * @type {Resolver}\n */\nfunction resolveAllLineSuffixes(events, context) {\n  let eventIndex = 0; // Skip first.\n\n  while (++eventIndex <= events.length) {\n    if ((eventIndex === events.length || events[eventIndex][1].type === types.lineEnding) && events[eventIndex - 1][1].type === types.data) {\n      const data = events[eventIndex - 1][1];\n      const chunks = context.sliceStream(data);\n      let index = chunks.length;\n      let bufferIndex = -1;\n      let size = 0;\n      /** @type {boolean | undefined} */\n      let tabs;\n      while (index--) {\n        const chunk = chunks[index];\n        if (typeof chunk === 'string') {\n          bufferIndex = chunk.length;\n          while (chunk.charCodeAt(bufferIndex - 1) === codes.space) {\n            size++;\n            bufferIndex--;\n          }\n          if (bufferIndex) break;\n          bufferIndex = -1;\n        }\n        // Number\n        else if (chunk === codes.horizontalTab) {\n          tabs = true;\n          size++;\n        } else if (chunk === codes.virtualSpace) {\n          // Empty\n        } else {\n          // Replacement character, exit.\n          index++;\n          break;\n        }\n      }\n      if (size) {\n        const token = {\n          type: eventIndex === events.length || tabs || size < constants.hardBreakPrefixSizeMin ? types.lineSuffix : types.hardBreakTrailing,\n          start: {\n            line: data.end.line,\n            column: data.end.column - size,\n            offset: data.end.offset - size,\n            _index: data.start._index + index,\n            _bufferIndex: index ? bufferIndex : data.start._bufferIndex + bufferIndex\n          },\n          end: Object.assign({}, data.end)\n        };\n        data.end = Object.assign({}, token.start);\n        if (data.start.offset === data.end.offset) {\n          Object.assign(data, token);\n        } else {\n          events.splice(eventIndex, 0, ['enter', token, context], ['exit', token, context]);\n          eventIndex += 2;\n        }\n      }\n      eventIndex++;\n    }\n  }\n  return events;\n}", "map": {"version": 3, "names": ["codes", "constants", "types", "ok", "assert", "resolver", "resolveAll", "createResolver", "string", "initializeFactory", "text", "field", "tokenize", "initializeText", "resolveAllLineSuffixes", "undefined", "effects", "self", "constructs", "parser", "attempt", "start", "notText", "code", "atBreak", "eof", "consume", "enter", "data", "exit", "list", "index", "Array", "isArray", "length", "item", "previous", "call", "extraResolver", "resolveAllText", "events", "context", "type", "end", "splice", "eventIndex", "lineEnding", "chunks", "sliceStream", "bufferIndex", "size", "tabs", "chunk", "charCodeAt", "space", "horizontalTab", "virtualSpace", "token", "hardBreakPrefixSizeMin", "lineSuffix", "hardBreakTrailing", "line", "column", "offset", "_index", "_bufferIndex", "Object", "assign"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/micromark/dev/lib/initialize/text.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').InitialConstruct} InitialConstruct\n * @typedef {import('micromark-util-types').Initializer} Initializer\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n */\n\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\nexport const resolver = {resolveAll: createResolver()}\nexport const string = initializeFactory('string')\nexport const text = initializeFactory('text')\n\n/**\n * @param {'string' | 'text'} field\n * @returns {InitialConstruct}\n */\nfunction initializeFactory(field) {\n  return {\n    tokenize: initializeText,\n    resolveAll: createResolver(\n      field === 'text' ? resolveAllLineSuffixes : undefined\n    )\n  }\n\n  /**\n   * @this {TokenizeContext}\n   * @type {Initializer}\n   */\n  function initializeText(effects) {\n    const self = this\n    const constructs = this.parser.constructs[field]\n    const text = effects.attempt(constructs, start, notText)\n\n    return start\n\n    /** @type {State} */\n    function start(code) {\n      return atBreak(code) ? text(code) : notText(code)\n    }\n\n    /** @type {State} */\n    function notText(code) {\n      if (code === codes.eof) {\n        effects.consume(code)\n        return\n      }\n\n      effects.enter(types.data)\n      effects.consume(code)\n      return data\n    }\n\n    /** @type {State} */\n    function data(code) {\n      if (atBreak(code)) {\n        effects.exit(types.data)\n        return text(code)\n      }\n\n      // Data.\n      effects.consume(code)\n      return data\n    }\n\n    /**\n     * @param {Code} code\n     * @returns {boolean}\n     */\n    function atBreak(code) {\n      if (code === codes.eof) {\n        return true\n      }\n\n      const list = constructs[code]\n      let index = -1\n\n      if (list) {\n        // Always populated by defaults.\n        assert(Array.isArray(list), 'expected `disable.null` to be populated')\n\n        while (++index < list.length) {\n          const item = list[index]\n          if (!item.previous || item.previous.call(self, self.previous)) {\n            return true\n          }\n        }\n      }\n\n      return false\n    }\n  }\n}\n\n/**\n * @param {Resolver | undefined} [extraResolver]\n * @returns {Resolver}\n */\nfunction createResolver(extraResolver) {\n  return resolveAllText\n\n  /** @type {Resolver} */\n  function resolveAllText(events, context) {\n    let index = -1\n    /** @type {number | undefined} */\n    let enter\n\n    // A rather boring computation (to merge adjacent `data` events) which\n    // improves mm performance by 29%.\n    while (++index <= events.length) {\n      if (enter === undefined) {\n        if (events[index] && events[index][1].type === types.data) {\n          enter = index\n          index++\n        }\n      } else if (!events[index] || events[index][1].type !== types.data) {\n        // Don’t do anything if there is one data token.\n        if (index !== enter + 2) {\n          events[enter][1].end = events[index - 1][1].end\n          events.splice(enter + 2, index - enter - 2)\n          index = enter + 2\n        }\n\n        enter = undefined\n      }\n    }\n\n    return extraResolver ? extraResolver(events, context) : events\n  }\n}\n\n/**\n * A rather ugly set of instructions which again looks at chunks in the input\n * stream.\n * The reason to do this here is that it is *much* faster to parse in reverse.\n * And that we can’t hook into `null` to split the line suffix before an EOF.\n * To do: figure out if we can make this into a clean utility, or even in core.\n * As it will be useful for GFMs literal autolink extension (and maybe even\n * tables?)\n *\n * @type {Resolver}\n */\nfunction resolveAllLineSuffixes(events, context) {\n  let eventIndex = 0 // Skip first.\n\n  while (++eventIndex <= events.length) {\n    if (\n      (eventIndex === events.length ||\n        events[eventIndex][1].type === types.lineEnding) &&\n      events[eventIndex - 1][1].type === types.data\n    ) {\n      const data = events[eventIndex - 1][1]\n      const chunks = context.sliceStream(data)\n      let index = chunks.length\n      let bufferIndex = -1\n      let size = 0\n      /** @type {boolean | undefined} */\n      let tabs\n\n      while (index--) {\n        const chunk = chunks[index]\n\n        if (typeof chunk === 'string') {\n          bufferIndex = chunk.length\n\n          while (chunk.charCodeAt(bufferIndex - 1) === codes.space) {\n            size++\n            bufferIndex--\n          }\n\n          if (bufferIndex) break\n          bufferIndex = -1\n        }\n        // Number\n        else if (chunk === codes.horizontalTab) {\n          tabs = true\n          size++\n        } else if (chunk === codes.virtualSpace) {\n          // Empty\n        } else {\n          // Replacement character, exit.\n          index++\n          break\n        }\n      }\n\n      if (size) {\n        const token = {\n          type:\n            eventIndex === events.length ||\n            tabs ||\n            size < constants.hardBreakPrefixSizeMin\n              ? types.lineSuffix\n              : types.hardBreakTrailing,\n          start: {\n            line: data.end.line,\n            column: data.end.column - size,\n            offset: data.end.offset - size,\n            _index: data.start._index + index,\n            _bufferIndex: index\n              ? bufferIndex\n              : data.start._bufferIndex + bufferIndex\n          },\n          end: Object.assign({}, data.end)\n        }\n\n        data.end = Object.assign({}, token.start)\n\n        if (data.start.offset === data.end.offset) {\n          Object.assign(data, token)\n        } else {\n          events.splice(\n            eventIndex,\n            0,\n            ['enter', token, context],\n            ['exit', token, context]\n          )\n          eventIndex += 2\n        }\n      }\n\n      eventIndex++\n    }\n  }\n\n  return events\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,KAAK,QAAO,gCAAgC;AACpD,SAAQC,SAAS,QAAO,oCAAoC;AAC5D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;AAEvC,OAAO,MAAMC,QAAQ,GAAG;EAACC,UAAU,EAAEC,cAAc,CAAC;AAAC,CAAC;AACtD,OAAO,MAAMC,MAAM,GAAGC,iBAAiB,CAAC,QAAQ,CAAC;AACjD,OAAO,MAAMC,IAAI,GAAGD,iBAAiB,CAAC,MAAM,CAAC;;AAE7C;AACA;AACA;AACA;AACA,SAASA,iBAAiBA,CAACE,KAAK,EAAE;EAChC,OAAO;IACLC,QAAQ,EAAEC,cAAc;IACxBP,UAAU,EAAEC,cAAc,CACxBI,KAAK,KAAK,MAAM,GAAGG,sBAAsB,GAAGC,SAC9C;EACF,CAAC;;EAED;AACF;AACA;AACA;EACE,SAASF,cAAcA,CAACG,OAAO,EAAE;IAC/B,MAAMC,IAAI,GAAG,IAAI;IACjB,MAAMC,UAAU,GAAG,IAAI,CAACC,MAAM,CAACD,UAAU,CAACP,KAAK,CAAC;IAChD,MAAMD,IAAI,GAAGM,OAAO,CAACI,OAAO,CAACF,UAAU,EAAEG,KAAK,EAAEC,OAAO,CAAC;IAExD,OAAOD,KAAK;;IAEZ;IACA,SAASA,KAAKA,CAACE,IAAI,EAAE;MACnB,OAAOC,OAAO,CAACD,IAAI,CAAC,GAAGb,IAAI,CAACa,IAAI,CAAC,GAAGD,OAAO,CAACC,IAAI,CAAC;IACnD;;IAEA;IACA,SAASD,OAAOA,CAACC,IAAI,EAAE;MACrB,IAAIA,IAAI,KAAKvB,KAAK,CAACyB,GAAG,EAAE;QACtBT,OAAO,CAACU,OAAO,CAACH,IAAI,CAAC;QACrB;MACF;MAEAP,OAAO,CAACW,KAAK,CAACzB,KAAK,CAAC0B,IAAI,CAAC;MACzBZ,OAAO,CAACU,OAAO,CAACH,IAAI,CAAC;MACrB,OAAOK,IAAI;IACb;;IAEA;IACA,SAASA,IAAIA,CAACL,IAAI,EAAE;MAClB,IAAIC,OAAO,CAACD,IAAI,CAAC,EAAE;QACjBP,OAAO,CAACa,IAAI,CAAC3B,KAAK,CAAC0B,IAAI,CAAC;QACxB,OAAOlB,IAAI,CAACa,IAAI,CAAC;MACnB;;MAEA;MACAP,OAAO,CAACU,OAAO,CAACH,IAAI,CAAC;MACrB,OAAOK,IAAI;IACb;;IAEA;AACJ;AACA;AACA;IACI,SAASJ,OAAOA,CAACD,IAAI,EAAE;MACrB,IAAIA,IAAI,KAAKvB,KAAK,CAACyB,GAAG,EAAE;QACtB,OAAO,IAAI;MACb;MAEA,MAAMK,IAAI,GAAGZ,UAAU,CAACK,IAAI,CAAC;MAC7B,IAAIQ,KAAK,GAAG,CAAC,CAAC;MAEd,IAAID,IAAI,EAAE;QACR;QACA1B,MAAM,CAAC4B,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE,yCAAyC,CAAC;QAEtE,OAAO,EAAEC,KAAK,GAAGD,IAAI,CAACI,MAAM,EAAE;UAC5B,MAAMC,IAAI,GAAGL,IAAI,CAACC,KAAK,CAAC;UACxB,IAAI,CAACI,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACpB,IAAI,EAAEA,IAAI,CAACmB,QAAQ,CAAC,EAAE;YAC7D,OAAO,IAAI;UACb;QACF;MACF;MAEA,OAAO,KAAK;IACd;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA,SAAS7B,cAAcA,CAAC+B,aAAa,EAAE;EACrC,OAAOC,cAAc;;EAErB;EACA,SAASA,cAAcA,CAACC,MAAM,EAAEC,OAAO,EAAE;IACvC,IAAIV,KAAK,GAAG,CAAC,CAAC;IACd;IACA,IAAIJ,KAAK;;IAET;IACA;IACA,OAAO,EAAEI,KAAK,IAAIS,MAAM,CAACN,MAAM,EAAE;MAC/B,IAAIP,KAAK,KAAKZ,SAAS,EAAE;QACvB,IAAIyB,MAAM,CAACT,KAAK,CAAC,IAAIS,MAAM,CAACT,KAAK,CAAC,CAAC,CAAC,CAAC,CAACW,IAAI,KAAKxC,KAAK,CAAC0B,IAAI,EAAE;UACzDD,KAAK,GAAGI,KAAK;UACbA,KAAK,EAAE;QACT;MACF,CAAC,MAAM,IAAI,CAACS,MAAM,CAACT,KAAK,CAAC,IAAIS,MAAM,CAACT,KAAK,CAAC,CAAC,CAAC,CAAC,CAACW,IAAI,KAAKxC,KAAK,CAAC0B,IAAI,EAAE;QACjE;QACA,IAAIG,KAAK,KAAKJ,KAAK,GAAG,CAAC,EAAE;UACvBa,MAAM,CAACb,KAAK,CAAC,CAAC,CAAC,CAAC,CAACgB,GAAG,GAAGH,MAAM,CAACT,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACY,GAAG;UAC/CH,MAAM,CAACI,MAAM,CAACjB,KAAK,GAAG,CAAC,EAAEI,KAAK,GAAGJ,KAAK,GAAG,CAAC,CAAC;UAC3CI,KAAK,GAAGJ,KAAK,GAAG,CAAC;QACnB;QAEAA,KAAK,GAAGZ,SAAS;MACnB;IACF;IAEA,OAAOuB,aAAa,GAAGA,aAAa,CAACE,MAAM,EAAEC,OAAO,CAAC,GAAGD,MAAM;EAChE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS1B,sBAAsBA,CAAC0B,MAAM,EAAEC,OAAO,EAAE;EAC/C,IAAII,UAAU,GAAG,CAAC,EAAC;;EAEnB,OAAO,EAAEA,UAAU,IAAIL,MAAM,CAACN,MAAM,EAAE;IACpC,IACE,CAACW,UAAU,KAAKL,MAAM,CAACN,MAAM,IAC3BM,MAAM,CAACK,UAAU,CAAC,CAAC,CAAC,CAAC,CAACH,IAAI,KAAKxC,KAAK,CAAC4C,UAAU,KACjDN,MAAM,CAACK,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACH,IAAI,KAAKxC,KAAK,CAAC0B,IAAI,EAC7C;MACA,MAAMA,IAAI,GAAGY,MAAM,CAACK,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACtC,MAAME,MAAM,GAAGN,OAAO,CAACO,WAAW,CAACpB,IAAI,CAAC;MACxC,IAAIG,KAAK,GAAGgB,MAAM,CAACb,MAAM;MACzB,IAAIe,WAAW,GAAG,CAAC,CAAC;MACpB,IAAIC,IAAI,GAAG,CAAC;MACZ;MACA,IAAIC,IAAI;MAER,OAAOpB,KAAK,EAAE,EAAE;QACd,MAAMqB,KAAK,GAAGL,MAAM,CAAChB,KAAK,CAAC;QAE3B,IAAI,OAAOqB,KAAK,KAAK,QAAQ,EAAE;UAC7BH,WAAW,GAAGG,KAAK,CAAClB,MAAM;UAE1B,OAAOkB,KAAK,CAACC,UAAU,CAACJ,WAAW,GAAG,CAAC,CAAC,KAAKjD,KAAK,CAACsD,KAAK,EAAE;YACxDJ,IAAI,EAAE;YACND,WAAW,EAAE;UACf;UAEA,IAAIA,WAAW,EAAE;UACjBA,WAAW,GAAG,CAAC,CAAC;QAClB;QACA;QAAA,KACK,IAAIG,KAAK,KAAKpD,KAAK,CAACuD,aAAa,EAAE;UACtCJ,IAAI,GAAG,IAAI;UACXD,IAAI,EAAE;QACR,CAAC,MAAM,IAAIE,KAAK,KAAKpD,KAAK,CAACwD,YAAY,EAAE;UACvC;QAAA,CACD,MAAM;UACL;UACAzB,KAAK,EAAE;UACP;QACF;MACF;MAEA,IAAImB,IAAI,EAAE;QACR,MAAMO,KAAK,GAAG;UACZf,IAAI,EACFG,UAAU,KAAKL,MAAM,CAACN,MAAM,IAC5BiB,IAAI,IACJD,IAAI,GAAGjD,SAAS,CAACyD,sBAAsB,GACnCxD,KAAK,CAACyD,UAAU,GAChBzD,KAAK,CAAC0D,iBAAiB;UAC7BvC,KAAK,EAAE;YACLwC,IAAI,EAAEjC,IAAI,CAACe,GAAG,CAACkB,IAAI;YACnBC,MAAM,EAAElC,IAAI,CAACe,GAAG,CAACmB,MAAM,GAAGZ,IAAI;YAC9Ba,MAAM,EAAEnC,IAAI,CAACe,GAAG,CAACoB,MAAM,GAAGb,IAAI;YAC9Bc,MAAM,EAAEpC,IAAI,CAACP,KAAK,CAAC2C,MAAM,GAAGjC,KAAK;YACjCkC,YAAY,EAAElC,KAAK,GACfkB,WAAW,GACXrB,IAAI,CAACP,KAAK,CAAC4C,YAAY,GAAGhB;UAChC,CAAC;UACDN,GAAG,EAAEuB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEvC,IAAI,CAACe,GAAG;QACjC,CAAC;QAEDf,IAAI,CAACe,GAAG,GAAGuB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEV,KAAK,CAACpC,KAAK,CAAC;QAEzC,IAAIO,IAAI,CAACP,KAAK,CAAC0C,MAAM,KAAKnC,IAAI,CAACe,GAAG,CAACoB,MAAM,EAAE;UACzCG,MAAM,CAACC,MAAM,CAACvC,IAAI,EAAE6B,KAAK,CAAC;QAC5B,CAAC,MAAM;UACLjB,MAAM,CAACI,MAAM,CACXC,UAAU,EACV,CAAC,EACD,CAAC,OAAO,EAAEY,KAAK,EAAEhB,OAAO,CAAC,EACzB,CAAC,MAAM,EAAEgB,KAAK,EAAEhB,OAAO,CACzB,CAAC;UACDI,UAAU,IAAI,CAAC;QACjB;MACF;MAEAA,UAAU,EAAE;IACd;EACF;EAEA,OAAOL,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}