{"ast": null, "code": "/**\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('mdast-util-to-hast').Options} Options\n * @typedef {import('unified').Processor<any, any, any, any>} Processor\n *\n * @typedef {import('mdast-util-to-hast')} DoNotTouchAsThisImportIncludesRawInTree\n */\n\nimport { toHast } from 'mdast-util-to-hast';\n\n// Note: the `<MdastRoot, HastRoot>` overload doesn’t seem to work :'(\n\n/**\n * Plugin that turns markdown into HTML to support rehype.\n *\n * *   If a destination processor is given, that processor runs with a new HTML\n *     (hast) tree (bridge-mode).\n *     As the given processor runs with a hast tree, and rehype plugins support\n *     hast, that means rehype plugins can be used with the given processor.\n *     The hast tree is discarded in the end.\n *     It’s highly unlikely that you want to do this.\n * *   The common case is to not pass a destination processor, in which case the\n *     current processor continues running with a new HTML (hast) tree\n *     (mutate-mode).\n *     As the current processor continues with a hast tree, and rehype plugins\n *     support hast, that means rehype plugins can be used after\n *     `remark-rehype`.\n *     It’s likely that this is what you want to do.\n *\n * @param destination\n *   Optional unified processor.\n * @param options\n *   Options passed to `mdast-util-to-hast`.\n */\nconst remarkRehype = /** @type {(import('unified').Plugin<[Processor, Options?]|[null|undefined, Options?]|[Options]|[], MdastRoot>)} */\n\nfunction (destination, options) {\n  return destination && 'run' in destination ? bridge(destination, options) : mutate(destination || options);\n};\nexport default remarkRehype;\n\n/**\n * Bridge-mode.\n * Runs the destination with the new hast tree.\n *\n * @type {import('unified').Plugin<[Processor, Options?], MdastRoot>}\n */\nfunction bridge(destination, options) {\n  return (node, file, next) => {\n    destination.run(toHast(node, options), file, error => {\n      next(error);\n    });\n  };\n}\n\n/**\n * Mutate-mode.\n * Further plugins run on the hast tree.\n *\n * @type {import('unified').Plugin<[Options?]|void[], MdastRoot, HastRoot>}\n */\nfunction mutate(options) {\n  // @ts-expect-error: assume a corresponding node is returned by `toHast`.\n  return node => toHast(node, options);\n}", "map": {"version": 3, "names": ["toHast", "remarkRehype", "destination", "options", "bridge", "mutate", "node", "file", "next", "run", "error"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/remark-rehype/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('mdast-util-to-hast').Options} Options\n * @typedef {import('unified').Processor<any, any, any, any>} Processor\n *\n * @typedef {import('mdast-util-to-hast')} DoNotTouchAsThisImportIncludesRawInTree\n */\n\nimport {toHast} from 'mdast-util-to-hast'\n\n// Note: the `<MdastRoot, HastRoot>` overload doesn’t seem to work :'(\n\n/**\n * Plugin that turns markdown into HTML to support rehype.\n *\n * *   If a destination processor is given, that processor runs with a new HTML\n *     (hast) tree (bridge-mode).\n *     As the given processor runs with a hast tree, and rehype plugins support\n *     hast, that means rehype plugins can be used with the given processor.\n *     The hast tree is discarded in the end.\n *     It’s highly unlikely that you want to do this.\n * *   The common case is to not pass a destination processor, in which case the\n *     current processor continues running with a new HTML (hast) tree\n *     (mutate-mode).\n *     As the current processor continues with a hast tree, and rehype plugins\n *     support hast, that means rehype plugins can be used after\n *     `remark-rehype`.\n *     It’s likely that this is what you want to do.\n *\n * @param destination\n *   Optional unified processor.\n * @param options\n *   Options passed to `mdast-util-to-hast`.\n */\nconst remarkRehype =\n  /** @type {(import('unified').Plugin<[Processor, Options?]|[null|undefined, Options?]|[Options]|[], MdastRoot>)} */\n  (\n    function (destination, options) {\n      return destination && 'run' in destination\n        ? bridge(destination, options)\n        : mutate(destination || options)\n    }\n  )\n\nexport default remarkRehype\n\n/**\n * Bridge-mode.\n * Runs the destination with the new hast tree.\n *\n * @type {import('unified').Plugin<[Processor, Options?], MdastRoot>}\n */\nfunction bridge(destination, options) {\n  return (node, file, next) => {\n    destination.run(toHast(node, options), file, (error) => {\n      next(error)\n    })\n  }\n}\n\n/**\n * Mutate-mode.\n * Further plugins run on the hast tree.\n *\n * @type {import('unified').Plugin<[Options?]|void[], MdastRoot, HastRoot>}\n */\nfunction mutate(options) {\n  // @ts-expect-error: assume a corresponding node is returned by `toHast`.\n  return (node) => toHast(node, options)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,MAAM,QAAO,oBAAoB;;AAEzC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAChB;;AAEE,SAAAA,CAAUC,WAAW,EAAEC,OAAO,EAAE;EAC9B,OAAOD,WAAW,IAAI,KAAK,IAAIA,WAAW,GACtCE,MAAM,CAACF,WAAW,EAAEC,OAAO,CAAC,GAC5BE,MAAM,CAACH,WAAW,IAAIC,OAAO,CAAC;AACpC,CACD;AAEH,eAAeF,YAAY;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,MAAMA,CAACF,WAAW,EAAEC,OAAO,EAAE;EACpC,OAAO,CAACG,IAAI,EAAEC,IAAI,EAAEC,IAAI,KAAK;IAC3BN,WAAW,CAACO,GAAG,CAACT,MAAM,CAACM,IAAI,EAAEH,OAAO,CAAC,EAAEI,IAAI,EAAGG,KAAK,IAAK;MACtDF,IAAI,CAACE,KAAK,CAAC;IACb,CAAC,CAAC;EACJ,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASL,MAAMA,CAACF,OAAO,EAAE;EACvB;EACA,OAAQG,IAAI,IAAKN,MAAM,CAACM,IAAI,EAAEH,OAAO,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}