{"ast": null, "code": "/**\n * This module is compiled away!\n *\n * Parsing markdown comes with a couple of constants, such as minimum or maximum\n * sizes of certain sequences.\n * Additionally, there are a couple symbols used inside micromark.\n * These are all defined here, but compiled away by scripts.\n */\nexport const constants = /** @type {const} */{\n  attentionSideBefore: 1,\n  // Symbol to mark an attention sequence as before content: `*a`\n  attentionSideAfter: 2,\n  // Symbol to mark an attention sequence as after content: `a*`\n  atxHeadingOpeningFenceSizeMax: 6,\n  // 6 number signs is fine, 7 isn’t.\n  autolinkDomainSizeMax: 63,\n  // 63 characters is fine, 64 is too many.\n  autolinkSchemeSizeMax: 32,\n  // 32 characters is fine, 33 is too many.\n  cdataOpeningString: 'CDATA[',\n  // And preceded by `<![`.\n  characterGroupWhitespace: 1,\n  // Symbol used to indicate a character is whitespace\n  characterGroupPunctuation: 2,\n  // Symbol used to indicate a character is punctuation\n  characterReferenceDecimalSizeMax: 7,\n  // `&#9999999;`.\n  characterReferenceHexadecimalSizeMax: 6,\n  // `&#xff9999;`.\n  characterReferenceNamedSizeMax: 31,\n  // `&CounterClockwiseContourIntegral;`.\n  codeFencedSequenceSizeMin: 3,\n  // At least 3 ticks or tildes are needed.\n  contentTypeDocument: 'document',\n  contentTypeFlow: 'flow',\n  contentTypeContent: 'content',\n  contentTypeString: 'string',\n  contentTypeText: 'text',\n  hardBreakPrefixSizeMin: 2,\n  // At least 2 trailing spaces are needed.\n  htmlRaw: 1,\n  // Symbol for `<script>`\n  htmlComment: 2,\n  // Symbol for `<!---->`\n  htmlInstruction: 3,\n  // Symbol for `<?php?>`\n  htmlDeclaration: 4,\n  // Symbol for `<!doctype>`\n  htmlCdata: 5,\n  // Symbol for `<![CDATA[]]>`\n  htmlBasic: 6,\n  // Symbol for `<div`\n  htmlComplete: 7,\n  // Symbol for `<x>`\n  htmlRawSizeMax: 8,\n  // Length of `textarea`.\n  linkResourceDestinationBalanceMax: 32,\n  // See: <https://spec.commonmark.org/0.30/#link-destination>, <https://github.com/remarkjs/react-markdown/issues/658#issuecomment-984345577>\n  linkReferenceSizeMax: 999,\n  // See: <https://spec.commonmark.org/0.30/#link-label>\n  listItemValueSizeMax: 10,\n  // See: <https://spec.commonmark.org/0.30/#ordered-list-marker>\n  numericBaseDecimal: 10,\n  numericBaseHexadecimal: 0x10,\n  tabSize: 4,\n  // Tabs have a hard-coded size of 4, per CommonMark.\n  thematicBreakMarkerCountMin: 3,\n  // At least 3 asterisks, dashes, or underscores are needed.\n  v8MaxSafeChunkSize: 10000 // V8 (and potentially others) have problems injecting giant arrays into other arrays, hence we operate in chunks.\n};", "map": {"version": 3, "names": ["constants", "attentionSideBefore", "attentionSideAfter", "atxHeadingOpeningFenceSizeMax", "autolinkDomainSizeMax", "autolinkSchemeSizeMax", "cdataOpeningString", "characterGroupWhitespace", "characterGroupPunctuation", "characterReferenceDecimalSizeMax", "characterReferenceHexadecimalSizeMax", "characterReferenceNamedSizeMax", "codeFencedSequenceSizeMin", "contentTypeDocument", "contentTypeFlow", "contentTypeContent", "contentTypeString", "contentTypeText", "hardBreakPrefixSizeMin", "htmlRaw", "htmlComment", "htmlInstruction", "htmlDeclaration", "htmlCdata", "htmlBasic", "htmlComplete", "htmlRawSizeMax", "linkResourceDestinationBalanceMax", "linkReferenceSizeMax", "listItemValueSizeMax", "numericBaseDecimal", "numericBaseHexadecimal", "tabSize", "thematicBreakMarkerCountMin", "v8MaxSafeChunkSize"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/micromark-util-symbol/constants.js"], "sourcesContent": ["/**\n * This module is compiled away!\n *\n * Parsing markdown comes with a couple of constants, such as minimum or maximum\n * sizes of certain sequences.\n * Additionally, there are a couple symbols used inside micromark.\n * These are all defined here, but compiled away by scripts.\n */\nexport const constants = /** @type {const} */ ({\n  attentionSideBefore: 1, // Symbol to mark an attention sequence as before content: `*a`\n  attentionSideAfter: 2, // Symbol to mark an attention sequence as after content: `a*`\n  atxHeadingOpeningFenceSizeMax: 6, // 6 number signs is fine, 7 isn’t.\n  autolinkDomainSizeMax: 63, // 63 characters is fine, 64 is too many.\n  autolinkSchemeSizeMax: 32, // 32 characters is fine, 33 is too many.\n  cdataOpeningString: 'CDATA[', // And preceded by `<![`.\n  characterGroupWhitespace: 1, // Symbol used to indicate a character is whitespace\n  characterGroupPunctuation: 2, // Symbol used to indicate a character is punctuation\n  characterReferenceDecimalSizeMax: 7, // `&#9999999;`.\n  characterReferenceHexadecimalSizeMax: 6, // `&#xff9999;`.\n  characterReferenceNamedSizeMax: 31, // `&CounterClockwiseContourIntegral;`.\n  codeFencedSequenceSizeMin: 3, // At least 3 ticks or tildes are needed.\n  contentTypeDocument: 'document',\n  contentTypeFlow: 'flow',\n  contentTypeContent: 'content',\n  contentTypeString: 'string',\n  contentTypeText: 'text',\n  hardBreakPrefixSizeMin: 2, // At least 2 trailing spaces are needed.\n  htmlRaw: 1, // Symbol for `<script>`\n  htmlComment: 2, // Symbol for `<!---->`\n  htmlInstruction: 3, // Symbol for `<?php?>`\n  htmlDeclaration: 4, // Symbol for `<!doctype>`\n  htmlCdata: 5, // Symbol for `<![CDATA[]]>`\n  htmlBasic: 6, // Symbol for `<div`\n  htmlComplete: 7, // Symbol for `<x>`\n  htmlRawSizeMax: 8, // Length of `textarea`.\n  linkResourceDestinationBalanceMax: 32, // See: <https://spec.commonmark.org/0.30/#link-destination>, <https://github.com/remarkjs/react-markdown/issues/658#issuecomment-984345577>\n  linkReferenceSizeMax: 999, // See: <https://spec.commonmark.org/0.30/#link-label>\n  listItemValueSizeMax: 10, // See: <https://spec.commonmark.org/0.30/#ordered-list-marker>\n  numericBaseDecimal: 10,\n  numericBaseHexadecimal: 0x10,\n  tabSize: 4, // Tabs have a hard-coded size of 4, per CommonMark.\n  thematicBreakMarkerCountMin: 3, // At least 3 asterisks, dashes, or underscores are needed.\n  v8MaxSafeChunkSize: 10000 // V8 (and potentially others) have problems injecting giant arrays into other arrays, hence we operate in chunks.\n})\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,SAAS,GAAG,oBAAsB;EAC7CC,mBAAmB,EAAE,CAAC;EAAE;EACxBC,kBAAkB,EAAE,CAAC;EAAE;EACvBC,6BAA6B,EAAE,CAAC;EAAE;EAClCC,qBAAqB,EAAE,EAAE;EAAE;EAC3BC,qBAAqB,EAAE,EAAE;EAAE;EAC3BC,kBAAkB,EAAE,QAAQ;EAAE;EAC9BC,wBAAwB,EAAE,CAAC;EAAE;EAC7BC,yBAAyB,EAAE,CAAC;EAAE;EAC9BC,gCAAgC,EAAE,CAAC;EAAE;EACrCC,oCAAoC,EAAE,CAAC;EAAE;EACzCC,8BAA8B,EAAE,EAAE;EAAE;EACpCC,yBAAyB,EAAE,CAAC;EAAE;EAC9BC,mBAAmB,EAAE,UAAU;EAC/BC,eAAe,EAAE,MAAM;EACvBC,kBAAkB,EAAE,SAAS;EAC7BC,iBAAiB,EAAE,QAAQ;EAC3BC,eAAe,EAAE,MAAM;EACvBC,sBAAsB,EAAE,CAAC;EAAE;EAC3BC,OAAO,EAAE,CAAC;EAAE;EACZC,WAAW,EAAE,CAAC;EAAE;EAChBC,eAAe,EAAE,CAAC;EAAE;EACpBC,eAAe,EAAE,CAAC;EAAE;EACpBC,SAAS,EAAE,CAAC;EAAE;EACdC,SAAS,EAAE,CAAC;EAAE;EACdC,YAAY,EAAE,CAAC;EAAE;EACjBC,cAAc,EAAE,CAAC;EAAE;EACnBC,iCAAiC,EAAE,EAAE;EAAE;EACvCC,oBAAoB,EAAE,GAAG;EAAE;EAC3BC,oBAAoB,EAAE,EAAE;EAAE;EAC1BC,kBAAkB,EAAE,EAAE;EACtBC,sBAAsB,EAAE,IAAI;EAC5BC,OAAO,EAAE,CAAC;EAAE;EACZC,2BAA2B,EAAE,CAAC;EAAE;EAChCC,kBAAkB,EAAE,KAAK,CAAC;AAC5B,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}