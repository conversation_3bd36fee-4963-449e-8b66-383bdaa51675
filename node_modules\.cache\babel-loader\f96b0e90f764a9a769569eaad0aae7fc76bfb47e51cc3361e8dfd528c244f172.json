{"ast": null, "code": "import { invariant } from '../../utils/errors.mjs';\nimport { setValues } from '../../render/utils/setters.mjs';\nimport { animateVisualElement } from '../interfaces/visual-element.mjs';\nfunction stopAnimation(visualElement) {\n  visualElement.values.forEach(value => value.stop());\n}\n/**\n * @public\n */\nfunction animationControls() {\n  /**\n   * Track whether the host component has mounted.\n   */\n  let hasMounted = false;\n  /**\n   * A collection of linked component animation controls.\n   */\n  const subscribers = new Set();\n  const controls = {\n    subscribe(visualElement) {\n      subscribers.add(visualElement);\n      return () => void subscribers.delete(visualElement);\n    },\n    start(definition, transitionOverride) {\n      invariant(hasMounted, \"controls.start() should only be called after a component has mounted. Consider calling within a useEffect hook.\");\n      const animations = [];\n      subscribers.forEach(visualElement => {\n        animations.push(animateVisualElement(visualElement, definition, {\n          transitionOverride\n        }));\n      });\n      return Promise.all(animations);\n    },\n    set(definition) {\n      invariant(hasMounted, \"controls.set() should only be called after a component has mounted. Consider calling within a useEffect hook.\");\n      return subscribers.forEach(visualElement => {\n        setValues(visualElement, definition);\n      });\n    },\n    stop() {\n      subscribers.forEach(visualElement => {\n        stopAnimation(visualElement);\n      });\n    },\n    mount() {\n      hasMounted = true;\n      return () => {\n        hasMounted = false;\n        controls.stop();\n      };\n    }\n  };\n  return controls;\n}\nexport { animationControls };", "map": {"version": 3, "names": ["invariant", "set<PERSON><PERSON><PERSON>", "animateVisualElement", "stopAnimation", "visualElement", "values", "for<PERSON>ach", "value", "stop", "animationControls", "hasMounted", "subscribers", "Set", "controls", "subscribe", "add", "delete", "start", "definition", "transitionOverride", "animations", "push", "Promise", "all", "set", "mount"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/framer-motion/dist/es/animation/hooks/animation-controls.mjs"], "sourcesContent": ["import { invariant } from '../../utils/errors.mjs';\nimport { setValues } from '../../render/utils/setters.mjs';\nimport { animateVisualElement } from '../interfaces/visual-element.mjs';\n\nfunction stopAnimation(visualElement) {\n    visualElement.values.forEach((value) => value.stop());\n}\n/**\n * @public\n */\nfunction animationControls() {\n    /**\n     * Track whether the host component has mounted.\n     */\n    let hasMounted = false;\n    /**\n     * A collection of linked component animation controls.\n     */\n    const subscribers = new Set();\n    const controls = {\n        subscribe(visualElement) {\n            subscribers.add(visualElement);\n            return () => void subscribers.delete(visualElement);\n        },\n        start(definition, transitionOverride) {\n            invariant(hasMounted, \"controls.start() should only be called after a component has mounted. Consider calling within a useEffect hook.\");\n            const animations = [];\n            subscribers.forEach((visualElement) => {\n                animations.push(animateVisualElement(visualElement, definition, {\n                    transitionOverride,\n                }));\n            });\n            return Promise.all(animations);\n        },\n        set(definition) {\n            invariant(hasMounted, \"controls.set() should only be called after a component has mounted. Consider calling within a useEffect hook.\");\n            return subscribers.forEach((visualElement) => {\n                setValues(visualElement, definition);\n            });\n        },\n        stop() {\n            subscribers.forEach((visualElement) => {\n                stopAnimation(visualElement);\n            });\n        },\n        mount() {\n            hasMounted = true;\n            return () => {\n                hasMounted = false;\n                controls.stop();\n            };\n        },\n    };\n    return controls;\n}\n\nexport { animationControls };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,wBAAwB;AAClD,SAASC,SAAS,QAAQ,gCAAgC;AAC1D,SAASC,oBAAoB,QAAQ,kCAAkC;AAEvE,SAASC,aAAaA,CAACC,aAAa,EAAE;EAClCA,aAAa,CAACC,MAAM,CAACC,OAAO,CAAEC,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;AACzD;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAAA,EAAG;EACzB;AACJ;AACA;EACI,IAAIC,UAAU,GAAG,KAAK;EACtB;AACJ;AACA;EACI,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAG;IACbC,SAASA,CAACV,aAAa,EAAE;MACrBO,WAAW,CAACI,GAAG,CAACX,aAAa,CAAC;MAC9B,OAAO,MAAM,KAAKO,WAAW,CAACK,MAAM,CAACZ,aAAa,CAAC;IACvD,CAAC;IACDa,KAAKA,CAACC,UAAU,EAAEC,kBAAkB,EAAE;MAClCnB,SAAS,CAACU,UAAU,EAAE,iHAAiH,CAAC;MACxI,MAAMU,UAAU,GAAG,EAAE;MACrBT,WAAW,CAACL,OAAO,CAAEF,aAAa,IAAK;QACnCgB,UAAU,CAACC,IAAI,CAACnB,oBAAoB,CAACE,aAAa,EAAEc,UAAU,EAAE;UAC5DC;QACJ,CAAC,CAAC,CAAC;MACP,CAAC,CAAC;MACF,OAAOG,OAAO,CAACC,GAAG,CAACH,UAAU,CAAC;IAClC,CAAC;IACDI,GAAGA,CAACN,UAAU,EAAE;MACZlB,SAAS,CAACU,UAAU,EAAE,+GAA+G,CAAC;MACtI,OAAOC,WAAW,CAACL,OAAO,CAAEF,aAAa,IAAK;QAC1CH,SAAS,CAACG,aAAa,EAAEc,UAAU,CAAC;MACxC,CAAC,CAAC;IACN,CAAC;IACDV,IAAIA,CAAA,EAAG;MACHG,WAAW,CAACL,OAAO,CAAEF,aAAa,IAAK;QACnCD,aAAa,CAACC,aAAa,CAAC;MAChC,CAAC,CAAC;IACN,CAAC;IACDqB,KAAKA,CAAA,EAAG;MACJf,UAAU,GAAG,IAAI;MACjB,OAAO,MAAM;QACTA,UAAU,GAAG,KAAK;QAClBG,QAAQ,CAACL,IAAI,CAAC,CAAC;MACnB,CAAC;IACL;EACJ,CAAC;EACD,OAAOK,QAAQ;AACnB;AAEA,SAASJ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}