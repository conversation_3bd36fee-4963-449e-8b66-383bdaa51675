{"ast": null, "code": "import { asciiAlphanumeric } from 'micromark-util-character';\nimport { encode } from 'micromark-util-encode';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { values } from 'micromark-util-symbol/values.js';\n\n/**\n * Make a value safe for injection as a URL.\n *\n * This encodes unsafe characters with percent-encoding and skips already\n * encoded sequences (see `normalizeUri`).\n * Further unsafe characters are encoded as character references (see\n * `micromark-util-encode`).\n *\n * A regex of allowed protocols can be given, in which case the URL is\n * sanitized.\n * For example, `/^(https?|ircs?|mailto|xmpp)$/i` can be used for `a[href]`, or\n * `/^https?$/i` for `img[src]` (this is what `github.com` allows).\n * If the URL includes an unknown protocol (one not matched by `protocol`, such\n * as a dangerous example, `javascript:`), the value is ignored.\n *\n * @param {string | undefined} url\n *   URI to sanitize.\n * @param {RegExp | null | undefined} [protocol]\n *   Allowed protocols.\n * @returns {string}\n *   Sanitized URI.\n */\nexport function sanitizeUri(url, protocol) {\n  const value = encode(normalizeUri(url || ''));\n  if (!protocol) {\n    return value;\n  }\n  const colon = value.indexOf(':');\n  const questionMark = value.indexOf('?');\n  const numberSign = value.indexOf('#');\n  const slash = value.indexOf('/');\n  if (\n  // If there is no protocol, it’s relative.\n  colon < 0 ||\n  // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n  slash > -1 && colon > slash || questionMark > -1 && colon > questionMark || numberSign > -1 && colon > numberSign ||\n  // It is a protocol, it should be allowed.\n  protocol.test(value.slice(0, colon))) {\n    return value;\n  }\n  return '';\n}\n\n/**\n * Normalize a URL.\n *\n * Encode unsafe characters with percent-encoding, skipping already encoded\n * sequences.\n *\n * @param {string} value\n *   URI to normalize.\n * @returns {string}\n *   Normalized URI.\n */\nexport function normalizeUri(value) {\n  /** @type {Array<string>} */\n  const result = [];\n  let index = -1;\n  let start = 0;\n  let skip = 0;\n  while (++index < value.length) {\n    const code = value.charCodeAt(index);\n    /** @type {string} */\n    let replace = '';\n\n    // A correct percent encoded value.\n    if (code === codes.percentSign && asciiAlphanumeric(value.charCodeAt(index + 1)) && asciiAlphanumeric(value.charCodeAt(index + 2))) {\n      skip = 2;\n    }\n    // ASCII.\n    else if (code < 128) {\n      if (!/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(code))) {\n        replace = String.fromCharCode(code);\n      }\n    }\n    // Astral.\n    else if (code > 55295 && code < 57344) {\n      const next = value.charCodeAt(index + 1);\n\n      // A correct surrogate pair.\n      if (code < 56320 && next > 56319 && next < 57344) {\n        replace = String.fromCharCode(code, next);\n        skip = 1;\n      }\n      // Lone surrogate.\n      else {\n        replace = values.replacementCharacter;\n      }\n    }\n    // Unicode.\n    else {\n      replace = String.fromCharCode(code);\n    }\n    if (replace) {\n      result.push(value.slice(start, index), encodeURIComponent(replace));\n      start = index + skip + 1;\n      replace = '';\n    }\n    if (skip) {\n      index += skip;\n      skip = 0;\n    }\n  }\n  return result.join('') + value.slice(start);\n}", "map": {"version": 3, "names": ["asciiAlphanumeric", "encode", "codes", "values", "sanitizeUri", "url", "protocol", "value", "normalizeUri", "colon", "indexOf", "questionMark", "numberSign", "slash", "test", "slice", "result", "index", "start", "skip", "length", "code", "charCodeAt", "replace", "percentSign", "String", "fromCharCode", "next", "replacementCharacter", "push", "encodeURIComponent", "join"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/micromark-util-sanitize-uri/dev/index.js"], "sourcesContent": ["import {asciiAlphanumeric} from 'micromark-util-character'\nimport {encode} from 'micromark-util-encode'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {values} from 'micromark-util-symbol/values.js'\n\n/**\n * Make a value safe for injection as a URL.\n *\n * This encodes unsafe characters with percent-encoding and skips already\n * encoded sequences (see `normalizeUri`).\n * Further unsafe characters are encoded as character references (see\n * `micromark-util-encode`).\n *\n * A regex of allowed protocols can be given, in which case the URL is\n * sanitized.\n * For example, `/^(https?|ircs?|mailto|xmpp)$/i` can be used for `a[href]`, or\n * `/^https?$/i` for `img[src]` (this is what `github.com` allows).\n * If the URL includes an unknown protocol (one not matched by `protocol`, such\n * as a dangerous example, `javascript:`), the value is ignored.\n *\n * @param {string | undefined} url\n *   URI to sanitize.\n * @param {RegExp | null | undefined} [protocol]\n *   Allowed protocols.\n * @returns {string}\n *   Sanitized URI.\n */\nexport function sanitizeUri(url, protocol) {\n  const value = encode(normalizeUri(url || ''))\n\n  if (!protocol) {\n    return value\n  }\n\n  const colon = value.indexOf(':')\n  const questionMark = value.indexOf('?')\n  const numberSign = value.indexOf('#')\n  const slash = value.indexOf('/')\n\n  if (\n    // If there is no protocol, it’s relative.\n    colon < 0 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash > -1 && colon > slash) ||\n    (questionMark > -1 && colon > questionMark) ||\n    (numberSign > -1 && colon > numberSign) ||\n    // It is a protocol, it should be allowed.\n    protocol.test(value.slice(0, colon))\n  ) {\n    return value\n  }\n\n  return ''\n}\n\n/**\n * Normalize a URL.\n *\n * Encode unsafe characters with percent-encoding, skipping already encoded\n * sequences.\n *\n * @param {string} value\n *   URI to normalize.\n * @returns {string}\n *   Normalized URI.\n */\nexport function normalizeUri(value) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n  let start = 0\n  let skip = 0\n\n  while (++index < value.length) {\n    const code = value.charCodeAt(index)\n    /** @type {string} */\n    let replace = ''\n\n    // A correct percent encoded value.\n    if (\n      code === codes.percentSign &&\n      asciiAlphanumeric(value.charCodeAt(index + 1)) &&\n      asciiAlphanumeric(value.charCodeAt(index + 2))\n    ) {\n      skip = 2\n    }\n    // ASCII.\n    else if (code < 128) {\n      if (!/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(code))) {\n        replace = String.fromCharCode(code)\n      }\n    }\n    // Astral.\n    else if (code > 55295 && code < 57344) {\n      const next = value.charCodeAt(index + 1)\n\n      // A correct surrogate pair.\n      if (code < 56320 && next > 56319 && next < 57344) {\n        replace = String.fromCharCode(code, next)\n        skip = 1\n      }\n      // Lone surrogate.\n      else {\n        replace = values.replacementCharacter\n      }\n    }\n    // Unicode.\n    else {\n      replace = String.fromCharCode(code)\n    }\n\n    if (replace) {\n      result.push(value.slice(start, index), encodeURIComponent(replace))\n      start = index + skip + 1\n      replace = ''\n    }\n\n    if (skip) {\n      index += skip\n      skip = 0\n    }\n  }\n\n  return result.join('') + value.slice(start)\n}\n"], "mappings": "AAAA,SAAQA,iBAAiB,QAAO,0BAA0B;AAC1D,SAAQC,MAAM,QAAO,uBAAuB;AAC5C,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,MAAM,QAAO,iCAAiC;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,GAAG,EAAEC,QAAQ,EAAE;EACzC,MAAMC,KAAK,GAAGN,MAAM,CAACO,YAAY,CAACH,GAAG,IAAI,EAAE,CAAC,CAAC;EAE7C,IAAI,CAACC,QAAQ,EAAE;IACb,OAAOC,KAAK;EACd;EAEA,MAAME,KAAK,GAAGF,KAAK,CAACG,OAAO,CAAC,GAAG,CAAC;EAChC,MAAMC,YAAY,GAAGJ,KAAK,CAACG,OAAO,CAAC,GAAG,CAAC;EACvC,MAAME,UAAU,GAAGL,KAAK,CAACG,OAAO,CAAC,GAAG,CAAC;EACrC,MAAMG,KAAK,GAAGN,KAAK,CAACG,OAAO,CAAC,GAAG,CAAC;EAEhC;EACE;EACAD,KAAK,GAAG,CAAC;EACT;EACCI,KAAK,GAAG,CAAC,CAAC,IAAIJ,KAAK,GAAGI,KAAM,IAC5BF,YAAY,GAAG,CAAC,CAAC,IAAIF,KAAK,GAAGE,YAAa,IAC1CC,UAAU,GAAG,CAAC,CAAC,IAAIH,KAAK,GAAGG,UAAW;EACvC;EACAN,QAAQ,CAACQ,IAAI,CAACP,KAAK,CAACQ,KAAK,CAAC,CAAC,EAAEN,KAAK,CAAC,CAAC,EACpC;IACA,OAAOF,KAAK;EACd;EAEA,OAAO,EAAE;AACX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACD,KAAK,EAAE;EAClC;EACA,MAAMS,MAAM,GAAG,EAAE;EACjB,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,IAAI,GAAG,CAAC;EAEZ,OAAO,EAAEF,KAAK,GAAGV,KAAK,CAACa,MAAM,EAAE;IAC7B,MAAMC,IAAI,GAAGd,KAAK,CAACe,UAAU,CAACL,KAAK,CAAC;IACpC;IACA,IAAIM,OAAO,GAAG,EAAE;;IAEhB;IACA,IACEF,IAAI,KAAKnB,KAAK,CAACsB,WAAW,IAC1BxB,iBAAiB,CAACO,KAAK,CAACe,UAAU,CAACL,KAAK,GAAG,CAAC,CAAC,CAAC,IAC9CjB,iBAAiB,CAACO,KAAK,CAACe,UAAU,CAACL,KAAK,GAAG,CAAC,CAAC,CAAC,EAC9C;MACAE,IAAI,GAAG,CAAC;IACV;IACA;IAAA,KACK,IAAIE,IAAI,GAAG,GAAG,EAAE;MACnB,IAAI,CAAC,mBAAmB,CAACP,IAAI,CAACW,MAAM,CAACC,YAAY,CAACL,IAAI,CAAC,CAAC,EAAE;QACxDE,OAAO,GAAGE,MAAM,CAACC,YAAY,CAACL,IAAI,CAAC;MACrC;IACF;IACA;IAAA,KACK,IAAIA,IAAI,GAAG,KAAK,IAAIA,IAAI,GAAG,KAAK,EAAE;MACrC,MAAMM,IAAI,GAAGpB,KAAK,CAACe,UAAU,CAACL,KAAK,GAAG,CAAC,CAAC;;MAExC;MACA,IAAII,IAAI,GAAG,KAAK,IAAIM,IAAI,GAAG,KAAK,IAAIA,IAAI,GAAG,KAAK,EAAE;QAChDJ,OAAO,GAAGE,MAAM,CAACC,YAAY,CAACL,IAAI,EAAEM,IAAI,CAAC;QACzCR,IAAI,GAAG,CAAC;MACV;MACA;MAAA,KACK;QACHI,OAAO,GAAGpB,MAAM,CAACyB,oBAAoB;MACvC;IACF;IACA;IAAA,KACK;MACHL,OAAO,GAAGE,MAAM,CAACC,YAAY,CAACL,IAAI,CAAC;IACrC;IAEA,IAAIE,OAAO,EAAE;MACXP,MAAM,CAACa,IAAI,CAACtB,KAAK,CAACQ,KAAK,CAACG,KAAK,EAAED,KAAK,CAAC,EAAEa,kBAAkB,CAACP,OAAO,CAAC,CAAC;MACnEL,KAAK,GAAGD,KAAK,GAAGE,IAAI,GAAG,CAAC;MACxBI,OAAO,GAAG,EAAE;IACd;IAEA,IAAIJ,IAAI,EAAE;MACRF,KAAK,IAAIE,IAAI;MACbA,IAAI,GAAG,CAAC;IACV;EACF;EAEA,OAAOH,MAAM,CAACe,IAAI,CAAC,EAAE,CAAC,GAAGxB,KAAK,CAACQ,KAAK,CAACG,KAAK,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}