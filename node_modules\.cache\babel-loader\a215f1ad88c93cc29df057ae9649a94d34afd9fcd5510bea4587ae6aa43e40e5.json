{"ast": null, "code": "/**\n * @typedef {import('./schema.js').Properties} Properties\n * @typedef {import('./schema.js').Normal} Normal\n */\n\nimport { Schema } from './schema.js';\n\n/**\n * @param {Schema[]} definitions\n * @param {string} [space]\n * @returns {Schema}\n */\nexport function merge(definitions, space) {\n  /** @type {Properties} */\n  const property = {};\n  /** @type {Normal} */\n  const normal = {};\n  let index = -1;\n  while (++index < definitions.length) {\n    Object.assign(property, definitions[index].property);\n    Object.assign(normal, definitions[index].normal);\n  }\n  return new Schema(property, normal, space);\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "merge", "definitions", "space", "property", "normal", "index", "length", "Object", "assign"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/property-information/lib/util/merge.js"], "sourcesContent": ["/**\n * @typedef {import('./schema.js').Properties} Properties\n * @typedef {import('./schema.js').Normal} Normal\n */\n\nimport {Schema} from './schema.js'\n\n/**\n * @param {Schema[]} definitions\n * @param {string} [space]\n * @returns {Schema}\n */\nexport function merge(definitions, space) {\n  /** @type {Properties} */\n  const property = {}\n  /** @type {Normal} */\n  const normal = {}\n  let index = -1\n\n  while (++index < definitions.length) {\n    Object.assign(property, definitions[index].property)\n    Object.assign(normal, definitions[index].normal)\n  }\n\n  return new Schema(property, normal, space)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAAQA,MAAM,QAAO,aAAa;;AAElC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAACC,WAAW,EAAEC,KAAK,EAAE;EACxC;EACA,MAAMC,QAAQ,GAAG,CAAC,CAAC;EACnB;EACA,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,IAAIC,KAAK,GAAG,CAAC,CAAC;EAEd,OAAO,EAAEA,KAAK,GAAGJ,WAAW,CAACK,MAAM,EAAE;IACnCC,MAAM,CAACC,MAAM,CAACL,QAAQ,EAAEF,WAAW,CAACI,KAAK,CAAC,CAACF,QAAQ,CAAC;IACpDI,MAAM,CAACC,MAAM,CAACJ,MAAM,EAAEH,WAAW,CAACI,KAAK,CAAC,CAACD,MAAM,CAAC;EAClD;EAEA,OAAO,IAAIL,MAAM,CAACI,QAAQ,EAAEC,MAAM,EAAEF,KAAK,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}