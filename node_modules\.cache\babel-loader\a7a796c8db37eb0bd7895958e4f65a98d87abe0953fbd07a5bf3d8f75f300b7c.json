{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoice-extracting-agent\\\\src\\\\components\\\\BackgroundAnimation.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BackgroundAnimation = () => {\n  _s();\n  const canvasRef = useRef(null);\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n    const ctx = canvas.getContext('2d');\n    let animationFrameId;\n    let particles = [];\n\n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Particle class\n    class Particle {\n      constructor() {\n        this.x = Math.random() * canvas.width;\n        this.y = Math.random() * canvas.height;\n        this.size = Math.random() * 3 + 1;\n        this.speedX = (Math.random() - 0.5) * 0.5;\n        this.speedY = (Math.random() - 0.5) * 0.5;\n        this.opacity = Math.random() * 0.5 + 0.1;\n        this.color = `rgba(14, 165, 233, ${this.opacity})`;\n      }\n      update() {\n        this.x += this.speedX;\n        this.y += this.speedY;\n\n        // Wrap around edges\n        if (this.x > canvas.width) this.x = 0;\n        if (this.x < 0) this.x = canvas.width;\n        if (this.y > canvas.height) this.y = 0;\n        if (this.y < 0) this.y = canvas.height;\n\n        // Pulse opacity\n        this.opacity += (Math.random() - 0.5) * 0.01;\n        this.opacity = Math.max(0.05, Math.min(0.5, this.opacity));\n        this.color = `rgba(14, 165, 233, ${this.opacity})`;\n      }\n      draw() {\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);\n        ctx.fillStyle = this.color;\n        ctx.fill();\n      }\n    }\n\n    // Initialize particles\n    const initParticles = () => {\n      particles = [];\n      const particleCount = Math.floor(canvas.width * canvas.height / 15000);\n      for (let i = 0; i < particleCount; i++) {\n        particles.push(new Particle());\n      }\n    };\n    initParticles();\n\n    // Animation loop\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      // Update and draw particles\n      particles.forEach(particle => {\n        particle.update();\n        particle.draw();\n      });\n\n      // Draw connections between nearby particles\n      particles.forEach((particle, i) => {\n        particles.slice(i + 1).forEach(otherParticle => {\n          const dx = particle.x - otherParticle.x;\n          const dy = particle.y - otherParticle.y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n          if (distance < 100) {\n            ctx.beginPath();\n            ctx.moveTo(particle.x, particle.y);\n            ctx.lineTo(otherParticle.x, otherParticle.y);\n            ctx.strokeStyle = `rgba(14, 165, 233, ${0.1 * (1 - distance / 100)})`;\n            ctx.lineWidth = 0.5;\n            ctx.stroke();\n          }\n        });\n      });\n      animationFrameId = requestAnimationFrame(animate);\n    };\n    animate();\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      cancelAnimationFrame(animationFrameId);\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"canvas\", {\n      ref: canvasRef,\n      className: \"fixed inset-0 pointer-events-none z-0\",\n      style: {\n        background: 'transparent'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 pointer-events-none z-0\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute w-96 h-96 rounded-full opacity-10\",\n        style: {\n          background: 'radial-gradient(circle, rgba(14, 165, 233, 0.3) 0%, transparent 70%)',\n          top: '10%',\n          left: '10%'\n        },\n        animate: {\n          scale: [1, 1.2, 1],\n          opacity: [0.1, 0.2, 0.1]\n        },\n        transition: {\n          duration: 8,\n          repeat: Infinity,\n          ease: \"easeInOut\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute w-64 h-64 rounded-full opacity-10\",\n        style: {\n          background: 'radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%)',\n          top: '60%',\n          right: '10%'\n        },\n        animate: {\n          scale: [1.2, 1, 1.2],\n          opacity: [0.1, 0.15, 0.1]\n        },\n        transition: {\n          duration: 6,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n          delay: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute w-48 h-48 rounded-full opacity-10\",\n        style: {\n          background: 'radial-gradient(circle, rgba(168, 85, 247, 0.3) 0%, transparent 70%)',\n          bottom: '20%',\n          left: '30%'\n        },\n        animate: {\n          scale: [1, 1.3, 1],\n          opacity: [0.1, 0.2, 0.1]\n        },\n        transition: {\n          duration: 10,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n          delay: 4\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), [...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute w-2 h-2 bg-primary-500 rounded-full opacity-20\",\n        style: {\n          top: `${Math.random() * 100}%`,\n          left: `${Math.random() * 100}%`\n        },\n        animate: {\n          y: [0, -30, 0],\n          opacity: [0.2, 0.5, 0.2]\n        },\n        transition: {\n          duration: 4 + Math.random() * 4,\n          repeat: Infinity,\n          ease: \"easeInOut\",\n          delay: Math.random() * 2\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(BackgroundAnimation, \"UJgi7ynoup7eqypjnwyX/s32POg=\");\n_c = BackgroundAnimation;\nexport default BackgroundAnimation;\nvar _c;\n$RefreshReg$(_c, \"BackgroundAnimation\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "motion", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BackgroundAnimation", "_s", "canvasRef", "canvas", "current", "ctx", "getContext", "animationFrameId", "particles", "resizeCanvas", "width", "window", "innerWidth", "height", "innerHeight", "addEventListener", "Particle", "constructor", "x", "Math", "random", "y", "size", "speedX", "speedY", "opacity", "color", "update", "max", "min", "draw", "beginPath", "arc", "PI", "fillStyle", "fill", "initParticles", "particleCount", "floor", "i", "push", "animate", "clearRect", "for<PERSON>ach", "particle", "slice", "otherParticle", "dx", "dy", "distance", "sqrt", "moveTo", "lineTo", "strokeStyle", "lineWidth", "stroke", "requestAnimationFrame", "removeEventListener", "cancelAnimationFrame", "children", "ref", "className", "style", "background", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "top", "left", "scale", "transition", "duration", "repeat", "Infinity", "ease", "right", "delay", "bottom", "Array", "map", "_", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/src/components/BackgroundAnimation.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\n\nconst BackgroundAnimation = () => {\n  const canvasRef = useRef(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    let animationFrameId;\n    let particles = [];\n\n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Particle class\n    class Particle {\n      constructor() {\n        this.x = Math.random() * canvas.width;\n        this.y = Math.random() * canvas.height;\n        this.size = Math.random() * 3 + 1;\n        this.speedX = (Math.random() - 0.5) * 0.5;\n        this.speedY = (Math.random() - 0.5) * 0.5;\n        this.opacity = Math.random() * 0.5 + 0.1;\n        this.color = `rgba(14, 165, 233, ${this.opacity})`;\n      }\n\n      update() {\n        this.x += this.speedX;\n        this.y += this.speedY;\n\n        // Wrap around edges\n        if (this.x > canvas.width) this.x = 0;\n        if (this.x < 0) this.x = canvas.width;\n        if (this.y > canvas.height) this.y = 0;\n        if (this.y < 0) this.y = canvas.height;\n\n        // Pulse opacity\n        this.opacity += (Math.random() - 0.5) * 0.01;\n        this.opacity = Math.max(0.05, Math.min(0.5, this.opacity));\n        this.color = `rgba(14, 165, 233, ${this.opacity})`;\n      }\n\n      draw() {\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);\n        ctx.fillStyle = this.color;\n        ctx.fill();\n      }\n    }\n\n    // Initialize particles\n    const initParticles = () => {\n      particles = [];\n      const particleCount = Math.floor((canvas.width * canvas.height) / 15000);\n      for (let i = 0; i < particleCount; i++) {\n        particles.push(new Particle());\n      }\n    };\n\n    initParticles();\n\n    // Animation loop\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      // Update and draw particles\n      particles.forEach(particle => {\n        particle.update();\n        particle.draw();\n      });\n\n      // Draw connections between nearby particles\n      particles.forEach((particle, i) => {\n        particles.slice(i + 1).forEach(otherParticle => {\n          const dx = particle.x - otherParticle.x;\n          const dy = particle.y - otherParticle.y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n\n          if (distance < 100) {\n            ctx.beginPath();\n            ctx.moveTo(particle.x, particle.y);\n            ctx.lineTo(otherParticle.x, otherParticle.y);\n            ctx.strokeStyle = `rgba(14, 165, 233, ${0.1 * (1 - distance / 100)})`;\n            ctx.lineWidth = 0.5;\n            ctx.stroke();\n          }\n        });\n      });\n\n      animationFrameId = requestAnimationFrame(animate);\n    };\n\n    animate();\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      cancelAnimationFrame(animationFrameId);\n    };\n  }, []);\n\n  return (\n    <>\n      {/* Canvas for particle animation */}\n      <canvas\n        ref={canvasRef}\n        className=\"fixed inset-0 pointer-events-none z-0\"\n        style={{ background: 'transparent' }}\n      />\n\n      {/* Static floating elements */}\n      <div className=\"fixed inset-0 pointer-events-none z-0\">\n        {/* Gradient orbs */}\n        <motion.div\n          className=\"absolute w-96 h-96 rounded-full opacity-10\"\n          style={{\n            background: 'radial-gradient(circle, rgba(14, 165, 233, 0.3) 0%, transparent 70%)',\n            top: '10%',\n            left: '10%',\n          }}\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.1, 0.2, 0.1],\n          }}\n          transition={{\n            duration: 8,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n          }}\n        />\n\n        <motion.div\n          className=\"absolute w-64 h-64 rounded-full opacity-10\"\n          style={{\n            background: 'radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%)',\n            top: '60%',\n            right: '10%',\n          }}\n          animate={{\n            scale: [1.2, 1, 1.2],\n            opacity: [0.1, 0.15, 0.1],\n          }}\n          transition={{\n            duration: 6,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 2\n          }}\n        />\n\n        <motion.div\n          className=\"absolute w-48 h-48 rounded-full opacity-10\"\n          style={{\n            background: 'radial-gradient(circle, rgba(168, 85, 247, 0.3) 0%, transparent 70%)',\n            bottom: '20%',\n            left: '30%',\n          }}\n          animate={{\n            scale: [1, 1.3, 1],\n            opacity: [0.1, 0.2, 0.1],\n          }}\n          transition={{\n            duration: 10,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 4\n          }}\n        />\n\n        {/* Floating geometric shapes */}\n        {[...Array(5)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-primary-500 rounded-full opacity-20\"\n            style={{\n              top: `${Math.random() * 100}%`,\n              left: `${Math.random() * 100}%`,\n            }}\n            animate={{\n              y: [0, -30, 0],\n              opacity: [0.2, 0.5, 0.2],\n            }}\n            transition={{\n              duration: 4 + Math.random() * 4,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n              delay: Math.random() * 2\n            }}\n          />\n        ))}\n      </div>\n    </>\n  );\n};\n\nexport default BackgroundAnimation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,MAAM,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,SAAS,GAAGR,MAAM,CAAC,IAAI,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd,MAAMU,MAAM,GAAGD,SAAS,CAACE,OAAO;IAChC,IAAI,CAACD,MAAM,EAAE;IAEb,MAAME,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;IACnC,IAAIC,gBAAgB;IACpB,IAAIC,SAAS,GAAG,EAAE;;IAElB;IACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzBN,MAAM,CAACO,KAAK,GAAGC,MAAM,CAACC,UAAU;MAChCT,MAAM,CAACU,MAAM,GAAGF,MAAM,CAACG,WAAW;IACpC,CAAC;IAEDL,YAAY,CAAC,CAAC;IACdE,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;;IAE/C;IACA,MAAMO,QAAQ,CAAC;MACbC,WAAWA,CAAA,EAAG;QACZ,IAAI,CAACC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGjB,MAAM,CAACO,KAAK;QACrC,IAAI,CAACW,CAAC,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGjB,MAAM,CAACU,MAAM;QACtC,IAAI,CAACS,IAAI,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QACjC,IAAI,CAACG,MAAM,GAAG,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;QACzC,IAAI,CAACI,MAAM,GAAG,CAACL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;QACzC,IAAI,CAACK,OAAO,GAAGN,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;QACxC,IAAI,CAACM,KAAK,GAAG,sBAAsB,IAAI,CAACD,OAAO,GAAG;MACpD;MAEAE,MAAMA,CAAA,EAAG;QACP,IAAI,CAACT,CAAC,IAAI,IAAI,CAACK,MAAM;QACrB,IAAI,CAACF,CAAC,IAAI,IAAI,CAACG,MAAM;;QAErB;QACA,IAAI,IAAI,CAACN,CAAC,GAAGf,MAAM,CAACO,KAAK,EAAE,IAAI,CAACQ,CAAC,GAAG,CAAC;QACrC,IAAI,IAAI,CAACA,CAAC,GAAG,CAAC,EAAE,IAAI,CAACA,CAAC,GAAGf,MAAM,CAACO,KAAK;QACrC,IAAI,IAAI,CAACW,CAAC,GAAGlB,MAAM,CAACU,MAAM,EAAE,IAAI,CAACQ,CAAC,GAAG,CAAC;QACtC,IAAI,IAAI,CAACA,CAAC,GAAG,CAAC,EAAE,IAAI,CAACA,CAAC,GAAGlB,MAAM,CAACU,MAAM;;QAEtC;QACA,IAAI,CAACY,OAAO,IAAI,CAACN,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;QAC5C,IAAI,CAACK,OAAO,GAAGN,IAAI,CAACS,GAAG,CAAC,IAAI,EAAET,IAAI,CAACU,GAAG,CAAC,GAAG,EAAE,IAAI,CAACJ,OAAO,CAAC,CAAC;QAC1D,IAAI,CAACC,KAAK,GAAG,sBAAsB,IAAI,CAACD,OAAO,GAAG;MACpD;MAEAK,IAAIA,CAAA,EAAG;QACLzB,GAAG,CAAC0B,SAAS,CAAC,CAAC;QACf1B,GAAG,CAAC2B,GAAG,CAAC,IAAI,CAACd,CAAC,EAAE,IAAI,CAACG,CAAC,EAAE,IAAI,CAACC,IAAI,EAAE,CAAC,EAAEH,IAAI,CAACc,EAAE,GAAG,CAAC,CAAC;QAClD5B,GAAG,CAAC6B,SAAS,GAAG,IAAI,CAACR,KAAK;QAC1BrB,GAAG,CAAC8B,IAAI,CAAC,CAAC;MACZ;IACF;;IAEA;IACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;MAC1B5B,SAAS,GAAG,EAAE;MACd,MAAM6B,aAAa,GAAGlB,IAAI,CAACmB,KAAK,CAAEnC,MAAM,CAACO,KAAK,GAAGP,MAAM,CAACU,MAAM,GAAI,KAAK,CAAC;MACxE,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,aAAa,EAAEE,CAAC,EAAE,EAAE;QACtC/B,SAAS,CAACgC,IAAI,CAAC,IAAIxB,QAAQ,CAAC,CAAC,CAAC;MAChC;IACF,CAAC;IAEDoB,aAAa,CAAC,CAAC;;IAEf;IACA,MAAMK,OAAO,GAAGA,CAAA,KAAM;MACpBpC,GAAG,CAACqC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEvC,MAAM,CAACO,KAAK,EAAEP,MAAM,CAACU,MAAM,CAAC;;MAEhD;MACAL,SAAS,CAACmC,OAAO,CAACC,QAAQ,IAAI;QAC5BA,QAAQ,CAACjB,MAAM,CAAC,CAAC;QACjBiB,QAAQ,CAACd,IAAI,CAAC,CAAC;MACjB,CAAC,CAAC;;MAEF;MACAtB,SAAS,CAACmC,OAAO,CAAC,CAACC,QAAQ,EAAEL,CAAC,KAAK;QACjC/B,SAAS,CAACqC,KAAK,CAACN,CAAC,GAAG,CAAC,CAAC,CAACI,OAAO,CAACG,aAAa,IAAI;UAC9C,MAAMC,EAAE,GAAGH,QAAQ,CAAC1B,CAAC,GAAG4B,aAAa,CAAC5B,CAAC;UACvC,MAAM8B,EAAE,GAAGJ,QAAQ,CAACvB,CAAC,GAAGyB,aAAa,CAACzB,CAAC;UACvC,MAAM4B,QAAQ,GAAG9B,IAAI,CAAC+B,IAAI,CAACH,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;UAE7C,IAAIC,QAAQ,GAAG,GAAG,EAAE;YAClB5C,GAAG,CAAC0B,SAAS,CAAC,CAAC;YACf1B,GAAG,CAAC8C,MAAM,CAACP,QAAQ,CAAC1B,CAAC,EAAE0B,QAAQ,CAACvB,CAAC,CAAC;YAClChB,GAAG,CAAC+C,MAAM,CAACN,aAAa,CAAC5B,CAAC,EAAE4B,aAAa,CAACzB,CAAC,CAAC;YAC5ChB,GAAG,CAACgD,WAAW,GAAG,sBAAsB,GAAG,IAAI,CAAC,GAAGJ,QAAQ,GAAG,GAAG,CAAC,GAAG;YACrE5C,GAAG,CAACiD,SAAS,GAAG,GAAG;YACnBjD,GAAG,CAACkD,MAAM,CAAC,CAAC;UACd;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;MAEFhD,gBAAgB,GAAGiD,qBAAqB,CAACf,OAAO,CAAC;IACnD,CAAC;IAEDA,OAAO,CAAC,CAAC;;IAET;IACA,OAAO,MAAM;MACX9B,MAAM,CAAC8C,mBAAmB,CAAC,QAAQ,EAAEhD,YAAY,CAAC;MAClDiD,oBAAoB,CAACnD,gBAAgB,CAAC;IACxC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEV,OAAA,CAAAE,SAAA;IAAA4D,QAAA,gBAEE9D,OAAA;MACE+D,GAAG,EAAE1D,SAAU;MACf2D,SAAS,EAAC,uCAAuC;MACjDC,KAAK,EAAE;QAAEC,UAAU,EAAE;MAAc;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,eAGFtE,OAAA;MAAKgE,SAAS,EAAC,uCAAuC;MAAAF,QAAA,gBAEpD9D,OAAA,CAACF,MAAM,CAACyE,GAAG;QACTP,SAAS,EAAC,4CAA4C;QACtDC,KAAK,EAAE;UACLC,UAAU,EAAE,sEAAsE;UAClFM,GAAG,EAAE,KAAK;UACVC,IAAI,EAAE;QACR,CAAE;QACF7B,OAAO,EAAE;UACP8B,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAClB9C,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;QACzB,CAAE;QACF+C,UAAU,EAAE;UACVC,QAAQ,EAAE,CAAC;UACXC,MAAM,EAAEC,QAAQ;UAChBC,IAAI,EAAE;QACR;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEFtE,OAAA,CAACF,MAAM,CAACyE,GAAG;QACTP,SAAS,EAAC,4CAA4C;QACtDC,KAAK,EAAE;UACLC,UAAU,EAAE,sEAAsE;UAClFM,GAAG,EAAE,KAAK;UACVQ,KAAK,EAAE;QACT,CAAE;QACFpC,OAAO,EAAE;UACP8B,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;UACpB9C,OAAO,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG;QAC1B,CAAE;QACF+C,UAAU,EAAE;UACVC,QAAQ,EAAE,CAAC;UACXC,MAAM,EAAEC,QAAQ;UAChBC,IAAI,EAAE,WAAW;UACjBE,KAAK,EAAE;QACT;MAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEFtE,OAAA,CAACF,MAAM,CAACyE,GAAG;QACTP,SAAS,EAAC,4CAA4C;QACtDC,KAAK,EAAE;UACLC,UAAU,EAAE,sEAAsE;UAClFgB,MAAM,EAAE,KAAK;UACbT,IAAI,EAAE;QACR,CAAE;QACF7B,OAAO,EAAE;UACP8B,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAClB9C,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;QACzB,CAAE;QACF+C,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,MAAM,EAAEC,QAAQ;UAChBC,IAAI,EAAE,WAAW;UACjBE,KAAK,EAAE;QACT;MAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGD,CAAC,GAAGa,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAE3C,CAAC,kBACtB1C,OAAA,CAACF,MAAM,CAACyE,GAAG;QAETP,SAAS,EAAC,yDAAyD;QACnEC,KAAK,EAAE;UACLO,GAAG,EAAE,GAAGlD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC9BkD,IAAI,EAAE,GAAGnD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;QAC9B,CAAE;QACFqB,OAAO,EAAE;UACPpB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;UACdI,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;QACzB,CAAE;QACF+C,UAAU,EAAE;UACVC,QAAQ,EAAE,CAAC,GAAGtD,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC/BsD,MAAM,EAAEC,QAAQ;UAChBC,IAAI,EAAE,WAAW;UACjBE,KAAK,EAAE3D,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QACzB;MAAE,GAfGmB,CAAC;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBP,CACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAClE,EAAA,CAvMID,mBAAmB;AAAAmF,EAAA,GAAnBnF,mBAAmB;AAyMzB,eAAeA,mBAAmB;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}