[{"C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\components\\FileUpload.js": "3", "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\components\\ChatInterface.js": "4", "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\components\\Header.js": "5", "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\components\\BackgroundAnimation.js": "6", "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\components\\InvoiceDisplay.js": "7", "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\context\\ChatContext.js": "8", "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\components\\SuggestedQuestions.js": "9", "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\components\\ChatMessage.js": "10"}, {"size": 254, "mtime": 1753167320680, "results": "11", "hashOfConfig": "12"}, {"size": 2921, "mtime": 1753169147391, "results": "13", "hashOfConfig": "12"}, {"size": 5444, "mtime": 1753167509408, "results": "14", "hashOfConfig": "12"}, {"size": 8527, "mtime": 1753169158820, "results": "15", "hashOfConfig": "12"}, {"size": 4722, "mtime": 1753175241177, "results": "16", "hashOfConfig": "12"}, {"size": 5715, "mtime": 1753167458736, "results": "17", "hashOfConfig": "12"}, {"size": 7948, "mtime": 1753175190233, "results": "18", "hashOfConfig": "12"}, {"size": 5577, "mtime": 1753167408615, "results": "19", "hashOfConfig": "12"}, {"size": 3515, "mtime": 1753167657109, "results": "20", "hashOfConfig": "12"}, {"size": 3958, "mtime": 1753167629435, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "sonww4", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\components\\FileUpload.js", [], [], "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\components\\ChatInterface.js", [], [], "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\components\\BackgroundAnimation.js", [], [], "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\components\\InvoiceDisplay.js", ["52"], [], "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\context\\ChatContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\components\\SuggestedQuestions.js", [], [], "C:\\Users\\<USER>\\Desktop\\invoice-extracting-agent\\src\\components\\ChatMessage.js", [], [], {"ruleId": "53", "severity": 1, "message": "54", "line": 37, "column": 11, "nodeType": "55", "messageId": "56", "endLine": 37, "endColumn": 22}, "no-unused-vars", "'paymentData' is assigned a value but never used.", "Identifier", "unusedVar"]