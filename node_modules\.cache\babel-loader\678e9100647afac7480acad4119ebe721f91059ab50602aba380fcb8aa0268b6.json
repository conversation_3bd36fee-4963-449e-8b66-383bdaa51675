{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport { asciiPunctuation } from 'micromark-util-character';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\n\n/** @type {Construct} */\nexport const characterEscape = {\n  name: 'characterEscape',\n  tokenize: tokenizeCharacterEscape\n};\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterEscape(effects, ok, nok) {\n  return start;\n\n  /**\n   * Start of character escape.\n   *\n   * ```markdown\n   * > | a\\*b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.backslash, 'expected `\\\\`');\n    effects.enter(types.characterEscape);\n    effects.enter(types.escapeMarker);\n    effects.consume(code);\n    effects.exit(types.escapeMarker);\n    return inside;\n  }\n\n  /**\n   * After `\\`, at punctuation.\n   *\n   * ```markdown\n   * > | a\\*b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    // ASCII punctuation.\n    if (asciiPunctuation(code)) {\n      effects.enter(types.characterEscapeValue);\n      effects.consume(code);\n      effects.exit(types.characterEscapeValue);\n      effects.exit(types.characterEscape);\n      return ok;\n    }\n    return nok(code);\n  }\n}", "map": {"version": 3, "names": ["asciiPunctuation", "codes", "types", "ok", "assert", "characterEscape", "name", "tokenize", "tokenizeCharacterEscape", "effects", "nok", "start", "code", "backslash", "enter", "<PERSON><PERSON><PERSON><PERSON>", "consume", "exit", "inside", "characterEscapeValue"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/micromark-core-commonmark/dev/lib/character-escape.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport {asciiPunctuation} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/** @type {Construct} */\nexport const characterEscape = {\n  name: 'characterEscape',\n  tokenize: tokenizeCharacterEscape\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterEscape(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of character escape.\n   *\n   * ```markdown\n   * > | a\\*b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.backslash, 'expected `\\\\`')\n    effects.enter(types.characterEscape)\n    effects.enter(types.escapeMarker)\n    effects.consume(code)\n    effects.exit(types.escapeMarker)\n    return inside\n  }\n\n  /**\n   * After `\\`, at punctuation.\n   *\n   * ```markdown\n   * > | a\\*b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    // ASCII punctuation.\n    if (asciiPunctuation(code)) {\n      effects.enter(types.characterEscapeValue)\n      effects.consume(code)\n      effects.exit(types.characterEscapeValue)\n      effects.exit(types.characterEscape)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,gBAAgB,QAAO,0BAA0B;AACzD,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;;AAEvC;AACA,OAAO,MAAMC,eAAe,GAAG;EAC7BC,IAAI,EAAE,iBAAiB;EACvBC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASA,uBAAuBA,CAACC,OAAO,EAAEN,EAAE,EAAEO,GAAG,EAAE;EACjD,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnBR,MAAM,CAACQ,IAAI,KAAKX,KAAK,CAACY,SAAS,EAAE,eAAe,CAAC;IACjDJ,OAAO,CAACK,KAAK,CAACZ,KAAK,CAACG,eAAe,CAAC;IACpCI,OAAO,CAACK,KAAK,CAACZ,KAAK,CAACa,YAAY,CAAC;IACjCN,OAAO,CAACO,OAAO,CAACJ,IAAI,CAAC;IACrBH,OAAO,CAACQ,IAAI,CAACf,KAAK,CAACa,YAAY,CAAC;IAChC,OAAOG,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,MAAMA,CAACN,IAAI,EAAE;IACpB;IACA,IAAIZ,gBAAgB,CAACY,IAAI,CAAC,EAAE;MAC1BH,OAAO,CAACK,KAAK,CAACZ,KAAK,CAACiB,oBAAoB,CAAC;MACzCV,OAAO,CAACO,OAAO,CAACJ,IAAI,CAAC;MACrBH,OAAO,CAACQ,IAAI,CAACf,KAAK,CAACiB,oBAAoB,CAAC;MACxCV,OAAO,CAACQ,IAAI,CAACf,KAAK,CAACG,eAAe,CAAC;MACnC,OAAOF,EAAE;IACX;IAEA,OAAOO,GAAG,CAACE,IAAI,CAAC;EAClB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}