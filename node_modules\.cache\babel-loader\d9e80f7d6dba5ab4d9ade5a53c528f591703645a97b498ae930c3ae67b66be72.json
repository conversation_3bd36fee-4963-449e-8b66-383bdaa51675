{"ast": null, "code": "import { warnOnce } from '../../utils/warn-once.mjs';\nimport { useScroll } from '../use-scroll.mjs';\n\n/**\n * @deprecated useElementScroll is deprecated. Convert to useScroll({ container: ref })\n */\nfunction useElementScroll(ref) {\n  if (process.env.NODE_ENV === \"development\") {\n    warnOnce(false, \"useElementScroll is deprecated. Convert to useScroll({ container: ref }).\");\n  }\n  return useScroll({\n    container: ref\n  });\n}\nexport { useElementScroll };", "map": {"version": 3, "names": ["warnOnce", "useScroll", "useElementScroll", "ref", "process", "env", "NODE_ENV", "container"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/framer-motion/dist/es/value/scroll/use-element-scroll.mjs"], "sourcesContent": ["import { warnOnce } from '../../utils/warn-once.mjs';\nimport { useScroll } from '../use-scroll.mjs';\n\n/**\n * @deprecated useElementScroll is deprecated. Convert to useScroll({ container: ref })\n */\nfunction useElementScroll(ref) {\n    if (process.env.NODE_ENV === \"development\") {\n        warnOnce(false, \"useElementScroll is deprecated. Convert to useScroll({ container: ref }).\");\n    }\n    return useScroll({ container: ref });\n}\n\nexport { useElementScroll };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,SAAS,QAAQ,mBAAmB;;AAE7C;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,GAAG,EAAE;EAC3B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IACxCN,QAAQ,CAAC,KAAK,EAAE,2EAA2E,CAAC;EAChG;EACA,OAAOC,SAAS,CAAC;IAAEM,SAAS,EAAEJ;EAAI,CAAC,CAAC;AACxC;AAEA,SAASD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}