{"ast": null, "code": "/**\n * @param {Record<string, string>} attributes\n * @param {string} attribute\n * @returns {string}\n */\nexport function caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute;\n}", "map": {"version": 3, "names": ["caseSensitiveTransform", "attributes", "attribute"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/property-information/lib/util/case-sensitive-transform.js"], "sourcesContent": ["/**\n * @param {Record<string, string>} attributes\n * @param {string} attribute\n * @returns {string}\n */\nexport function caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,sBAAsBA,CAACC,UAAU,EAAEC,SAAS,EAAE;EAC5D,OAAOA,SAAS,IAAID,UAAU,GAAGA,UAAU,CAACC,SAAS,CAAC,GAAGA,SAAS;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}