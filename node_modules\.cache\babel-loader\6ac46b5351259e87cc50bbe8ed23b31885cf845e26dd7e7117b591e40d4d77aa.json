{"ast": null, "code": "/**\n * @typedef {import('mdast').FootnoteReference} FootnoteReference\n * @typedef {import('hast').Element} Element\n * @typedef {import('../state.js').State} State\n */\n\nimport { normalizeUri } from 'micromark-util-sanitize-uri';\n\n/**\n * Turn an mdast `footnoteReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {FootnoteReference} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function footnoteReference(state, node) {\n  const id = String(node.identifier).toUpperCase();\n  const safeId = normalizeUri(id.toLowerCase());\n  const index = state.footnoteOrder.indexOf(id);\n  /** @type {number} */\n  let counter;\n  if (index === -1) {\n    state.footnoteOrder.push(id);\n    state.footnoteCounts[id] = 1;\n    counter = state.footnoteOrder.length;\n  } else {\n    state.footnoteCounts[id]++;\n    counter = index + 1;\n  }\n  const reuseCounter = state.footnoteCounts[id];\n\n  /** @type {Element} */\n  const link = {\n    type: 'element',\n    tagName: 'a',\n    properties: {\n      href: '#' + state.clobberPrefix + 'fn-' + safeId,\n      id: state.clobberPrefix + 'fnref-' + safeId + (reuseCounter > 1 ? '-' + reuseCounter : ''),\n      dataFootnoteRef: true,\n      ariaDescribedBy: ['footnote-label']\n    },\n    children: [{\n      type: 'text',\n      value: String(counter)\n    }]\n  };\n  state.patch(node, link);\n\n  /** @type {Element} */\n  const sup = {\n    type: 'element',\n    tagName: 'sup',\n    properties: {},\n    children: [link]\n  };\n  state.patch(node, sup);\n  return state.applyData(node, sup);\n}", "map": {"version": 3, "names": ["normalizeUri", "footnoteReference", "state", "node", "id", "String", "identifier", "toUpperCase", "safeId", "toLowerCase", "index", "footnoteOrder", "indexOf", "counter", "push", "footnoteCounts", "length", "reuseCounter", "link", "type", "tagName", "properties", "href", "clobberPrefix", "dataFootnoteRef", "ariaDescribedBy", "children", "value", "patch", "sup", "applyData"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js"], "sourcesContent": ["/**\n * @typedef {import('mdast').FootnoteReference} FootnoteReference\n * @typedef {import('hast').Element} Element\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `footnoteReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {FootnoteReference} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function footnoteReference(state, node) {\n  const id = String(node.identifier).toUpperCase()\n  const safeId = normalizeUri(id.toLowerCase())\n  const index = state.footnoteOrder.indexOf(id)\n  /** @type {number} */\n  let counter\n\n  if (index === -1) {\n    state.footnoteOrder.push(id)\n    state.footnoteCounts[id] = 1\n    counter = state.footnoteOrder.length\n  } else {\n    state.footnoteCounts[id]++\n    counter = index + 1\n  }\n\n  const reuseCounter = state.footnoteCounts[id]\n\n  /** @type {Element} */\n  const link = {\n    type: 'element',\n    tagName: 'a',\n    properties: {\n      href: '#' + state.clobberPrefix + 'fn-' + safeId,\n      id:\n        state.clobberPrefix +\n        'fnref-' +\n        safeId +\n        (reuseCounter > 1 ? '-' + reuseCounter : ''),\n      dataFootnoteRef: true,\n      ariaDescribedBy: ['footnote-label']\n    },\n    children: [{type: 'text', value: String(counter)}]\n  }\n  state.patch(node, link)\n\n  /** @type {Element} */\n  const sup = {\n    type: 'element',\n    tagName: 'sup',\n    properties: {},\n    children: [link]\n  }\n  state.patch(node, sup)\n  return state.applyData(node, sup)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,6BAA6B;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC7C,MAAMC,EAAE,GAAGC,MAAM,CAACF,IAAI,CAACG,UAAU,CAAC,CAACC,WAAW,CAAC,CAAC;EAChD,MAAMC,MAAM,GAAGR,YAAY,CAACI,EAAE,CAACK,WAAW,CAAC,CAAC,CAAC;EAC7C,MAAMC,KAAK,GAAGR,KAAK,CAACS,aAAa,CAACC,OAAO,CAACR,EAAE,CAAC;EAC7C;EACA,IAAIS,OAAO;EAEX,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;IAChBR,KAAK,CAACS,aAAa,CAACG,IAAI,CAACV,EAAE,CAAC;IAC5BF,KAAK,CAACa,cAAc,CAACX,EAAE,CAAC,GAAG,CAAC;IAC5BS,OAAO,GAAGX,KAAK,CAACS,aAAa,CAACK,MAAM;EACtC,CAAC,MAAM;IACLd,KAAK,CAACa,cAAc,CAACX,EAAE,CAAC,EAAE;IAC1BS,OAAO,GAAGH,KAAK,GAAG,CAAC;EACrB;EAEA,MAAMO,YAAY,GAAGf,KAAK,CAACa,cAAc,CAACX,EAAE,CAAC;;EAE7C;EACA,MAAMc,IAAI,GAAG;IACXC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,GAAG;IACZC,UAAU,EAAE;MACVC,IAAI,EAAE,GAAG,GAAGpB,KAAK,CAACqB,aAAa,GAAG,KAAK,GAAGf,MAAM;MAChDJ,EAAE,EACAF,KAAK,CAACqB,aAAa,GACnB,QAAQ,GACRf,MAAM,IACLS,YAAY,GAAG,CAAC,GAAG,GAAG,GAAGA,YAAY,GAAG,EAAE,CAAC;MAC9CO,eAAe,EAAE,IAAI;MACrBC,eAAe,EAAE,CAAC,gBAAgB;IACpC,CAAC;IACDC,QAAQ,EAAE,CAAC;MAACP,IAAI,EAAE,MAAM;MAAEQ,KAAK,EAAEtB,MAAM,CAACQ,OAAO;IAAC,CAAC;EACnD,CAAC;EACDX,KAAK,CAAC0B,KAAK,CAACzB,IAAI,EAAEe,IAAI,CAAC;;EAEvB;EACA,MAAMW,GAAG,GAAG;IACVV,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,CAAC,CAAC;IACdK,QAAQ,EAAE,CAACR,IAAI;EACjB,CAAC;EACDhB,KAAK,CAAC0B,KAAK,CAACzB,IAAI,EAAE0B,GAAG,CAAC;EACtB,OAAO3B,KAAK,CAAC4B,SAAS,CAAC3B,IAAI,EAAE0B,GAAG,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}