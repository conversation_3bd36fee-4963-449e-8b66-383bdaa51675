{"ast": null, "code": "/**\n * @typedef {import('./lib/index.js').Callback} Callback\n * @typedef {import('./lib/index.js').Middleware} Middleware\n * @typedef {import('./lib/index.js').Pipeline} Pipeline\n * @typedef {import('./lib/index.js').Run} Run\n * @typedef {import('./lib/index.js').Use} Use\n */\n\nexport { trough, wrap } from './lib/index.js';", "map": {"version": 3, "names": ["trough", "wrap"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/trough/index.js"], "sourcesContent": ["/**\n * @typedef {import('./lib/index.js').Callback} Callback\n * @typedef {import('./lib/index.js').Middleware} Middleware\n * @typedef {import('./lib/index.js').Pipeline} Pipeline\n * @typedef {import('./lib/index.js').Run} Run\n * @typedef {import('./lib/index.js').Use} Use\n */\n\nexport {trough, wrap} from './lib/index.js'\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,MAAM,EAAEC,IAAI,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}