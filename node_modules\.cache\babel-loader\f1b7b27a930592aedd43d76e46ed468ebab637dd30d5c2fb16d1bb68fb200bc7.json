{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Effects} Effects\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').TokenType} TokenType\n */\n\nimport { markdownLineEnding, markdownSpace } from 'micromark-util-character';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { constants } from 'micromark-util-symbol/constants.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\n\n/**\n * Parse labels.\n *\n * > 👉 **Note**: labels in markdown are capped at 999 characters in the string.\n *\n * ###### Examples\n *\n * ```markdown\n * [a]\n * [a\n * b]\n * [a\\]b]\n * ```\n *\n * @this {TokenizeContext}\n *   Tokenize context.\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {State} nok\n *   State switched to when unsuccessful.\n * @param {TokenType} type\n *   Type of the whole label (`[a]`).\n * @param {TokenType} markerType\n *   Type for the markers (`[` and `]`).\n * @param {TokenType} stringType\n *   Type for the identifier (`a`).\n * @returns {State}\n *   Start state.\n */\n// eslint-disable-next-line max-params\nexport function factoryLabel(effects, ok, nok, type, markerType, stringType) {\n  const self = this;\n  let size = 0;\n  /** @type {boolean} */\n  let seen;\n  return start;\n\n  /**\n   * Start of label.\n   *\n   * ```markdown\n   * > | [a]\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`');\n    effects.enter(type);\n    effects.enter(markerType);\n    effects.consume(code);\n    effects.exit(markerType);\n    effects.enter(stringType);\n    return atBreak;\n  }\n\n  /**\n   * In label, at something, before something else.\n   *\n   * ```markdown\n   * > | [a]\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (size > constants.linkReferenceSizeMax || code === codes.eof || code === codes.leftSquareBracket || code === codes.rightSquareBracket && !seen ||\n    // To do: remove in the future once we’ve switched from\n    // `micromark-extension-footnote` to `micromark-extension-gfm-footnote`,\n    // which doesn’t need this.\n    // Hidden footnotes hook.\n    /* c8 ignore next 3 */\n    code === codes.caret && !size && '_hiddenFootnoteSupport' in self.parser.constructs) {\n      return nok(code);\n    }\n    if (code === codes.rightSquareBracket) {\n      effects.exit(stringType);\n      effects.enter(markerType);\n      effects.consume(code);\n      effects.exit(markerType);\n      effects.exit(type);\n      return ok;\n    }\n\n    // To do: indent? Link chunks and EOLs together?\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding);\n      effects.consume(code);\n      effects.exit(types.lineEnding);\n      return atBreak;\n    }\n    effects.enter(types.chunkString, {\n      contentType: constants.contentTypeString\n    });\n    return labelInside(code);\n  }\n\n  /**\n   * In label, in text.\n   *\n   * ```markdown\n   * > | [a]\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelInside(code) {\n    if (code === codes.eof || code === codes.leftSquareBracket || code === codes.rightSquareBracket || markdownLineEnding(code) || size++ > constants.linkReferenceSizeMax) {\n      effects.exit(types.chunkString);\n      return atBreak(code);\n    }\n    effects.consume(code);\n    if (!seen) seen = !markdownSpace(code);\n    return code === codes.backslash ? labelEscape : labelInside;\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * ```markdown\n   * > | [a\\*a]\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEscape(code) {\n    if (code === codes.leftSquareBracket || code === codes.backslash || code === codes.rightSquareBracket) {\n      effects.consume(code);\n      size++;\n      return labelInside;\n    }\n    return labelInside(code);\n  }\n}", "map": {"version": 3, "names": ["markdownLineEnding", "markdownSpace", "codes", "constants", "types", "ok", "assert", "factoryLabel", "effects", "nok", "type", "markerType", "stringType", "self", "size", "seen", "start", "code", "leftSquareBracket", "enter", "consume", "exit", "atBreak", "linkReferenceSizeMax", "eof", "rightSquareBracket", "caret", "parser", "constructs", "lineEnding", "chunkString", "contentType", "contentTypeString", "labelInside", "backslash", "labelEscape"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/micromark-factory-label/dev/index.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Effects} Effects\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').TokenType} TokenType\n */\n\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/**\n * Parse labels.\n *\n * > 👉 **Note**: labels in markdown are capped at 999 characters in the string.\n *\n * ###### Examples\n *\n * ```markdown\n * [a]\n * [a\n * b]\n * [a\\]b]\n * ```\n *\n * @this {TokenizeContext}\n *   Tokenize context.\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {State} nok\n *   State switched to when unsuccessful.\n * @param {TokenType} type\n *   Type of the whole label (`[a]`).\n * @param {TokenType} markerType\n *   Type for the markers (`[` and `]`).\n * @param {TokenType} stringType\n *   Type for the identifier (`a`).\n * @returns {State}\n *   Start state.\n */\n// eslint-disable-next-line max-params\nexport function factoryLabel(effects, ok, nok, type, markerType, stringType) {\n  const self = this\n  let size = 0\n  /** @type {boolean} */\n  let seen\n\n  return start\n\n  /**\n   * Start of label.\n   *\n   * ```markdown\n   * > | [a]\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    effects.enter(type)\n    effects.enter(markerType)\n    effects.consume(code)\n    effects.exit(markerType)\n    effects.enter(stringType)\n    return atBreak\n  }\n\n  /**\n   * In label, at something, before something else.\n   *\n   * ```markdown\n   * > | [a]\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (\n      size > constants.linkReferenceSizeMax ||\n      code === codes.eof ||\n      code === codes.leftSquareBracket ||\n      (code === codes.rightSquareBracket && !seen) ||\n      // To do: remove in the future once we’ve switched from\n      // `micromark-extension-footnote` to `micromark-extension-gfm-footnote`,\n      // which doesn’t need this.\n      // Hidden footnotes hook.\n      /* c8 ignore next 3 */\n      (code === codes.caret &&\n        !size &&\n        '_hiddenFootnoteSupport' in self.parser.constructs)\n    ) {\n      return nok(code)\n    }\n\n    if (code === codes.rightSquareBracket) {\n      effects.exit(stringType)\n      effects.enter(markerType)\n      effects.consume(code)\n      effects.exit(markerType)\n      effects.exit(type)\n      return ok\n    }\n\n    // To do: indent? Link chunks and EOLs together?\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return atBreak\n    }\n\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return labelInside(code)\n  }\n\n  /**\n   * In label, in text.\n   *\n   * ```markdown\n   * > | [a]\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelInside(code) {\n    if (\n      code === codes.eof ||\n      code === codes.leftSquareBracket ||\n      code === codes.rightSquareBracket ||\n      markdownLineEnding(code) ||\n      size++ > constants.linkReferenceSizeMax\n    ) {\n      effects.exit(types.chunkString)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    if (!seen) seen = !markdownSpace(code)\n    return code === codes.backslash ? labelEscape : labelInside\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * ```markdown\n   * > | [a\\*a]\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEscape(code) {\n    if (\n      code === codes.leftSquareBracket ||\n      code === codes.backslash ||\n      code === codes.rightSquareBracket\n    ) {\n      effects.consume(code)\n      size++\n      return labelInside\n    }\n\n    return labelInside(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,kBAAkB,EAAEC,aAAa,QAAO,0BAA0B;AAC1E,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,SAAS,QAAO,oCAAoC;AAC5D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAEH,EAAE,EAAEI,GAAG,EAAEC,IAAI,EAAEC,UAAU,EAAEC,UAAU,EAAE;EAC3E,MAAMC,IAAI,GAAG,IAAI;EACjB,IAAIC,IAAI,GAAG,CAAC;EACZ;EACA,IAAIC,IAAI;EAER,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnBX,MAAM,CAACW,IAAI,KAAKf,KAAK,CAACgB,iBAAiB,EAAE,cAAc,CAAC;IACxDV,OAAO,CAACW,KAAK,CAACT,IAAI,CAAC;IACnBF,OAAO,CAACW,KAAK,CAACR,UAAU,CAAC;IACzBH,OAAO,CAACY,OAAO,CAACH,IAAI,CAAC;IACrBT,OAAO,CAACa,IAAI,CAACV,UAAU,CAAC;IACxBH,OAAO,CAACW,KAAK,CAACP,UAAU,CAAC;IACzB,OAAOU,OAAO;EAChB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,OAAOA,CAACL,IAAI,EAAE;IACrB,IACEH,IAAI,GAAGX,SAAS,CAACoB,oBAAoB,IACrCN,IAAI,KAAKf,KAAK,CAACsB,GAAG,IAClBP,IAAI,KAAKf,KAAK,CAACgB,iBAAiB,IAC/BD,IAAI,KAAKf,KAAK,CAACuB,kBAAkB,IAAI,CAACV,IAAK;IAC5C;IACA;IACA;IACA;IACA;IACCE,IAAI,KAAKf,KAAK,CAACwB,KAAK,IACnB,CAACZ,IAAI,IACL,wBAAwB,IAAID,IAAI,CAACc,MAAM,CAACC,UAAW,EACrD;MACA,OAAOnB,GAAG,CAACQ,IAAI,CAAC;IAClB;IAEA,IAAIA,IAAI,KAAKf,KAAK,CAACuB,kBAAkB,EAAE;MACrCjB,OAAO,CAACa,IAAI,CAACT,UAAU,CAAC;MACxBJ,OAAO,CAACW,KAAK,CAACR,UAAU,CAAC;MACzBH,OAAO,CAACY,OAAO,CAACH,IAAI,CAAC;MACrBT,OAAO,CAACa,IAAI,CAACV,UAAU,CAAC;MACxBH,OAAO,CAACa,IAAI,CAACX,IAAI,CAAC;MAClB,OAAOL,EAAE;IACX;;IAEA;IACA,IAAIL,kBAAkB,CAACiB,IAAI,CAAC,EAAE;MAC5BT,OAAO,CAACW,KAAK,CAACf,KAAK,CAACyB,UAAU,CAAC;MAC/BrB,OAAO,CAACY,OAAO,CAACH,IAAI,CAAC;MACrBT,OAAO,CAACa,IAAI,CAACjB,KAAK,CAACyB,UAAU,CAAC;MAC9B,OAAOP,OAAO;IAChB;IAEAd,OAAO,CAACW,KAAK,CAACf,KAAK,CAAC0B,WAAW,EAAE;MAACC,WAAW,EAAE5B,SAAS,CAAC6B;IAAiB,CAAC,CAAC;IAC5E,OAAOC,WAAW,CAAChB,IAAI,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASgB,WAAWA,CAAChB,IAAI,EAAE;IACzB,IACEA,IAAI,KAAKf,KAAK,CAACsB,GAAG,IAClBP,IAAI,KAAKf,KAAK,CAACgB,iBAAiB,IAChCD,IAAI,KAAKf,KAAK,CAACuB,kBAAkB,IACjCzB,kBAAkB,CAACiB,IAAI,CAAC,IACxBH,IAAI,EAAE,GAAGX,SAAS,CAACoB,oBAAoB,EACvC;MACAf,OAAO,CAACa,IAAI,CAACjB,KAAK,CAAC0B,WAAW,CAAC;MAC/B,OAAOR,OAAO,CAACL,IAAI,CAAC;IACtB;IAEAT,OAAO,CAACY,OAAO,CAACH,IAAI,CAAC;IACrB,IAAI,CAACF,IAAI,EAAEA,IAAI,GAAG,CAACd,aAAa,CAACgB,IAAI,CAAC;IACtC,OAAOA,IAAI,KAAKf,KAAK,CAACgC,SAAS,GAAGC,WAAW,GAAGF,WAAW;EAC7D;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASE,WAAWA,CAAClB,IAAI,EAAE;IACzB,IACEA,IAAI,KAAKf,KAAK,CAACgB,iBAAiB,IAChCD,IAAI,KAAKf,KAAK,CAACgC,SAAS,IACxBjB,IAAI,KAAKf,KAAK,CAACuB,kBAAkB,EACjC;MACAjB,OAAO,CAACY,OAAO,CAACH,IAAI,CAAC;MACrBH,IAAI,EAAE;MACN,OAAOmB,WAAW;IACpB;IAEA,OAAOA,WAAW,CAAChB,IAAI,CAAC;EAC1B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}