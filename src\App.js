import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ChatInterface from './components/ChatInterface';
import FileUpload from './components/FileUpload';
import Header from './components/Header';
import BackgroundAnimation from './components/BackgroundAnimation';
import InvoiceDisplay from './components/InvoiceDisplay';
import { ChatProvider } from './context/ChatContext';
import './App.css';

function App() {
  const [conversationId, setConversationId] = useState(null);
  const [invoiceData, setInvoiceData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleFileUpload = async (file) => {
    setIsLoading(true);
    try {
      const formData = new FormData();
      formData.append('pdf', file);

      const response = await fetch('/api/upload-pdf', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload file');
      }

      const result = await response.json();
      setConversationId(result.conversationId);
      setInvoiceData(result.data);
    } catch (error) {
      console.error('Upload error:', error);
      // Handle error (show toast, etc.)
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ChatProvider>
      <div className="App min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-800 relative">
        <BackgroundAnimation />

        <div className="relative z-10 flex flex-col min-h-screen">
          <Header />

          <main className="flex-1 flex flex-col lg:flex-row gap-6 p-6 max-w-7xl mx-auto w-full overflow-y-auto">
            {/* Left Panel - File Upload & Invoice Display */}
            <motion.div
              className="lg:w-1/3 space-y-6 overflow-y-auto"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
            >
              <FileUpload
                onFileUpload={handleFileUpload}
                isLoading={isLoading}
              />

              <AnimatePresence>
                {invoiceData && (
                  <InvoiceDisplay
                    data={invoiceData}
                    conversationId={conversationId}
                  />
                )}
              </AnimatePresence>
            </motion.div>

            {/* Right Panel - Chat Interface */}
            <motion.div
              className="lg:w-2/3 flex flex-col min-h-0"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <ChatInterface
                conversationId={conversationId}
                invoiceData={invoiceData}
              />
            </motion.div>
          </main>
        </div>
      </div>
    </ChatProvider>
  );
}

export default App;
