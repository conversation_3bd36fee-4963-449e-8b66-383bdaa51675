{"ast": null, "code": "/**\n * @typedef {import('./util/schema.js').Schema} Schema\n */\n\nimport { normalize } from './normalize.js';\nimport { DefinedInfo } from './util/defined-info.js';\nimport { Info } from './util/info.js';\nconst valid = /^data[-\\w.:]+$/i;\nconst dash = /-[a-z]/g;\nconst cap = /[A-Z]/g;\n\n/**\n * @param {Schema} schema\n * @param {string} value\n * @returns {Info}\n */\nexport function find(schema, value) {\n  const normal = normalize(value);\n  let prop = value;\n  let Type = Info;\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]];\n  }\n  if (normal.length > 4 && normal.slice(0, 4) === 'data' && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      // Turn it into a property.\n      const rest = value.slice(5).replace(dash, camelcase);\n      prop = 'data' + rest.charAt(0).toUpperCase() + rest.slice(1);\n    } else {\n      // Turn it into an attribute.\n      const rest = value.slice(4);\n      if (!dash.test(rest)) {\n        let dashes = rest.replace(cap, kebab);\n        if (dashes.charAt(0) !== '-') {\n          dashes = '-' + dashes;\n        }\n        value = 'data' + dashes;\n      }\n    }\n    Type = DefinedInfo;\n  }\n  return new Type(prop, value);\n}\n\n/**\n * @param {string} $0\n * @returns {string}\n */\nfunction kebab($0) {\n  return '-' + $0.toLowerCase();\n}\n\n/**\n * @param {string} $0\n * @returns {string}\n */\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase();\n}", "map": {"version": 3, "names": ["normalize", "DefinedInfo", "Info", "valid", "dash", "cap", "find", "schema", "value", "normal", "prop", "Type", "property", "length", "slice", "test", "char<PERSON>t", "rest", "replace", "camelcase", "toUpperCase", "dashes", "kebab", "$0", "toLowerCase"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/property-information/lib/find.js"], "sourcesContent": ["/**\n * @typedef {import('./util/schema.js').Schema} Schema\n */\n\nimport {normalize} from './normalize.js'\nimport {DefinedInfo} from './util/defined-info.js'\nimport {Info} from './util/info.js'\n\nconst valid = /^data[-\\w.:]+$/i\nconst dash = /-[a-z]/g\nconst cap = /[A-Z]/g\n\n/**\n * @param {Schema} schema\n * @param {string} value\n * @returns {Info}\n */\nexport function find(schema, value) {\n  const normal = normalize(value)\n  let prop = value\n  let Type = Info\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === 'data' && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      // Turn it into a property.\n      const rest = value.slice(5).replace(dash, camelcase)\n      prop = 'data' + rest.charAt(0).toUpperCase() + rest.slice(1)\n    } else {\n      // Turn it into an attribute.\n      const rest = value.slice(4)\n\n      if (!dash.test(rest)) {\n        let dashes = rest.replace(cap, kebab)\n\n        if (dashes.charAt(0) !== '-') {\n          dashes = '-' + dashes\n        }\n\n        value = 'data' + dashes\n      }\n    }\n\n    Type = DefinedInfo\n  }\n\n  return new Type(prop, value)\n}\n\n/**\n * @param {string} $0\n * @returns {string}\n */\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\n/**\n * @param {string} $0\n * @returns {string}\n */\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAAQA,SAAS,QAAO,gBAAgB;AACxC,SAAQC,WAAW,QAAO,wBAAwB;AAClD,SAAQC,IAAI,QAAO,gBAAgB;AAEnC,MAAMC,KAAK,GAAG,iBAAiB;AAC/B,MAAMC,IAAI,GAAG,SAAS;AACtB,MAAMC,GAAG,GAAG,QAAQ;;AAEpB;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,IAAIA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAClC,MAAMC,MAAM,GAAGT,SAAS,CAACQ,KAAK,CAAC;EAC/B,IAAIE,IAAI,GAAGF,KAAK;EAChB,IAAIG,IAAI,GAAGT,IAAI;EAEf,IAAIO,MAAM,IAAIF,MAAM,CAACE,MAAM,EAAE;IAC3B,OAAOF,MAAM,CAACK,QAAQ,CAACL,MAAM,CAACE,MAAM,CAACA,MAAM,CAAC,CAAC;EAC/C;EAEA,IAAIA,MAAM,CAACI,MAAM,GAAG,CAAC,IAAIJ,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,IAAIX,KAAK,CAACY,IAAI,CAACP,KAAK,CAAC,EAAE;IAC3E;IACA,IAAIA,KAAK,CAACQ,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC3B;MACA,MAAMC,IAAI,GAAGT,KAAK,CAACM,KAAK,CAAC,CAAC,CAAC,CAACI,OAAO,CAACd,IAAI,EAAEe,SAAS,CAAC;MACpDT,IAAI,GAAG,MAAM,GAAGO,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,GAAGH,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC;IAC9D,CAAC,MAAM;MACL;MACA,MAAMG,IAAI,GAAGT,KAAK,CAACM,KAAK,CAAC,CAAC,CAAC;MAE3B,IAAI,CAACV,IAAI,CAACW,IAAI,CAACE,IAAI,CAAC,EAAE;QACpB,IAAII,MAAM,GAAGJ,IAAI,CAACC,OAAO,CAACb,GAAG,EAAEiB,KAAK,CAAC;QAErC,IAAID,MAAM,CAACL,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UAC5BK,MAAM,GAAG,GAAG,GAAGA,MAAM;QACvB;QAEAb,KAAK,GAAG,MAAM,GAAGa,MAAM;MACzB;IACF;IAEAV,IAAI,GAAGV,WAAW;EACpB;EAEA,OAAO,IAAIU,IAAI,CAACD,IAAI,EAAEF,KAAK,CAAC;AAC9B;;AAEA;AACA;AACA;AACA;AACA,SAASc,KAAKA,CAACC,EAAE,EAAE;EACjB,OAAO,GAAG,GAAGA,EAAE,CAACC,WAAW,CAAC,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA,SAASL,SAASA,CAACI,EAAE,EAAE;EACrB,OAAOA,EAAE,CAACP,MAAM,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}