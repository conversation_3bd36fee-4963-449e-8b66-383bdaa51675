{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoice-extracting-agent\\\\src\\\\components\\\\Header.js\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { FaRobot, FaCog, FaQuestionCircle } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  const handleSettings = () => {\n    alert('Settings panel coming soon!\\n\\nFeatures will include:\\n• Theme customization\\n• Language preferences\\n• Notification settings\\n• API configuration\\n• Data export options');\n  };\n  const handleHelp = () => {\n    const helpText = `\n🤖 Agentic AI Billing Assistant - Help\n\nGETTING STARTED:\n1. Upload an invoice (PDF or image) using the upload area\n2. View extracted information in the left panel\n3. Ask questions about your bill in the chat\n4. Use Quick Actions to pay bills or download details\n\nFEATURES:\n• AI-powered invoice extraction\n• Natural language chat interface\n• Payment processing integration\n• Bill analysis and insights\n• General knowledge Q&A\n\nSUPPORTED FORMATS:\n• PDF documents\n• JPG/PNG images\n• Multi-page documents\n\nTIPS:\n• Ask specific questions like \"When is this due?\"\n• Use voice input by clicking the microphone\n• Try general questions about finance or markets\n• Click suggested questions for quick interactions\n\nNeed more help? Contact <NAME_EMAIL>\n    `;\n    alert(helpText.trim());\n  };\n  return /*#__PURE__*/_jsxDEV(motion.header, {\n    className: \"glass-dark border-b border-dark-700 p-4\",\n    initial: {\n      opacity: 0,\n      y: -20\n    },\n    animate: {\n      opacity: 1,\n      y: 0\n    },\n    transition: {\n      duration: 0.6\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"flex items-center space-x-3\",\n        whileHover: {\n          scale: 1.02\n        },\n        transition: {\n          type: \"spring\",\n          stiffness: 400,\n          damping: 10\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center\",\n            animate: {\n              boxShadow: [\"0 0 20px rgba(14, 165, 233, 0.3)\", \"0 0 40px rgba(14, 165, 233, 0.6)\", \"0 0 20px rgba(14, 165, 233, 0.3)\"]\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity\n            },\n            children: /*#__PURE__*/_jsxDEV(FaRobot, {\n              className: \"text-white text-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 70,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-xl font-bold gradient-text-blue\",\n            children: \"Agentic AI Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-400\",\n            children: \"Billing, Payments & Intelligent Interactions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"flex items-center space-x-4\",\n        initial: {\n          opacity: 0,\n          x: 20\n        },\n        animate: {\n          opacity: 1,\n          x: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"w-2 h-2 bg-green-500 rounded-full\",\n            animate: {\n              scale: [1, 1.2, 1],\n              opacity: [1, 0.7, 1]\n            },\n            transition: {\n              duration: 2,\n              repeat: Infinity\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm text-gray-300\",\n            children: \"AI Online\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(motion.button, {\n            className: \"p-2 text-gray-400 hover:text-white hover:bg-dark-700 rounded-lg transition-colors\",\n            whileHover: {\n              scale: 1.1\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            title: \"Settings\",\n            children: /*#__PURE__*/_jsxDEV(FaCog, {\n              className: \"text-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            className: \"p-2 text-gray-400 hover:text-white hover:bg-dark-700 rounded-lg transition-colors\",\n            whileHover: {\n              scale: 1.1\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            title: \"Help\",\n            children: /*#__PURE__*/_jsxDEV(FaQuestionCircle, {\n              className: \"text-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"max-w-7xl mx-auto mt-2\",\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      transition: {\n        duration: 0.8,\n        delay: 0.4\n      },\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-center text-gray-400 text-sm\",\n        children: \"Ask me about your bills, market trends, or any general questions. I'm here to help! \\uD83D\\uDE80\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "motion", "FaRobot", "FaCog", "FaQuestionCircle", "jsxDEV", "_jsxDEV", "Header", "handleSettings", "alert", "handleHelp", "helpText", "trim", "header", "className", "initial", "opacity", "y", "animate", "transition", "duration", "children", "div", "whileHover", "scale", "type", "stiffness", "damping", "boxShadow", "repeat", "Infinity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "x", "delay", "button", "whileTap", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/src/components/Header.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { FaRobot, <PERSON>a<PERSON>og, FaQuestionCircle } from 'react-icons/fa';\n\nconst Header = () => {\n  const handleSettings = () => {\n    alert('Settings panel coming soon!\\n\\nFeatures will include:\\n• Theme customization\\n• Language preferences\\n• Notification settings\\n• API configuration\\n• Data export options');\n  };\n\n  const handleHelp = () => {\n    const helpText = `\n🤖 Agentic AI Billing Assistant - Help\n\nGETTING STARTED:\n1. Upload an invoice (PDF or image) using the upload area\n2. View extracted information in the left panel\n3. Ask questions about your bill in the chat\n4. Use Quick Actions to pay bills or download details\n\nFEATURES:\n• AI-powered invoice extraction\n• Natural language chat interface\n• Payment processing integration\n• Bill analysis and insights\n• General knowledge Q&A\n\nSUPPORTED FORMATS:\n• PDF documents\n• JPG/PNG images\n• Multi-page documents\n\nTIPS:\n• Ask specific questions like \"When is this due?\"\n• Use voice input by clicking the microphone\n• Try general questions about finance or markets\n• Click suggested questions for quick interactions\n\nNeed more help? Contact <NAME_EMAIL>\n    `;\n\n    alert(helpText.trim());\n  };\n\n  return (\n    <motion.header \n      className=\"glass-dark border-b border-dark-700 p-4\"\n      initial={{ opacity: 0, y: -20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.6 }}\n    >\n      <div className=\"max-w-7xl mx-auto flex items-center justify-between\">\n        {/* Logo and Title */}\n        <motion.div \n          className=\"flex items-center space-x-3\"\n          whileHover={{ scale: 1.02 }}\n          transition={{ type: \"spring\", stiffness: 400, damping: 10 }}\n        >\n          <div className=\"relative\">\n            <motion.div\n              className=\"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center\"\n              animate={{ \n                boxShadow: [\n                  \"0 0 20px rgba(14, 165, 233, 0.3)\",\n                  \"0 0 40px rgba(14, 165, 233, 0.6)\",\n                  \"0 0 20px rgba(14, 165, 233, 0.3)\"\n                ]\n              }}\n              transition={{ duration: 2, repeat: Infinity }}\n            >\n              <FaRobot className=\"text-white text-lg\" />\n            </motion.div>\n          </div>\n          <div>\n            <h1 className=\"text-xl font-bold gradient-text-blue\">\n              Agentic AI Assistant\n            </h1>\n            <p className=\"text-sm text-gray-400\">\n              Billing, Payments & Intelligent Interactions\n            </p>\n          </div>\n        </motion.div>\n\n        {/* Status Indicator */}\n        <motion.div \n          className=\"flex items-center space-x-4\"\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n        >\n          <div className=\"flex items-center space-x-2\">\n            <motion.div\n              className=\"w-2 h-2 bg-green-500 rounded-full\"\n              animate={{ \n                scale: [1, 1.2, 1],\n                opacity: [1, 0.7, 1]\n              }}\n              transition={{ duration: 2, repeat: Infinity }}\n            />\n            <span className=\"text-sm text-gray-300\">AI Online</span>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex items-center space-x-2\">\n            <motion.button\n              className=\"p-2 text-gray-400 hover:text-white hover:bg-dark-700 rounded-lg transition-colors\"\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n              title=\"Settings\"\n            >\n              <FaCog className=\"text-lg\" />\n            </motion.button>\n            \n            <motion.button\n              className=\"p-2 text-gray-400 hover:text-white hover:bg-dark-700 rounded-lg transition-colors\"\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n              title=\"Help\"\n            >\n              <FaQuestionCircle className=\"text-lg\" />\n            </motion.button>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Subtitle */}\n      <motion.div \n        className=\"max-w-7xl mx-auto mt-2\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 0.8, delay: 0.4 }}\n      >\n        <p className=\"text-center text-gray-400 text-sm\">\n          Ask me about your bills, market trends, or any general questions. I'm here to help! 🚀\n        </p>\n      </motion.div>\n    </motion.header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,EAAEC,KAAK,EAAEC,gBAAgB,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACnB,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3BC,KAAK,CAAC,2KAA2K,CAAC;EACpL,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,QAAQ,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IAEDF,KAAK,CAACE,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC;EACxB,CAAC;EAED,oBACEN,OAAA,CAACL,MAAM,CAACY,MAAM;IACZC,SAAS,EAAC,yCAAyC;IACnDC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;IAAG,CAAE;IAChCC,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAE;IAC9BE,UAAU,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAAAC,QAAA,gBAE9Bf,OAAA;MAAKQ,SAAS,EAAC,qDAAqD;MAAAO,QAAA,gBAElEf,OAAA,CAACL,MAAM,CAACqB,GAAG;QACTR,SAAS,EAAC,6BAA6B;QACvCS,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5BL,UAAU,EAAE;UAAEM,IAAI,EAAE,QAAQ;UAAEC,SAAS,EAAE,GAAG;UAAEC,OAAO,EAAE;QAAG,CAAE;QAAAN,QAAA,gBAE5Df,OAAA;UAAKQ,SAAS,EAAC,UAAU;UAAAO,QAAA,eACvBf,OAAA,CAACL,MAAM,CAACqB,GAAG;YACTR,SAAS,EAAC,yGAAyG;YACnHI,OAAO,EAAE;cACPU,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;YAEtC,CAAE;YACFT,UAAU,EAAE;cAAEC,QAAQ,EAAE,CAAC;cAAES,MAAM,EAAEC;YAAS,CAAE;YAAAT,QAAA,eAE9Cf,OAAA,CAACJ,OAAO;cAACY,SAAS,EAAC;YAAoB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN5B,OAAA;UAAAe,QAAA,gBACEf,OAAA;YAAIQ,SAAS,EAAC,sCAAsC;YAAAO,QAAA,EAAC;UAErD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL5B,OAAA;YAAGQ,SAAS,EAAC,uBAAuB;YAAAO,QAAA,EAAC;UAErC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb5B,OAAA,CAACL,MAAM,CAACqB,GAAG;QACTR,SAAS,EAAC,6BAA6B;QACvCC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEmB,CAAC,EAAE;QAAG,CAAE;QAC/BjB,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEmB,CAAC,EAAE;QAAE,CAAE;QAC9BhB,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEgB,KAAK,EAAE;QAAI,CAAE;QAAAf,QAAA,gBAE1Cf,OAAA;UAAKQ,SAAS,EAAC,6BAA6B;UAAAO,QAAA,gBAC1Cf,OAAA,CAACL,MAAM,CAACqB,GAAG;YACTR,SAAS,EAAC,mCAAmC;YAC7CI,OAAO,EAAE;cACPM,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;cAClBR,OAAO,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;YACrB,CAAE;YACFG,UAAU,EAAE;cAAEC,QAAQ,EAAE,CAAC;cAAES,MAAM,EAAEC;YAAS;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC,eACF5B,OAAA;YAAMQ,SAAS,EAAC,uBAAuB;YAAAO,QAAA,EAAC;UAAS;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eAGN5B,OAAA;UAAKQ,SAAS,EAAC,6BAA6B;UAAAO,QAAA,gBAC1Cf,OAAA,CAACL,MAAM,CAACoC,MAAM;YACZvB,SAAS,EAAC,mFAAmF;YAC7FS,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bc,QAAQ,EAAE;cAAEd,KAAK,EAAE;YAAK,CAAE;YAC1Be,KAAK,EAAC,UAAU;YAAAlB,QAAA,eAEhBf,OAAA,CAACH,KAAK;cAACW,SAAS,EAAC;YAAS;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eAEhB5B,OAAA,CAACL,MAAM,CAACoC,MAAM;YACZvB,SAAS,EAAC,mFAAmF;YAC7FS,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC3Bc,QAAQ,EAAE;cAAEd,KAAK,EAAE;YAAK,CAAE;YAC1Be,KAAK,EAAC,MAAM;YAAAlB,QAAA,eAEZf,OAAA,CAACF,gBAAgB;cAACU,SAAS,EAAC;YAAS;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGN5B,OAAA,CAACL,MAAM,CAACqB,GAAG;MACTR,SAAS,EAAC,wBAAwB;MAClCC,OAAO,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MACxBE,OAAO,EAAE;QAAEF,OAAO,EAAE;MAAE,CAAE;MACxBG,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEgB,KAAK,EAAE;MAAI,CAAE;MAAAf,QAAA,eAE1Cf,OAAA;QAAGQ,SAAS,EAAC,mCAAmC;QAAAO,QAAA,EAAC;MAEjD;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEpB,CAAC;AAACM,EAAA,GArIIjC,MAAM;AAuIZ,eAAeA,MAAM;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}