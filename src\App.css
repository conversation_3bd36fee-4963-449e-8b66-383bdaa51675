/* Additional custom styles for the App component */

.App {
  text-align: left;
}

/* Smooth transitions for all interactive elements */
* {
  transition: all 0.2s ease-in-out;
}

/* Custom button styles */
.btn-primary {
  @apply bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-medium py-2 px-4 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200;
}

.btn-secondary {
  @apply bg-dark-700 hover:bg-dark-600 text-white font-medium py-2 px-4 rounded-lg border border-dark-600 hover:border-dark-500 transition-all duration-200;
}

.btn-ghost {
  @apply bg-transparent hover:bg-dark-800 text-gray-300 hover:text-white font-medium py-2 px-4 rounded-lg border border-transparent hover:border-dark-600 transition-all duration-200;
}

/* Input styles */
.input-primary {
  @apply bg-dark-800 border border-dark-600 text-white placeholder-gray-400 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
}

/* Card styles */
.card {
  @apply bg-dark-800 border border-dark-700 rounded-xl p-6 shadow-lg;
}

.card-glass {
  @apply bg-white bg-opacity-5 backdrop-blur-lg border border-white border-opacity-10 rounded-xl p-6 shadow-lg;
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive design helpers */
@media (max-width: 768px) {
  .mobile-full {
    width: 100% !important;
  }
  
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-stack {
    flex-direction: column !important;
  }
}

/* Loading spinner */
.spinner {
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-left: 2px solid #0ea5e9;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Glow effects */
.glow-blue {
  box-shadow: 0 0 20px rgba(14, 165, 233, 0.3);
}

.glow-blue:hover {
  box-shadow: 0 0 30px rgba(14, 165, 233, 0.5);
}

.glow-green {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
}

.glow-green:hover {
  box-shadow: 0 0 30px rgba(34, 197, 94, 0.5);
}

/* Text selection */
::selection {
  background-color: rgba(14, 165, 233, 0.3);
  color: white;
}

/* Focus styles */
.focus-ring:focus {
  outline: none;
  ring: 2px;
  ring-color: rgba(14, 165, 233, 0.5);
  ring-offset: 2px;
  ring-offset-color: #0a0a0a;
}
