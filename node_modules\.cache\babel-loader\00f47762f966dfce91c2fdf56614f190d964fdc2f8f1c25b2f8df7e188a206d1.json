{"ast": null, "code": "import { codes } from 'micromark-util-symbol/codes.js';\nimport { values } from 'micromark-util-symbol/values.js';\n\n/**\n * Turn the number (in string form as either hexa- or plain decimal) coming from\n * a numeric character reference into a character.\n *\n * Sort of like `String.fromCharCode(Number.parseInt(value, base))`, but makes\n * non-characters and control characters safe.\n *\n * @param {string} value\n *   Value to decode.\n * @param {number} base\n *   Numeric base.\n * @returns {string}\n *   Character.\n */\nexport function decodeNumericCharacterReference(value, base) {\n  const code = Number.parseInt(value, base);\n  if (\n  // C0 except for HT, LF, FF, CR, space.\n  code < codes.ht || code === codes.vt || code > codes.cr && code < codes.space ||\n  // Control character (DEL) of C0, and C1 controls.\n  code > codes.tilde && code < 160 ||\n  // Lone high surrogates and low surrogates.\n  code > 55295 && code < 57344 ||\n  // Noncharacters.\n  code > 64975 && code < 65008 || /* eslint-disable no-bitwise */\n  (code & 65535) === 65535 || (code & 65535) === 65534 || /* eslint-enable no-bitwise */\n  // Out of range\n  code > 1114111) {\n    return values.replacementCharacter;\n  }\n  return String.fromCharCode(code);\n}", "map": {"version": 3, "names": ["codes", "values", "decodeNumericCharacterReference", "value", "base", "code", "Number", "parseInt", "ht", "vt", "cr", "space", "tilde", "replacementCharacter", "String", "fromCharCode"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/micromark-util-decode-numeric-character-reference/dev/index.js"], "sourcesContent": ["import {codes} from 'micromark-util-symbol/codes.js'\nimport {values} from 'micromark-util-symbol/values.js'\n\n/**\n * Turn the number (in string form as either hexa- or plain decimal) coming from\n * a numeric character reference into a character.\n *\n * Sort of like `String.fromCharCode(Number.parseInt(value, base))`, but makes\n * non-characters and control characters safe.\n *\n * @param {string} value\n *   Value to decode.\n * @param {number} base\n *   Numeric base.\n * @returns {string}\n *   Character.\n */\nexport function decodeNumericCharacterReference(value, base) {\n  const code = Number.parseInt(value, base)\n\n  if (\n    // C0 except for HT, LF, FF, CR, space.\n    code < codes.ht ||\n    code === codes.vt ||\n    (code > codes.cr && code < codes.space) ||\n    // Control character (DEL) of C0, and C1 controls.\n    (code > codes.tilde && code < 160) ||\n    // Lone high surrogates and low surrogates.\n    (code > 55295 && code < 57344) ||\n    // Noncharacters.\n    (code > 64975 && code < 65008) ||\n    /* eslint-disable no-bitwise */\n    (code & 65535) === 65535 ||\n    (code & 65535) === 65534 ||\n    /* eslint-enable no-bitwise */\n    // Out of range\n    code > 1114111\n  ) {\n    return values.replacementCharacter\n  }\n\n  return String.fromCharCode(code)\n}\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,gCAAgC;AACpD,SAAQC,MAAM,QAAO,iCAAiC;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,+BAA+BA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC3D,MAAMC,IAAI,GAAGC,MAAM,CAACC,QAAQ,CAACJ,KAAK,EAAEC,IAAI,CAAC;EAEzC;EACE;EACAC,IAAI,GAAGL,KAAK,CAACQ,EAAE,IACfH,IAAI,KAAKL,KAAK,CAACS,EAAE,IAChBJ,IAAI,GAAGL,KAAK,CAACU,EAAE,IAAIL,IAAI,GAAGL,KAAK,CAACW,KAAM;EACvC;EACCN,IAAI,GAAGL,KAAK,CAACY,KAAK,IAAIP,IAAI,GAAG,GAAI;EAClC;EACCA,IAAI,GAAG,KAAK,IAAIA,IAAI,GAAG,KAAM;EAC9B;EACCA,IAAI,GAAG,KAAK,IAAIA,IAAI,GAAG,KAAM,IAC9B;EACA,CAACA,IAAI,GAAG,KAAK,MAAM,KAAK,IACxB,CAACA,IAAI,GAAG,KAAK,MAAM,KAAK,IACxB;EACA;EACAA,IAAI,GAAG,OAAO,EACd;IACA,OAAOJ,MAAM,CAACY,oBAAoB;EACpC;EAEA,OAAOC,MAAM,CAACC,YAAY,CAACV,IAAI,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}