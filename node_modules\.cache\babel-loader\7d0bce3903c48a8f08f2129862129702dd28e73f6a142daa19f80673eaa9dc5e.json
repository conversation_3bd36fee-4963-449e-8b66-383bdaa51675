{"ast": null, "code": "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Point} Point\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n */\n\nimport { stringifyPosition } from 'unist-util-stringify-position';\n\n/**\n * Message.\n */\nexport class VFileMessage extends Error {\n  /**\n   * Create a message for `reason` at `place` from `origin`.\n   *\n   * When an error is passed in as `reason`, the `stack` is copied.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   *\n   *   > 👉 **Note**: you should use markdown.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns\n   *   Instance of `VFileMessage`.\n   */\n  // To do: next major: expose `undefined` everywhere instead of `null`.\n  constructor(reason, place, origin) {\n    /** @type {[string | null, string | null]} */\n    const parts = [null, null];\n    /** @type {Position} */\n    let position = {\n      // @ts-expect-error: we always follows the structure of `position`.\n      start: {\n        line: null,\n        column: null\n      },\n      // @ts-expect-error: \"\n      end: {\n        line: null,\n        column: null\n      }\n    };\n    super();\n    if (typeof place === 'string') {\n      origin = place;\n      place = undefined;\n    }\n    if (typeof origin === 'string') {\n      const index = origin.indexOf(':');\n      if (index === -1) {\n        parts[1] = origin;\n      } else {\n        parts[0] = origin.slice(0, index);\n        parts[1] = origin.slice(index + 1);\n      }\n    }\n    if (place) {\n      // Node.\n      if ('type' in place || 'position' in place) {\n        if (place.position) {\n          // To do: next major: deep clone.\n          // @ts-expect-error: looks like a position.\n          position = place.position;\n        }\n      }\n      // Position.\n      else if ('start' in place || 'end' in place) {\n        // @ts-expect-error: looks like a position.\n        // To do: next major: deep clone.\n        position = place;\n      }\n      // Point.\n      else if ('line' in place || 'column' in place) {\n        // To do: next major: deep clone.\n        position.start = place;\n      }\n    }\n\n    // Fields from `Error`.\n    /**\n     * Serialized positional info of error.\n     *\n     * On normal errors, this would be something like `ParseError`, buit in\n     * `VFile` messages we use this space to show where an error happened.\n     */\n    this.name = stringifyPosition(place) || '1:1';\n\n    /**\n     * Reason for message.\n     *\n     * @type {string}\n     */\n    this.message = typeof reason === 'object' ? reason.message : reason;\n\n    /**\n     * Stack of message.\n     *\n     * This is used by normal errors to show where something happened in\n     * programming code, irrelevant for `VFile` messages,\n     *\n     * @type {string}\n     */\n    this.stack = '';\n    if (typeof reason === 'object' && reason.stack) {\n      this.stack = reason.stack;\n    }\n\n    /**\n     * Reason for message.\n     *\n     * > 👉 **Note**: you should use markdown.\n     *\n     * @type {string}\n     */\n    this.reason = this.message;\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * State of problem.\n     *\n     * * `true` — marks associated file as no longer processable (error)\n     * * `false` — necessitates a (potential) change (warning)\n     * * `null | undefined` — for things that might not need changing (info)\n     *\n     * @type {boolean | null | undefined}\n     */\n    this.fatal;\n\n    /**\n     * Starting line of error.\n     *\n     * @type {number | null}\n     */\n    this.line = position.start.line;\n\n    /**\n     * Starting column of error.\n     *\n     * @type {number | null}\n     */\n    this.column = position.start.column;\n\n    /**\n     * Full unist position.\n     *\n     * @type {Position | null}\n     */\n    this.position = position;\n\n    /**\n     * Namespace of message (example: `'my-package'`).\n     *\n     * @type {string | null}\n     */\n    this.source = parts[0];\n\n    /**\n     * Category of message (example: `'my-rule'`).\n     *\n     * @type {string | null}\n     */\n    this.ruleId = parts[1];\n\n    /**\n     * Path of a file (used throughout the `VFile` ecosystem).\n     *\n     * @type {string | null}\n     */\n    this.file;\n\n    // The following fields are “well known”.\n    // Not standard.\n    // Feel free to add other non-standard fields to your messages.\n\n    /**\n     * Specify the source value that’s being reported, which is deemed\n     * incorrect.\n     *\n     * @type {string | null}\n     */\n    this.actual;\n\n    /**\n     * Suggest acceptable values that can be used instead of `actual`.\n     *\n     * @type {Array<string> | null}\n     */\n    this.expected;\n\n    /**\n     * Link to docs for the message.\n     *\n     * > 👉 **Note**: this must be an absolute URL that can be passed as `x`\n     * > to `new URL(x)`.\n     *\n     * @type {string | null}\n     */\n    this.url;\n\n    /**\n     * Long form description of the message (you should use markdown).\n     *\n     * @type {string | null}\n     */\n    this.note;\n    /* eslint-enable no-unused-expressions */\n  }\n}\nVFileMessage.prototype.file = '';\nVFileMessage.prototype.name = '';\nVFileMessage.prototype.reason = '';\nVFileMessage.prototype.message = '';\nVFileMessage.prototype.stack = '';\nVFileMessage.prototype.fatal = null;\nVFileMessage.prototype.column = null;\nVFileMessage.prototype.line = null;\nVFileMessage.prototype.source = null;\nVFileMessage.prototype.ruleId = null;\nVFileMessage.prototype.position = null;", "map": {"version": 3, "names": ["stringifyPosition", "VFileMessage", "Error", "constructor", "reason", "place", "origin", "parts", "position", "start", "line", "column", "end", "undefined", "index", "indexOf", "slice", "name", "message", "stack", "fatal", "source", "ruleId", "file", "actual", "expected", "url", "note", "prototype"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/vfile-message/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Point} Point\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n */\n\nimport {stringifyPosition} from 'unist-util-stringify-position'\n\n/**\n * Message.\n */\nexport class VFileMessage extends Error {\n  /**\n   * Create a message for `reason` at `place` from `origin`.\n   *\n   * When an error is passed in as `reason`, the `stack` is copied.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   *\n   *   > 👉 **Note**: you should use markdown.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns\n   *   Instance of `VFileMessage`.\n   */\n  // To do: next major: expose `undefined` everywhere instead of `null`.\n  constructor(reason, place, origin) {\n    /** @type {[string | null, string | null]} */\n    const parts = [null, null]\n    /** @type {Position} */\n    let position = {\n      // @ts-expect-error: we always follows the structure of `position`.\n      start: {line: null, column: null},\n      // @ts-expect-error: \"\n      end: {line: null, column: null}\n    }\n\n    super()\n\n    if (typeof place === 'string') {\n      origin = place\n      place = undefined\n    }\n\n    if (typeof origin === 'string') {\n      const index = origin.indexOf(':')\n\n      if (index === -1) {\n        parts[1] = origin\n      } else {\n        parts[0] = origin.slice(0, index)\n        parts[1] = origin.slice(index + 1)\n      }\n    }\n\n    if (place) {\n      // Node.\n      if ('type' in place || 'position' in place) {\n        if (place.position) {\n          // To do: next major: deep clone.\n          // @ts-expect-error: looks like a position.\n          position = place.position\n        }\n      }\n      // Position.\n      else if ('start' in place || 'end' in place) {\n        // @ts-expect-error: looks like a position.\n        // To do: next major: deep clone.\n        position = place\n      }\n      // Point.\n      else if ('line' in place || 'column' in place) {\n        // To do: next major: deep clone.\n        position.start = place\n      }\n    }\n\n    // Fields from `Error`.\n    /**\n     * Serialized positional info of error.\n     *\n     * On normal errors, this would be something like `ParseError`, buit in\n     * `VFile` messages we use this space to show where an error happened.\n     */\n    this.name = stringifyPosition(place) || '1:1'\n\n    /**\n     * Reason for message.\n     *\n     * @type {string}\n     */\n    this.message = typeof reason === 'object' ? reason.message : reason\n\n    /**\n     * Stack of message.\n     *\n     * This is used by normal errors to show where something happened in\n     * programming code, irrelevant for `VFile` messages,\n     *\n     * @type {string}\n     */\n    this.stack = ''\n\n    if (typeof reason === 'object' && reason.stack) {\n      this.stack = reason.stack\n    }\n\n    /**\n     * Reason for message.\n     *\n     * > 👉 **Note**: you should use markdown.\n     *\n     * @type {string}\n     */\n    this.reason = this.message\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * State of problem.\n     *\n     * * `true` — marks associated file as no longer processable (error)\n     * * `false` — necessitates a (potential) change (warning)\n     * * `null | undefined` — for things that might not need changing (info)\n     *\n     * @type {boolean | null | undefined}\n     */\n    this.fatal\n\n    /**\n     * Starting line of error.\n     *\n     * @type {number | null}\n     */\n    this.line = position.start.line\n\n    /**\n     * Starting column of error.\n     *\n     * @type {number | null}\n     */\n    this.column = position.start.column\n\n    /**\n     * Full unist position.\n     *\n     * @type {Position | null}\n     */\n    this.position = position\n\n    /**\n     * Namespace of message (example: `'my-package'`).\n     *\n     * @type {string | null}\n     */\n    this.source = parts[0]\n\n    /**\n     * Category of message (example: `'my-rule'`).\n     *\n     * @type {string | null}\n     */\n    this.ruleId = parts[1]\n\n    /**\n     * Path of a file (used throughout the `VFile` ecosystem).\n     *\n     * @type {string | null}\n     */\n    this.file\n\n    // The following fields are “well known”.\n    // Not standard.\n    // Feel free to add other non-standard fields to your messages.\n\n    /**\n     * Specify the source value that’s being reported, which is deemed\n     * incorrect.\n     *\n     * @type {string | null}\n     */\n    this.actual\n\n    /**\n     * Suggest acceptable values that can be used instead of `actual`.\n     *\n     * @type {Array<string> | null}\n     */\n    this.expected\n\n    /**\n     * Link to docs for the message.\n     *\n     * > 👉 **Note**: this must be an absolute URL that can be passed as `x`\n     * > to `new URL(x)`.\n     *\n     * @type {string | null}\n     */\n    this.url\n\n    /**\n     * Long form description of the message (you should use markdown).\n     *\n     * @type {string | null}\n     */\n    this.note\n    /* eslint-enable no-unused-expressions */\n  }\n}\n\nVFileMessage.prototype.file = ''\nVFileMessage.prototype.name = ''\nVFileMessage.prototype.reason = ''\nVFileMessage.prototype.message = ''\nVFileMessage.prototype.stack = ''\nVFileMessage.prototype.fatal = null\nVFileMessage.prototype.column = null\nVFileMessage.prototype.line = null\nVFileMessage.prototype.source = null\nVFileMessage.prototype.ruleId = null\nVFileMessage.prototype.position = null\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,iBAAiB,QAAO,+BAA+B;;AAE/D;AACA;AACA;AACA,OAAO,MAAMC,YAAY,SAASC,KAAK,CAAC;EACtC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE;EACAC,WAAWA,CAACC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAE;IACjC;IACA,MAAMC,KAAK,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;IAC1B;IACA,IAAIC,QAAQ,GAAG;MACb;MACAC,KAAK,EAAE;QAACC,IAAI,EAAE,IAAI;QAAEC,MAAM,EAAE;MAAI,CAAC;MACjC;MACAC,GAAG,EAAE;QAACF,IAAI,EAAE,IAAI;QAAEC,MAAM,EAAE;MAAI;IAChC,CAAC;IAED,KAAK,CAAC,CAAC;IAEP,IAAI,OAAON,KAAK,KAAK,QAAQ,EAAE;MAC7BC,MAAM,GAAGD,KAAK;MACdA,KAAK,GAAGQ,SAAS;IACnB;IAEA,IAAI,OAAOP,MAAM,KAAK,QAAQ,EAAE;MAC9B,MAAMQ,KAAK,GAAGR,MAAM,CAACS,OAAO,CAAC,GAAG,CAAC;MAEjC,IAAID,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBP,KAAK,CAAC,CAAC,CAAC,GAAGD,MAAM;MACnB,CAAC,MAAM;QACLC,KAAK,CAAC,CAAC,CAAC,GAAGD,MAAM,CAACU,KAAK,CAAC,CAAC,EAAEF,KAAK,CAAC;QACjCP,KAAK,CAAC,CAAC,CAAC,GAAGD,MAAM,CAACU,KAAK,CAACF,KAAK,GAAG,CAAC,CAAC;MACpC;IACF;IAEA,IAAIT,KAAK,EAAE;MACT;MACA,IAAI,MAAM,IAAIA,KAAK,IAAI,UAAU,IAAIA,KAAK,EAAE;QAC1C,IAAIA,KAAK,CAACG,QAAQ,EAAE;UAClB;UACA;UACAA,QAAQ,GAAGH,KAAK,CAACG,QAAQ;QAC3B;MACF;MACA;MAAA,KACK,IAAI,OAAO,IAAIH,KAAK,IAAI,KAAK,IAAIA,KAAK,EAAE;QAC3C;QACA;QACAG,QAAQ,GAAGH,KAAK;MAClB;MACA;MAAA,KACK,IAAI,MAAM,IAAIA,KAAK,IAAI,QAAQ,IAAIA,KAAK,EAAE;QAC7C;QACAG,QAAQ,CAACC,KAAK,GAAGJ,KAAK;MACxB;IACF;;IAEA;IACA;AACJ;AACA;AACA;AACA;AACA;IACI,IAAI,CAACY,IAAI,GAAGjB,iBAAiB,CAACK,KAAK,CAAC,IAAI,KAAK;;IAE7C;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACa,OAAO,GAAG,OAAOd,MAAM,KAAK,QAAQ,GAAGA,MAAM,CAACc,OAAO,GAAGd,MAAM;;IAEnE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACe,KAAK,GAAG,EAAE;IAEf,IAAI,OAAOf,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACe,KAAK,EAAE;MAC9C,IAAI,CAACA,KAAK,GAAGf,MAAM,CAACe,KAAK;IAC3B;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACf,MAAM,GAAG,IAAI,CAACc,OAAO;;IAE1B;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACE,KAAK;;IAEV;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACV,IAAI,GAAGF,QAAQ,CAACC,KAAK,CAACC,IAAI;;IAE/B;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACC,MAAM,GAAGH,QAAQ,CAACC,KAAK,CAACE,MAAM;;IAEnC;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACH,QAAQ,GAAGA,QAAQ;;IAExB;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACa,MAAM,GAAGd,KAAK,CAAC,CAAC,CAAC;;IAEtB;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACe,MAAM,GAAGf,KAAK,CAAC,CAAC,CAAC;;IAEtB;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACgB,IAAI;;IAET;IACA;IACA;;IAEA;AACJ;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,MAAM;;IAEX;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACC,QAAQ;;IAEb;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,GAAG;;IAER;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACC,IAAI;IACT;EACF;AACF;AAEA1B,YAAY,CAAC2B,SAAS,CAACL,IAAI,GAAG,EAAE;AAChCtB,YAAY,CAAC2B,SAAS,CAACX,IAAI,GAAG,EAAE;AAChChB,YAAY,CAAC2B,SAAS,CAACxB,MAAM,GAAG,EAAE;AAClCH,YAAY,CAAC2B,SAAS,CAACV,OAAO,GAAG,EAAE;AACnCjB,YAAY,CAAC2B,SAAS,CAACT,KAAK,GAAG,EAAE;AACjClB,YAAY,CAAC2B,SAAS,CAACR,KAAK,GAAG,IAAI;AACnCnB,YAAY,CAAC2B,SAAS,CAACjB,MAAM,GAAG,IAAI;AACpCV,YAAY,CAAC2B,SAAS,CAAClB,IAAI,GAAG,IAAI;AAClCT,YAAY,CAAC2B,SAAS,CAACP,MAAM,GAAG,IAAI;AACpCpB,YAAY,CAAC2B,SAAS,CAACN,MAAM,GAAG,IAAI;AACpCrB,YAAY,CAAC2B,SAAS,CAACpB,QAAQ,GAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}