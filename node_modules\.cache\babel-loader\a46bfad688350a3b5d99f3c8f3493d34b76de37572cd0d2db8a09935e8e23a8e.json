{"ast": null, "code": "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('vfile').VFileCompatible} VFileCompatible\n * @typedef {import('vfile').VFileValue} VFileValue\n * @typedef {import('..').Processor} Processor\n * @typedef {import('..').Plugin} Plugin\n * @typedef {import('..').Preset} Preset\n * @typedef {import('..').Pluggable} Pluggable\n * @typedef {import('..').PluggableList} PluggableList\n * @typedef {import('..').Transformer} Transformer\n * @typedef {import('..').Parser} Parser\n * @typedef {import('..').Compiler} Compiler\n * @typedef {import('..').RunCallback} RunCallback\n * @typedef {import('..').ProcessCallback} ProcessCallback\n *\n * @typedef Context\n * @property {Node} tree\n * @property {VFile} file\n */\n\nimport { bail } from 'bail';\nimport isBuffer from 'is-buffer';\nimport extend from 'extend';\nimport isPlainObj from 'is-plain-obj';\nimport { trough } from 'trough';\nimport { VFile } from 'vfile';\n\n// Expose a frozen processor.\nexport const unified = base().freeze();\nconst own = {}.hasOwnProperty;\n\n// Function to create the first processor.\n/**\n * @returns {Processor}\n */\nfunction base() {\n  const transformers = trough();\n  /** @type {Processor['attachers']} */\n  const attachers = [];\n  /** @type {Record<string, unknown>} */\n  let namespace = {};\n  /** @type {boolean|undefined} */\n  let frozen;\n  let freezeIndex = -1;\n\n  // Data management.\n  // @ts-expect-error: overloads are handled.\n  processor.data = data;\n  processor.Parser = undefined;\n  processor.Compiler = undefined;\n\n  // Lock.\n  processor.freeze = freeze;\n\n  // Plugins.\n  processor.attachers = attachers;\n  // @ts-expect-error: overloads are handled.\n  processor.use = use;\n\n  // API.\n  processor.parse = parse;\n  processor.stringify = stringify;\n  // @ts-expect-error: overloads are handled.\n  processor.run = run;\n  processor.runSync = runSync;\n  // @ts-expect-error: overloads are handled.\n  processor.process = process;\n  processor.processSync = processSync;\n\n  // Expose.\n  return processor;\n\n  // Create a new processor based on the processor in the current scope.\n  /** @type {Processor} */\n  function processor() {\n    const destination = base();\n    let index = -1;\n    while (++index < attachers.length) {\n      destination.use(...attachers[index]);\n    }\n    destination.data(extend(true, {}, namespace));\n    return destination;\n  }\n\n  /**\n   * @param {string|Record<string, unknown>} [key]\n   * @param {unknown} [value]\n   * @returns {unknown}\n   */\n  function data(key, value) {\n    if (typeof key === 'string') {\n      // Set `key`.\n      if (arguments.length === 2) {\n        assertUnfrozen('data', frozen);\n        namespace[key] = value;\n        return processor;\n      }\n\n      // Get `key`.\n      return own.call(namespace, key) && namespace[key] || null;\n    }\n\n    // Set space.\n    if (key) {\n      assertUnfrozen('data', frozen);\n      namespace = key;\n      return processor;\n    }\n\n    // Get space.\n    return namespace;\n  }\n\n  /** @type {Processor['freeze']} */\n  function freeze() {\n    if (frozen) {\n      return processor;\n    }\n    while (++freezeIndex < attachers.length) {\n      const [attacher, ...options] = attachers[freezeIndex];\n      if (options[0] === false) {\n        continue;\n      }\n      if (options[0] === true) {\n        options[0] = undefined;\n      }\n\n      /** @type {Transformer|void} */\n      const transformer = attacher.call(processor, ...options);\n      if (typeof transformer === 'function') {\n        transformers.use(transformer);\n      }\n    }\n    frozen = true;\n    freezeIndex = Number.POSITIVE_INFINITY;\n    return processor;\n  }\n\n  /**\n   * @param {Pluggable|null|undefined} [value]\n   * @param {...unknown} options\n   * @returns {Processor}\n   */\n  function use(value, ...options) {\n    /** @type {Record<string, unknown>|undefined} */\n    let settings;\n    assertUnfrozen('use', frozen);\n    if (value === null || value === undefined) {\n      // Empty.\n    } else if (typeof value === 'function') {\n      addPlugin(value, ...options);\n    } else if (typeof value === 'object') {\n      if (Array.isArray(value)) {\n        addList(value);\n      } else {\n        addPreset(value);\n      }\n    } else {\n      throw new TypeError('Expected usable value, not `' + value + '`');\n    }\n    if (settings) {\n      namespace.settings = Object.assign(namespace.settings || {}, settings);\n    }\n    return processor;\n\n    /**\n     * @param {import('..').Pluggable<unknown[]>} value\n     * @returns {void}\n     */\n    function add(value) {\n      if (typeof value === 'function') {\n        addPlugin(value);\n      } else if (typeof value === 'object') {\n        if (Array.isArray(value)) {\n          const [plugin, ...options] = value;\n          addPlugin(plugin, ...options);\n        } else {\n          addPreset(value);\n        }\n      } else {\n        throw new TypeError('Expected usable value, not `' + value + '`');\n      }\n    }\n\n    /**\n     * @param {Preset} result\n     * @returns {void}\n     */\n    function addPreset(result) {\n      addList(result.plugins);\n      if (result.settings) {\n        settings = Object.assign(settings || {}, result.settings);\n      }\n    }\n\n    /**\n     * @param {PluggableList|null|undefined} [plugins]\n     * @returns {void}\n     */\n    function addList(plugins) {\n      let index = -1;\n      if (plugins === null || plugins === undefined) {\n        // Empty.\n      } else if (Array.isArray(plugins)) {\n        while (++index < plugins.length) {\n          const thing = plugins[index];\n          add(thing);\n        }\n      } else {\n        throw new TypeError('Expected a list of plugins, not `' + plugins + '`');\n      }\n    }\n\n    /**\n     * @param {Plugin} plugin\n     * @param {...unknown} [value]\n     * @returns {void}\n     */\n    function addPlugin(plugin, value) {\n      let index = -1;\n      /** @type {Processor['attachers'][number]|undefined} */\n      let entry;\n      while (++index < attachers.length) {\n        if (attachers[index][0] === plugin) {\n          entry = attachers[index];\n          break;\n        }\n      }\n      if (entry) {\n        if (isPlainObj(entry[1]) && isPlainObj(value)) {\n          value = extend(true, entry[1], value);\n        }\n        entry[1] = value;\n      } else {\n        // @ts-expect-error: fine.\n        attachers.push([...arguments]);\n      }\n    }\n  }\n\n  /** @type {Processor['parse']} */\n  function parse(doc) {\n    processor.freeze();\n    const file = vfile(doc);\n    const Parser = processor.Parser;\n    assertParser('parse', Parser);\n    if (newable(Parser, 'parse')) {\n      // @ts-expect-error: `newable` checks this.\n      return new Parser(String(file), file).parse();\n    }\n\n    // @ts-expect-error: `newable` checks this.\n    return Parser(String(file), file); // eslint-disable-line new-cap\n  }\n\n  /** @type {Processor['stringify']} */\n  function stringify(node, doc) {\n    processor.freeze();\n    const file = vfile(doc);\n    const Compiler = processor.Compiler;\n    assertCompiler('stringify', Compiler);\n    assertNode(node);\n    if (newable(Compiler, 'compile')) {\n      // @ts-expect-error: `newable` checks this.\n      return new Compiler(node, file).compile();\n    }\n\n    // @ts-expect-error: `newable` checks this.\n    return Compiler(node, file); // eslint-disable-line new-cap\n  }\n\n  /**\n   * @param {Node} node\n   * @param {VFileCompatible|RunCallback} [doc]\n   * @param {RunCallback} [callback]\n   * @returns {Promise<Node>|void}\n   */\n  function run(node, doc, callback) {\n    assertNode(node);\n    processor.freeze();\n    if (!callback && typeof doc === 'function') {\n      callback = doc;\n      doc = undefined;\n    }\n    if (!callback) {\n      return new Promise(executor);\n    }\n    executor(null, callback);\n\n    /**\n     * @param {null|((node: Node) => void)} resolve\n     * @param {(error: Error) => void} reject\n     * @returns {void}\n     */\n    function executor(resolve, reject) {\n      // @ts-expect-error: `doc` can’t be a callback anymore, we checked.\n      transformers.run(node, vfile(doc), done);\n\n      /**\n       * @param {Error|null} error\n       * @param {Node} tree\n       * @param {VFile} file\n       * @returns {void}\n       */\n      function done(error, tree, file) {\n        tree = tree || node;\n        if (error) {\n          reject(error);\n        } else if (resolve) {\n          resolve(tree);\n        } else {\n          // @ts-expect-error: `callback` is defined if `resolve` is not.\n          callback(null, tree, file);\n        }\n      }\n    }\n  }\n\n  /** @type {Processor['runSync']} */\n  function runSync(node, file) {\n    /** @type {Node|undefined} */\n    let result;\n    /** @type {boolean|undefined} */\n    let complete;\n    processor.run(node, file, done);\n    assertDone('runSync', 'run', complete);\n\n    // @ts-expect-error: we either bailed on an error or have a tree.\n    return result;\n\n    /**\n     * @param {Error|null} [error]\n     * @param {Node} [tree]\n     * @returns {void}\n     */\n    function done(error, tree) {\n      bail(error);\n      result = tree;\n      complete = true;\n    }\n  }\n\n  /**\n   * @param {VFileCompatible} doc\n   * @param {ProcessCallback} [callback]\n   * @returns {Promise<VFile>|undefined}\n   */\n  function process(doc, callback) {\n    processor.freeze();\n    assertParser('process', processor.Parser);\n    assertCompiler('process', processor.Compiler);\n    if (!callback) {\n      return new Promise(executor);\n    }\n    executor(null, callback);\n\n    /**\n     * @param {null|((file: VFile) => void)} resolve\n     * @param {(error?: Error|null|undefined) => void} reject\n     * @returns {void}\n     */\n    function executor(resolve, reject) {\n      const file = vfile(doc);\n      processor.run(processor.parse(file), file, (error, tree, file) => {\n        if (error || !tree || !file) {\n          done(error);\n        } else {\n          /** @type {unknown} */\n          const result = processor.stringify(tree, file);\n          if (result === undefined || result === null) {\n            // Empty.\n          } else if (looksLikeAVFileValue(result)) {\n            file.value = result;\n          } else {\n            file.result = result;\n          }\n          done(error, file);\n        }\n      });\n\n      /**\n       * @param {Error|null|undefined} [error]\n       * @param {VFile|undefined} [file]\n       * @returns {void}\n       */\n      function done(error, file) {\n        if (error || !file) {\n          reject(error);\n        } else if (resolve) {\n          resolve(file);\n        } else {\n          // @ts-expect-error: `callback` is defined if `resolve` is not.\n          callback(null, file);\n        }\n      }\n    }\n  }\n\n  /** @type {Processor['processSync']} */\n  function processSync(doc) {\n    /** @type {boolean|undefined} */\n    let complete;\n    processor.freeze();\n    assertParser('processSync', processor.Parser);\n    assertCompiler('processSync', processor.Compiler);\n    const file = vfile(doc);\n    processor.process(file, done);\n    assertDone('processSync', 'process', complete);\n    return file;\n\n    /**\n     * @param {Error|null|undefined} [error]\n     * @returns {void}\n     */\n    function done(error) {\n      complete = true;\n      bail(error);\n    }\n  }\n}\n\n/**\n * Check if `value` is a constructor.\n *\n * @param {unknown} value\n * @param {string} name\n * @returns {boolean}\n */\nfunction newable(value, name) {\n  return typeof value === 'function' &&\n  // Prototypes do exist.\n  // type-coverage:ignore-next-line\n  value.prototype && (\n  // A function with keys in its prototype is probably a constructor.\n  // Classes’ prototype methods are not enumerable, so we check if some value\n  // exists in the prototype.\n  // type-coverage:ignore-next-line\n  keys(value.prototype) || name in value.prototype);\n}\n\n/**\n * Check if `value` is an object with keys.\n *\n * @param {Record<string, unknown>} value\n * @returns {boolean}\n */\nfunction keys(value) {\n  /** @type {string} */\n  let key;\n  for (key in value) {\n    if (own.call(value, key)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Assert a parser is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Parser}\n */\nfunction assertParser(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `Parser`');\n  }\n}\n\n/**\n * Assert a compiler is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Compiler}\n */\nfunction assertCompiler(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `Compiler`');\n  }\n}\n\n/**\n * Assert the processor is not frozen.\n *\n * @param {string} name\n * @param {unknown} frozen\n * @returns {asserts frozen is false}\n */\nfunction assertUnfrozen(name, frozen) {\n  if (frozen) {\n    throw new Error('Cannot call `' + name + '` on a frozen processor.\\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.');\n  }\n}\n\n/**\n * Assert `node` is a unist node.\n *\n * @param {unknown} node\n * @returns {asserts node is Node}\n */\nfunction assertNode(node) {\n  // `isPlainObj` unfortunately uses `any` instead of `unknown`.\n  // type-coverage:ignore-next-line\n  if (!isPlainObj(node) || typeof node.type !== 'string') {\n    throw new TypeError('Expected node, got `' + node + '`');\n    // Fine.\n  }\n}\n\n/**\n * Assert that `complete` is `true`.\n *\n * @param {string} name\n * @param {string} asyncName\n * @param {unknown} complete\n * @returns {asserts complete is true}\n */\nfunction assertDone(name, asyncName, complete) {\n  if (!complete) {\n    throw new Error('`' + name + '` finished async. Use `' + asyncName + '` instead');\n  }\n}\n\n/**\n * @param {VFileCompatible} [value]\n * @returns {VFile}\n */\nfunction vfile(value) {\n  return looksLikeAVFile(value) ? value : new VFile(value);\n}\n\n/**\n * @param {VFileCompatible} [value]\n * @returns {value is VFile}\n */\nfunction looksLikeAVFile(value) {\n  return Boolean(value && typeof value === 'object' && 'message' in value && 'messages' in value);\n}\n\n/**\n * @param {unknown} [value]\n * @returns {value is VFileValue}\n */\nfunction looksLikeAVFileValue(value) {\n  return typeof value === 'string' || isBuffer(value);\n}", "map": {"version": 3, "names": ["bail", "<PERSON><PERSON><PERSON><PERSON>", "extend", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trough", "VFile", "unified", "base", "freeze", "own", "hasOwnProperty", "transformers", "attachers", "namespace", "frozen", "freezeIndex", "processor", "data", "<PERSON><PERSON><PERSON>", "undefined", "Compiler", "use", "parse", "stringify", "run", "runSync", "process", "processSync", "destination", "index", "length", "key", "value", "arguments", "assertUnfrozen", "call", "attacher", "options", "transformer", "Number", "POSITIVE_INFINITY", "settings", "addPlugin", "Array", "isArray", "addList", "addPreset", "TypeError", "Object", "assign", "add", "plugin", "result", "plugins", "thing", "entry", "push", "doc", "file", "vfile", "assertParser", "newable", "String", "node", "assertCompiler", "assertNode", "compile", "callback", "Promise", "executor", "resolve", "reject", "done", "error", "tree", "complete", "assertDone", "looksLikeAVFileValue", "name", "prototype", "keys", "Error", "type", "asyncName", "looksLikeAVFile", "Boolean"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/unified/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('vfile').VFileCompatible} VFileCompatible\n * @typedef {import('vfile').VFileValue} VFileValue\n * @typedef {import('..').Processor} Processor\n * @typedef {import('..').Plugin} Plugin\n * @typedef {import('..').Preset} Preset\n * @typedef {import('..').Pluggable} Pluggable\n * @typedef {import('..').PluggableList} PluggableList\n * @typedef {import('..').Transformer} Transformer\n * @typedef {import('..').Parser} Parser\n * @typedef {import('..').Compiler} Compiler\n * @typedef {import('..').RunCallback} RunCallback\n * @typedef {import('..').ProcessCallback} ProcessCallback\n *\n * @typedef Context\n * @property {Node} tree\n * @property {VFile} file\n */\n\nimport {bail} from 'bail'\nimport isBuffer from 'is-buffer'\nimport extend from 'extend'\nimport isPlainObj from 'is-plain-obj'\nimport {trough} from 'trough'\nimport {VFile} from 'vfile'\n\n// Expose a frozen processor.\nexport const unified = base().freeze()\n\nconst own = {}.hasOwnProperty\n\n// Function to create the first processor.\n/**\n * @returns {Processor}\n */\nfunction base() {\n  const transformers = trough()\n  /** @type {Processor['attachers']} */\n  const attachers = []\n  /** @type {Record<string, unknown>} */\n  let namespace = {}\n  /** @type {boolean|undefined} */\n  let frozen\n  let freezeIndex = -1\n\n  // Data management.\n  // @ts-expect-error: overloads are handled.\n  processor.data = data\n  processor.Parser = undefined\n  processor.Compiler = undefined\n\n  // Lock.\n  processor.freeze = freeze\n\n  // Plugins.\n  processor.attachers = attachers\n  // @ts-expect-error: overloads are handled.\n  processor.use = use\n\n  // API.\n  processor.parse = parse\n  processor.stringify = stringify\n  // @ts-expect-error: overloads are handled.\n  processor.run = run\n  processor.runSync = runSync\n  // @ts-expect-error: overloads are handled.\n  processor.process = process\n  processor.processSync = processSync\n\n  // Expose.\n  return processor\n\n  // Create a new processor based on the processor in the current scope.\n  /** @type {Processor} */\n  function processor() {\n    const destination = base()\n    let index = -1\n\n    while (++index < attachers.length) {\n      destination.use(...attachers[index])\n    }\n\n    destination.data(extend(true, {}, namespace))\n\n    return destination\n  }\n\n  /**\n   * @param {string|Record<string, unknown>} [key]\n   * @param {unknown} [value]\n   * @returns {unknown}\n   */\n  function data(key, value) {\n    if (typeof key === 'string') {\n      // Set `key`.\n      if (arguments.length === 2) {\n        assertUnfrozen('data', frozen)\n        namespace[key] = value\n        return processor\n      }\n\n      // Get `key`.\n      return (own.call(namespace, key) && namespace[key]) || null\n    }\n\n    // Set space.\n    if (key) {\n      assertUnfrozen('data', frozen)\n      namespace = key\n      return processor\n    }\n\n    // Get space.\n    return namespace\n  }\n\n  /** @type {Processor['freeze']} */\n  function freeze() {\n    if (frozen) {\n      return processor\n    }\n\n    while (++freezeIndex < attachers.length) {\n      const [attacher, ...options] = attachers[freezeIndex]\n\n      if (options[0] === false) {\n        continue\n      }\n\n      if (options[0] === true) {\n        options[0] = undefined\n      }\n\n      /** @type {Transformer|void} */\n      const transformer = attacher.call(processor, ...options)\n\n      if (typeof transformer === 'function') {\n        transformers.use(transformer)\n      }\n    }\n\n    frozen = true\n    freezeIndex = Number.POSITIVE_INFINITY\n\n    return processor\n  }\n\n  /**\n   * @param {Pluggable|null|undefined} [value]\n   * @param {...unknown} options\n   * @returns {Processor}\n   */\n  function use(value, ...options) {\n    /** @type {Record<string, unknown>|undefined} */\n    let settings\n\n    assertUnfrozen('use', frozen)\n\n    if (value === null || value === undefined) {\n      // Empty.\n    } else if (typeof value === 'function') {\n      addPlugin(value, ...options)\n    } else if (typeof value === 'object') {\n      if (Array.isArray(value)) {\n        addList(value)\n      } else {\n        addPreset(value)\n      }\n    } else {\n      throw new TypeError('Expected usable value, not `' + value + '`')\n    }\n\n    if (settings) {\n      namespace.settings = Object.assign(namespace.settings || {}, settings)\n    }\n\n    return processor\n\n    /**\n     * @param {import('..').Pluggable<unknown[]>} value\n     * @returns {void}\n     */\n    function add(value) {\n      if (typeof value === 'function') {\n        addPlugin(value)\n      } else if (typeof value === 'object') {\n        if (Array.isArray(value)) {\n          const [plugin, ...options] = value\n          addPlugin(plugin, ...options)\n        } else {\n          addPreset(value)\n        }\n      } else {\n        throw new TypeError('Expected usable value, not `' + value + '`')\n      }\n    }\n\n    /**\n     * @param {Preset} result\n     * @returns {void}\n     */\n    function addPreset(result) {\n      addList(result.plugins)\n\n      if (result.settings) {\n        settings = Object.assign(settings || {}, result.settings)\n      }\n    }\n\n    /**\n     * @param {PluggableList|null|undefined} [plugins]\n     * @returns {void}\n     */\n    function addList(plugins) {\n      let index = -1\n\n      if (plugins === null || plugins === undefined) {\n        // Empty.\n      } else if (Array.isArray(plugins)) {\n        while (++index < plugins.length) {\n          const thing = plugins[index]\n          add(thing)\n        }\n      } else {\n        throw new TypeError('Expected a list of plugins, not `' + plugins + '`')\n      }\n    }\n\n    /**\n     * @param {Plugin} plugin\n     * @param {...unknown} [value]\n     * @returns {void}\n     */\n    function addPlugin(plugin, value) {\n      let index = -1\n      /** @type {Processor['attachers'][number]|undefined} */\n      let entry\n\n      while (++index < attachers.length) {\n        if (attachers[index][0] === plugin) {\n          entry = attachers[index]\n          break\n        }\n      }\n\n      if (entry) {\n        if (isPlainObj(entry[1]) && isPlainObj(value)) {\n          value = extend(true, entry[1], value)\n        }\n\n        entry[1] = value\n      } else {\n        // @ts-expect-error: fine.\n        attachers.push([...arguments])\n      }\n    }\n  }\n\n  /** @type {Processor['parse']} */\n  function parse(doc) {\n    processor.freeze()\n    const file = vfile(doc)\n    const Parser = processor.Parser\n    assertParser('parse', Parser)\n\n    if (newable(Parser, 'parse')) {\n      // @ts-expect-error: `newable` checks this.\n      return new Parser(String(file), file).parse()\n    }\n\n    // @ts-expect-error: `newable` checks this.\n    return Parser(String(file), file) // eslint-disable-line new-cap\n  }\n\n  /** @type {Processor['stringify']} */\n  function stringify(node, doc) {\n    processor.freeze()\n    const file = vfile(doc)\n    const Compiler = processor.Compiler\n    assertCompiler('stringify', Compiler)\n    assertNode(node)\n\n    if (newable(Compiler, 'compile')) {\n      // @ts-expect-error: `newable` checks this.\n      return new Compiler(node, file).compile()\n    }\n\n    // @ts-expect-error: `newable` checks this.\n    return Compiler(node, file) // eslint-disable-line new-cap\n  }\n\n  /**\n   * @param {Node} node\n   * @param {VFileCompatible|RunCallback} [doc]\n   * @param {RunCallback} [callback]\n   * @returns {Promise<Node>|void}\n   */\n  function run(node, doc, callback) {\n    assertNode(node)\n    processor.freeze()\n\n    if (!callback && typeof doc === 'function') {\n      callback = doc\n      doc = undefined\n    }\n\n    if (!callback) {\n      return new Promise(executor)\n    }\n\n    executor(null, callback)\n\n    /**\n     * @param {null|((node: Node) => void)} resolve\n     * @param {(error: Error) => void} reject\n     * @returns {void}\n     */\n    function executor(resolve, reject) {\n      // @ts-expect-error: `doc` can’t be a callback anymore, we checked.\n      transformers.run(node, vfile(doc), done)\n\n      /**\n       * @param {Error|null} error\n       * @param {Node} tree\n       * @param {VFile} file\n       * @returns {void}\n       */\n      function done(error, tree, file) {\n        tree = tree || node\n        if (error) {\n          reject(error)\n        } else if (resolve) {\n          resolve(tree)\n        } else {\n          // @ts-expect-error: `callback` is defined if `resolve` is not.\n          callback(null, tree, file)\n        }\n      }\n    }\n  }\n\n  /** @type {Processor['runSync']} */\n  function runSync(node, file) {\n    /** @type {Node|undefined} */\n    let result\n    /** @type {boolean|undefined} */\n    let complete\n\n    processor.run(node, file, done)\n\n    assertDone('runSync', 'run', complete)\n\n    // @ts-expect-error: we either bailed on an error or have a tree.\n    return result\n\n    /**\n     * @param {Error|null} [error]\n     * @param {Node} [tree]\n     * @returns {void}\n     */\n    function done(error, tree) {\n      bail(error)\n      result = tree\n      complete = true\n    }\n  }\n\n  /**\n   * @param {VFileCompatible} doc\n   * @param {ProcessCallback} [callback]\n   * @returns {Promise<VFile>|undefined}\n   */\n  function process(doc, callback) {\n    processor.freeze()\n    assertParser('process', processor.Parser)\n    assertCompiler('process', processor.Compiler)\n\n    if (!callback) {\n      return new Promise(executor)\n    }\n\n    executor(null, callback)\n\n    /**\n     * @param {null|((file: VFile) => void)} resolve\n     * @param {(error?: Error|null|undefined) => void} reject\n     * @returns {void}\n     */\n    function executor(resolve, reject) {\n      const file = vfile(doc)\n\n      processor.run(processor.parse(file), file, (error, tree, file) => {\n        if (error || !tree || !file) {\n          done(error)\n        } else {\n          /** @type {unknown} */\n          const result = processor.stringify(tree, file)\n\n          if (result === undefined || result === null) {\n            // Empty.\n          } else if (looksLikeAVFileValue(result)) {\n            file.value = result\n          } else {\n            file.result = result\n          }\n\n          done(error, file)\n        }\n      })\n\n      /**\n       * @param {Error|null|undefined} [error]\n       * @param {VFile|undefined} [file]\n       * @returns {void}\n       */\n      function done(error, file) {\n        if (error || !file) {\n          reject(error)\n        } else if (resolve) {\n          resolve(file)\n        } else {\n          // @ts-expect-error: `callback` is defined if `resolve` is not.\n          callback(null, file)\n        }\n      }\n    }\n  }\n\n  /** @type {Processor['processSync']} */\n  function processSync(doc) {\n    /** @type {boolean|undefined} */\n    let complete\n\n    processor.freeze()\n    assertParser('processSync', processor.Parser)\n    assertCompiler('processSync', processor.Compiler)\n\n    const file = vfile(doc)\n\n    processor.process(file, done)\n\n    assertDone('processSync', 'process', complete)\n\n    return file\n\n    /**\n     * @param {Error|null|undefined} [error]\n     * @returns {void}\n     */\n    function done(error) {\n      complete = true\n      bail(error)\n    }\n  }\n}\n\n/**\n * Check if `value` is a constructor.\n *\n * @param {unknown} value\n * @param {string} name\n * @returns {boolean}\n */\nfunction newable(value, name) {\n  return (\n    typeof value === 'function' &&\n    // Prototypes do exist.\n    // type-coverage:ignore-next-line\n    value.prototype &&\n    // A function with keys in its prototype is probably a constructor.\n    // Classes’ prototype methods are not enumerable, so we check if some value\n    // exists in the prototype.\n    // type-coverage:ignore-next-line\n    (keys(value.prototype) || name in value.prototype)\n  )\n}\n\n/**\n * Check if `value` is an object with keys.\n *\n * @param {Record<string, unknown>} value\n * @returns {boolean}\n */\nfunction keys(value) {\n  /** @type {string} */\n  let key\n\n  for (key in value) {\n    if (own.call(value, key)) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Assert a parser is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Parser}\n */\nfunction assertParser(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `Parser`')\n  }\n}\n\n/**\n * Assert a compiler is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Compiler}\n */\nfunction assertCompiler(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `Compiler`')\n  }\n}\n\n/**\n * Assert the processor is not frozen.\n *\n * @param {string} name\n * @param {unknown} frozen\n * @returns {asserts frozen is false}\n */\nfunction assertUnfrozen(name, frozen) {\n  if (frozen) {\n    throw new Error(\n      'Cannot call `' +\n        name +\n        '` on a frozen processor.\\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.'\n    )\n  }\n}\n\n/**\n * Assert `node` is a unist node.\n *\n * @param {unknown} node\n * @returns {asserts node is Node}\n */\nfunction assertNode(node) {\n  // `isPlainObj` unfortunately uses `any` instead of `unknown`.\n  // type-coverage:ignore-next-line\n  if (!isPlainObj(node) || typeof node.type !== 'string') {\n    throw new TypeError('Expected node, got `' + node + '`')\n    // Fine.\n  }\n}\n\n/**\n * Assert that `complete` is `true`.\n *\n * @param {string} name\n * @param {string} asyncName\n * @param {unknown} complete\n * @returns {asserts complete is true}\n */\nfunction assertDone(name, asyncName, complete) {\n  if (!complete) {\n    throw new Error(\n      '`' + name + '` finished async. Use `' + asyncName + '` instead'\n    )\n  }\n}\n\n/**\n * @param {VFileCompatible} [value]\n * @returns {VFile}\n */\nfunction vfile(value) {\n  return looksLikeAVFile(value) ? value : new VFile(value)\n}\n\n/**\n * @param {VFileCompatible} [value]\n * @returns {value is VFile}\n */\nfunction looksLikeAVFile(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'message' in value &&\n      'messages' in value\n  )\n}\n\n/**\n * @param {unknown} [value]\n * @returns {value is VFileValue}\n */\nfunction looksLikeAVFileValue(value) {\n  return typeof value === 'string' || isBuffer(value)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,IAAI,QAAO,MAAM;AACzB,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,MAAM,MAAM,QAAQ;AAC3B,OAAOC,UAAU,MAAM,cAAc;AACrC,SAAQC,MAAM,QAAO,QAAQ;AAC7B,SAAQC,KAAK,QAAO,OAAO;;AAE3B;AACA,OAAO,MAAMC,OAAO,GAAGC,IAAI,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AAEtC,MAAMC,GAAG,GAAG,CAAC,CAAC,CAACC,cAAc;;AAE7B;AACA;AACA;AACA;AACA,SAASH,IAAIA,CAAA,EAAG;EACd,MAAMI,YAAY,GAAGP,MAAM,CAAC,CAAC;EAC7B;EACA,MAAMQ,SAAS,GAAG,EAAE;EACpB;EACA,IAAIC,SAAS,GAAG,CAAC,CAAC;EAClB;EACA,IAAIC,MAAM;EACV,IAAIC,WAAW,GAAG,CAAC,CAAC;;EAEpB;EACA;EACAC,SAAS,CAACC,IAAI,GAAGA,IAAI;EACrBD,SAAS,CAACE,MAAM,GAAGC,SAAS;EAC5BH,SAAS,CAACI,QAAQ,GAAGD,SAAS;;EAE9B;EACAH,SAAS,CAACR,MAAM,GAAGA,MAAM;;EAEzB;EACAQ,SAAS,CAACJ,SAAS,GAAGA,SAAS;EAC/B;EACAI,SAAS,CAACK,GAAG,GAAGA,GAAG;;EAEnB;EACAL,SAAS,CAACM,KAAK,GAAGA,KAAK;EACvBN,SAAS,CAACO,SAAS,GAAGA,SAAS;EAC/B;EACAP,SAAS,CAACQ,GAAG,GAAGA,GAAG;EACnBR,SAAS,CAACS,OAAO,GAAGA,OAAO;EAC3B;EACAT,SAAS,CAACU,OAAO,GAAGA,OAAO;EAC3BV,SAAS,CAACW,WAAW,GAAGA,WAAW;;EAEnC;EACA,OAAOX,SAAS;;EAEhB;EACA;EACA,SAASA,SAASA,CAAA,EAAG;IACnB,MAAMY,WAAW,GAAGrB,IAAI,CAAC,CAAC;IAC1B,IAAIsB,KAAK,GAAG,CAAC,CAAC;IAEd,OAAO,EAAEA,KAAK,GAAGjB,SAAS,CAACkB,MAAM,EAAE;MACjCF,WAAW,CAACP,GAAG,CAAC,GAAGT,SAAS,CAACiB,KAAK,CAAC,CAAC;IACtC;IAEAD,WAAW,CAACX,IAAI,CAACf,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,EAAEW,SAAS,CAAC,CAAC;IAE7C,OAAOe,WAAW;EACpB;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASX,IAAIA,CAACc,GAAG,EAAEC,KAAK,EAAE;IACxB,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;MAC3B;MACA,IAAIE,SAAS,CAACH,MAAM,KAAK,CAAC,EAAE;QAC1BI,cAAc,CAAC,MAAM,EAAEpB,MAAM,CAAC;QAC9BD,SAAS,CAACkB,GAAG,CAAC,GAAGC,KAAK;QACtB,OAAOhB,SAAS;MAClB;;MAEA;MACA,OAAQP,GAAG,CAAC0B,IAAI,CAACtB,SAAS,EAAEkB,GAAG,CAAC,IAAIlB,SAAS,CAACkB,GAAG,CAAC,IAAK,IAAI;IAC7D;;IAEA;IACA,IAAIA,GAAG,EAAE;MACPG,cAAc,CAAC,MAAM,EAAEpB,MAAM,CAAC;MAC9BD,SAAS,GAAGkB,GAAG;MACf,OAAOf,SAAS;IAClB;;IAEA;IACA,OAAOH,SAAS;EAClB;;EAEA;EACA,SAASL,MAAMA,CAAA,EAAG;IAChB,IAAIM,MAAM,EAAE;MACV,OAAOE,SAAS;IAClB;IAEA,OAAO,EAAED,WAAW,GAAGH,SAAS,CAACkB,MAAM,EAAE;MACvC,MAAM,CAACM,QAAQ,EAAE,GAAGC,OAAO,CAAC,GAAGzB,SAAS,CAACG,WAAW,CAAC;MAErD,IAAIsB,OAAO,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;QACxB;MACF;MAEA,IAAIA,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;QACvBA,OAAO,CAAC,CAAC,CAAC,GAAGlB,SAAS;MACxB;;MAEA;MACA,MAAMmB,WAAW,GAAGF,QAAQ,CAACD,IAAI,CAACnB,SAAS,EAAE,GAAGqB,OAAO,CAAC;MAExD,IAAI,OAAOC,WAAW,KAAK,UAAU,EAAE;QACrC3B,YAAY,CAACU,GAAG,CAACiB,WAAW,CAAC;MAC/B;IACF;IAEAxB,MAAM,GAAG,IAAI;IACbC,WAAW,GAAGwB,MAAM,CAACC,iBAAiB;IAEtC,OAAOxB,SAAS;EAClB;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASK,GAAGA,CAACW,KAAK,EAAE,GAAGK,OAAO,EAAE;IAC9B;IACA,IAAII,QAAQ;IAEZP,cAAc,CAAC,KAAK,EAAEpB,MAAM,CAAC;IAE7B,IAAIkB,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKb,SAAS,EAAE;MACzC;IAAA,CACD,MAAM,IAAI,OAAOa,KAAK,KAAK,UAAU,EAAE;MACtCU,SAAS,CAACV,KAAK,EAAE,GAAGK,OAAO,CAAC;IAC9B,CAAC,MAAM,IAAI,OAAOL,KAAK,KAAK,QAAQ,EAAE;MACpC,IAAIW,KAAK,CAACC,OAAO,CAACZ,KAAK,CAAC,EAAE;QACxBa,OAAO,CAACb,KAAK,CAAC;MAChB,CAAC,MAAM;QACLc,SAAS,CAACd,KAAK,CAAC;MAClB;IACF,CAAC,MAAM;MACL,MAAM,IAAIe,SAAS,CAAC,8BAA8B,GAAGf,KAAK,GAAG,GAAG,CAAC;IACnE;IAEA,IAAIS,QAAQ,EAAE;MACZ5B,SAAS,CAAC4B,QAAQ,GAAGO,MAAM,CAACC,MAAM,CAACpC,SAAS,CAAC4B,QAAQ,IAAI,CAAC,CAAC,EAAEA,QAAQ,CAAC;IACxE;IAEA,OAAOzB,SAAS;;IAEhB;AACJ;AACA;AACA;IACI,SAASkC,GAAGA,CAAClB,KAAK,EAAE;MAClB,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;QAC/BU,SAAS,CAACV,KAAK,CAAC;MAClB,CAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QACpC,IAAIW,KAAK,CAACC,OAAO,CAACZ,KAAK,CAAC,EAAE;UACxB,MAAM,CAACmB,MAAM,EAAE,GAAGd,OAAO,CAAC,GAAGL,KAAK;UAClCU,SAAS,CAACS,MAAM,EAAE,GAAGd,OAAO,CAAC;QAC/B,CAAC,MAAM;UACLS,SAAS,CAACd,KAAK,CAAC;QAClB;MACF,CAAC,MAAM;QACL,MAAM,IAAIe,SAAS,CAAC,8BAA8B,GAAGf,KAAK,GAAG,GAAG,CAAC;MACnE;IACF;;IAEA;AACJ;AACA;AACA;IACI,SAASc,SAASA,CAACM,MAAM,EAAE;MACzBP,OAAO,CAACO,MAAM,CAACC,OAAO,CAAC;MAEvB,IAAID,MAAM,CAACX,QAAQ,EAAE;QACnBA,QAAQ,GAAGO,MAAM,CAACC,MAAM,CAACR,QAAQ,IAAI,CAAC,CAAC,EAAEW,MAAM,CAACX,QAAQ,CAAC;MAC3D;IACF;;IAEA;AACJ;AACA;AACA;IACI,SAASI,OAAOA,CAACQ,OAAO,EAAE;MACxB,IAAIxB,KAAK,GAAG,CAAC,CAAC;MAEd,IAAIwB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKlC,SAAS,EAAE;QAC7C;MAAA,CACD,MAAM,IAAIwB,KAAK,CAACC,OAAO,CAACS,OAAO,CAAC,EAAE;QACjC,OAAO,EAAExB,KAAK,GAAGwB,OAAO,CAACvB,MAAM,EAAE;UAC/B,MAAMwB,KAAK,GAAGD,OAAO,CAACxB,KAAK,CAAC;UAC5BqB,GAAG,CAACI,KAAK,CAAC;QACZ;MACF,CAAC,MAAM;QACL,MAAM,IAAIP,SAAS,CAAC,mCAAmC,GAAGM,OAAO,GAAG,GAAG,CAAC;MAC1E;IACF;;IAEA;AACJ;AACA;AACA;AACA;IACI,SAASX,SAASA,CAACS,MAAM,EAAEnB,KAAK,EAAE;MAChC,IAAIH,KAAK,GAAG,CAAC,CAAC;MACd;MACA,IAAI0B,KAAK;MAET,OAAO,EAAE1B,KAAK,GAAGjB,SAAS,CAACkB,MAAM,EAAE;QACjC,IAAIlB,SAAS,CAACiB,KAAK,CAAC,CAAC,CAAC,CAAC,KAAKsB,MAAM,EAAE;UAClCI,KAAK,GAAG3C,SAAS,CAACiB,KAAK,CAAC;UACxB;QACF;MACF;MAEA,IAAI0B,KAAK,EAAE;QACT,IAAIpD,UAAU,CAACoD,KAAK,CAAC,CAAC,CAAC,CAAC,IAAIpD,UAAU,CAAC6B,KAAK,CAAC,EAAE;UAC7CA,KAAK,GAAG9B,MAAM,CAAC,IAAI,EAAEqD,KAAK,CAAC,CAAC,CAAC,EAAEvB,KAAK,CAAC;QACvC;QAEAuB,KAAK,CAAC,CAAC,CAAC,GAAGvB,KAAK;MAClB,CAAC,MAAM;QACL;QACApB,SAAS,CAAC4C,IAAI,CAAC,CAAC,GAAGvB,SAAS,CAAC,CAAC;MAChC;IACF;EACF;;EAEA;EACA,SAASX,KAAKA,CAACmC,GAAG,EAAE;IAClBzC,SAAS,CAACR,MAAM,CAAC,CAAC;IAClB,MAAMkD,IAAI,GAAGC,KAAK,CAACF,GAAG,CAAC;IACvB,MAAMvC,MAAM,GAAGF,SAAS,CAACE,MAAM;IAC/B0C,YAAY,CAAC,OAAO,EAAE1C,MAAM,CAAC;IAE7B,IAAI2C,OAAO,CAAC3C,MAAM,EAAE,OAAO,CAAC,EAAE;MAC5B;MACA,OAAO,IAAIA,MAAM,CAAC4C,MAAM,CAACJ,IAAI,CAAC,EAAEA,IAAI,CAAC,CAACpC,KAAK,CAAC,CAAC;IAC/C;;IAEA;IACA,OAAOJ,MAAM,CAAC4C,MAAM,CAACJ,IAAI,CAAC,EAAEA,IAAI,CAAC,EAAC;EACpC;;EAEA;EACA,SAASnC,SAASA,CAACwC,IAAI,EAAEN,GAAG,EAAE;IAC5BzC,SAAS,CAACR,MAAM,CAAC,CAAC;IAClB,MAAMkD,IAAI,GAAGC,KAAK,CAACF,GAAG,CAAC;IACvB,MAAMrC,QAAQ,GAAGJ,SAAS,CAACI,QAAQ;IACnC4C,cAAc,CAAC,WAAW,EAAE5C,QAAQ,CAAC;IACrC6C,UAAU,CAACF,IAAI,CAAC;IAEhB,IAAIF,OAAO,CAACzC,QAAQ,EAAE,SAAS,CAAC,EAAE;MAChC;MACA,OAAO,IAAIA,QAAQ,CAAC2C,IAAI,EAAEL,IAAI,CAAC,CAACQ,OAAO,CAAC,CAAC;IAC3C;;IAEA;IACA,OAAO9C,QAAQ,CAAC2C,IAAI,EAAEL,IAAI,CAAC,EAAC;EAC9B;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASlC,GAAGA,CAACuC,IAAI,EAAEN,GAAG,EAAEU,QAAQ,EAAE;IAChCF,UAAU,CAACF,IAAI,CAAC;IAChB/C,SAAS,CAACR,MAAM,CAAC,CAAC;IAElB,IAAI,CAAC2D,QAAQ,IAAI,OAAOV,GAAG,KAAK,UAAU,EAAE;MAC1CU,QAAQ,GAAGV,GAAG;MACdA,GAAG,GAAGtC,SAAS;IACjB;IAEA,IAAI,CAACgD,QAAQ,EAAE;MACb,OAAO,IAAIC,OAAO,CAACC,QAAQ,CAAC;IAC9B;IAEAA,QAAQ,CAAC,IAAI,EAAEF,QAAQ,CAAC;;IAExB;AACJ;AACA;AACA;AACA;IACI,SAASE,QAAQA,CAACC,OAAO,EAAEC,MAAM,EAAE;MACjC;MACA5D,YAAY,CAACa,GAAG,CAACuC,IAAI,EAAEJ,KAAK,CAACF,GAAG,CAAC,EAAEe,IAAI,CAAC;;MAExC;AACN;AACA;AACA;AACA;AACA;MACM,SAASA,IAAIA,CAACC,KAAK,EAAEC,IAAI,EAAEhB,IAAI,EAAE;QAC/BgB,IAAI,GAAGA,IAAI,IAAIX,IAAI;QACnB,IAAIU,KAAK,EAAE;UACTF,MAAM,CAACE,KAAK,CAAC;QACf,CAAC,MAAM,IAAIH,OAAO,EAAE;UAClBA,OAAO,CAACI,IAAI,CAAC;QACf,CAAC,MAAM;UACL;UACAP,QAAQ,CAAC,IAAI,EAAEO,IAAI,EAAEhB,IAAI,CAAC;QAC5B;MACF;IACF;EACF;;EAEA;EACA,SAASjC,OAAOA,CAACsC,IAAI,EAAEL,IAAI,EAAE;IAC3B;IACA,IAAIN,MAAM;IACV;IACA,IAAIuB,QAAQ;IAEZ3D,SAAS,CAACQ,GAAG,CAACuC,IAAI,EAAEL,IAAI,EAAEc,IAAI,CAAC;IAE/BI,UAAU,CAAC,SAAS,EAAE,KAAK,EAAED,QAAQ,CAAC;;IAEtC;IACA,OAAOvB,MAAM;;IAEb;AACJ;AACA;AACA;AACA;IACI,SAASoB,IAAIA,CAACC,KAAK,EAAEC,IAAI,EAAE;MACzB1E,IAAI,CAACyE,KAAK,CAAC;MACXrB,MAAM,GAAGsB,IAAI;MACbC,QAAQ,GAAG,IAAI;IACjB;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASjD,OAAOA,CAAC+B,GAAG,EAAEU,QAAQ,EAAE;IAC9BnD,SAAS,CAACR,MAAM,CAAC,CAAC;IAClBoD,YAAY,CAAC,SAAS,EAAE5C,SAAS,CAACE,MAAM,CAAC;IACzC8C,cAAc,CAAC,SAAS,EAAEhD,SAAS,CAACI,QAAQ,CAAC;IAE7C,IAAI,CAAC+C,QAAQ,EAAE;MACb,OAAO,IAAIC,OAAO,CAACC,QAAQ,CAAC;IAC9B;IAEAA,QAAQ,CAAC,IAAI,EAAEF,QAAQ,CAAC;;IAExB;AACJ;AACA;AACA;AACA;IACI,SAASE,QAAQA,CAACC,OAAO,EAAEC,MAAM,EAAE;MACjC,MAAMb,IAAI,GAAGC,KAAK,CAACF,GAAG,CAAC;MAEvBzC,SAAS,CAACQ,GAAG,CAACR,SAAS,CAACM,KAAK,CAACoC,IAAI,CAAC,EAAEA,IAAI,EAAE,CAACe,KAAK,EAAEC,IAAI,EAAEhB,IAAI,KAAK;QAChE,IAAIe,KAAK,IAAI,CAACC,IAAI,IAAI,CAAChB,IAAI,EAAE;UAC3Bc,IAAI,CAACC,KAAK,CAAC;QACb,CAAC,MAAM;UACL;UACA,MAAMrB,MAAM,GAAGpC,SAAS,CAACO,SAAS,CAACmD,IAAI,EAAEhB,IAAI,CAAC;UAE9C,IAAIN,MAAM,KAAKjC,SAAS,IAAIiC,MAAM,KAAK,IAAI,EAAE;YAC3C;UAAA,CACD,MAAM,IAAIyB,oBAAoB,CAACzB,MAAM,CAAC,EAAE;YACvCM,IAAI,CAAC1B,KAAK,GAAGoB,MAAM;UACrB,CAAC,MAAM;YACLM,IAAI,CAACN,MAAM,GAAGA,MAAM;UACtB;UAEAoB,IAAI,CAACC,KAAK,EAAEf,IAAI,CAAC;QACnB;MACF,CAAC,CAAC;;MAEF;AACN;AACA;AACA;AACA;MACM,SAASc,IAAIA,CAACC,KAAK,EAAEf,IAAI,EAAE;QACzB,IAAIe,KAAK,IAAI,CAACf,IAAI,EAAE;UAClBa,MAAM,CAACE,KAAK,CAAC;QACf,CAAC,MAAM,IAAIH,OAAO,EAAE;UAClBA,OAAO,CAACZ,IAAI,CAAC;QACf,CAAC,MAAM;UACL;UACAS,QAAQ,CAAC,IAAI,EAAET,IAAI,CAAC;QACtB;MACF;IACF;EACF;;EAEA;EACA,SAAS/B,WAAWA,CAAC8B,GAAG,EAAE;IACxB;IACA,IAAIkB,QAAQ;IAEZ3D,SAAS,CAACR,MAAM,CAAC,CAAC;IAClBoD,YAAY,CAAC,aAAa,EAAE5C,SAAS,CAACE,MAAM,CAAC;IAC7C8C,cAAc,CAAC,aAAa,EAAEhD,SAAS,CAACI,QAAQ,CAAC;IAEjD,MAAMsC,IAAI,GAAGC,KAAK,CAACF,GAAG,CAAC;IAEvBzC,SAAS,CAACU,OAAO,CAACgC,IAAI,EAAEc,IAAI,CAAC;IAE7BI,UAAU,CAAC,aAAa,EAAE,SAAS,EAAED,QAAQ,CAAC;IAE9C,OAAOjB,IAAI;;IAEX;AACJ;AACA;AACA;IACI,SAASc,IAAIA,CAACC,KAAK,EAAE;MACnBE,QAAQ,GAAG,IAAI;MACf3E,IAAI,CAACyE,KAAK,CAAC;IACb;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASZ,OAAOA,CAAC7B,KAAK,EAAE8C,IAAI,EAAE;EAC5B,OACE,OAAO9C,KAAK,KAAK,UAAU;EAC3B;EACA;EACAA,KAAK,CAAC+C,SAAS;EACf;EACA;EACA;EACA;EACCC,IAAI,CAAChD,KAAK,CAAC+C,SAAS,CAAC,IAAID,IAAI,IAAI9C,KAAK,CAAC+C,SAAS,CAAC;AAEtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAIA,CAAChD,KAAK,EAAE;EACnB;EACA,IAAID,GAAG;EAEP,KAAKA,GAAG,IAAIC,KAAK,EAAE;IACjB,IAAIvB,GAAG,CAAC0B,IAAI,CAACH,KAAK,EAAED,GAAG,CAAC,EAAE;MACxB,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6B,YAAYA,CAACkB,IAAI,EAAE9C,KAAK,EAAE;EACjC,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;IAC/B,MAAM,IAAIe,SAAS,CAAC,UAAU,GAAG+B,IAAI,GAAG,oBAAoB,CAAC;EAC/D;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASd,cAAcA,CAACc,IAAI,EAAE9C,KAAK,EAAE;EACnC,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;IAC/B,MAAM,IAAIe,SAAS,CAAC,UAAU,GAAG+B,IAAI,GAAG,sBAAsB,CAAC;EACjE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS5C,cAAcA,CAAC4C,IAAI,EAAEhE,MAAM,EAAE;EACpC,IAAIA,MAAM,EAAE;IACV,MAAM,IAAImE,KAAK,CACb,eAAe,GACbH,IAAI,GACJ,kHACJ,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASb,UAAUA,CAACF,IAAI,EAAE;EACxB;EACA;EACA,IAAI,CAAC5D,UAAU,CAAC4D,IAAI,CAAC,IAAI,OAAOA,IAAI,CAACmB,IAAI,KAAK,QAAQ,EAAE;IACtD,MAAM,IAAInC,SAAS,CAAC,sBAAsB,GAAGgB,IAAI,GAAG,GAAG,CAAC;IACxD;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,UAAUA,CAACE,IAAI,EAAEK,SAAS,EAAER,QAAQ,EAAE;EAC7C,IAAI,CAACA,QAAQ,EAAE;IACb,MAAM,IAAIM,KAAK,CACb,GAAG,GAAGH,IAAI,GAAG,yBAAyB,GAAGK,SAAS,GAAG,WACvD,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASxB,KAAKA,CAAC3B,KAAK,EAAE;EACpB,OAAOoD,eAAe,CAACpD,KAAK,CAAC,GAAGA,KAAK,GAAG,IAAI3B,KAAK,CAAC2B,KAAK,CAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA,SAASoD,eAAeA,CAACpD,KAAK,EAAE;EAC9B,OAAOqD,OAAO,CACZrD,KAAK,IACH,OAAOA,KAAK,KAAK,QAAQ,IACzB,SAAS,IAAIA,KAAK,IAClB,UAAU,IAAIA,KAClB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAAS6C,oBAAoBA,CAAC7C,KAAK,EAAE;EACnC,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAI/B,QAAQ,CAAC+B,KAAK,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}