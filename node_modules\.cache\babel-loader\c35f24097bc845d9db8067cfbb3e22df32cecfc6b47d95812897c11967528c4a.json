{"ast": null, "code": "import { invariant } from '../../../utils/errors.mjs';\nimport { isNumericalString } from '../../../utils/is-numerical-string.mjs';\nimport { isCSSVariableToken } from './is-css-variable.mjs';\n\n/**\n * Parse <PERSON><PERSON>r's special CSS variable format into a CSS token and a fallback.\n *\n * ```\n * `var(--foo, #fff)` => [`--foo`, '#fff']\n * ```\n *\n * @param current\n */\nconst splitCSSVariableRegex = /var\\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\\)/;\nfunction parseCSSVariable(current) {\n  const match = splitCSSVariableRegex.exec(current);\n  if (!match) return [,];\n  const [, token, fallback] = match;\n  return [token, fallback];\n}\nconst maxDepth = 4;\nfunction getVariableValue(current, element, depth = 1) {\n  invariant(depth <= maxDepth, `Max CSS variable fallback depth detected in property \"${current}\". This may indicate a circular fallback dependency.`);\n  const [token, fallback] = parseCSSVariable(current);\n  // No CSS variable detected\n  if (!token) return;\n  // Attempt to read this CSS variable off the element\n  const resolved = window.getComputedStyle(element).getPropertyValue(token);\n  if (resolved) {\n    const trimmed = resolved.trim();\n    return isNumericalString(trimmed) ? parseFloat(trimmed) : trimmed;\n  } else if (isCSSVariableToken(fallback)) {\n    // The fallback might itself be a CSS variable, in which case we attempt to resolve it too.\n    return getVariableValue(fallback, element, depth + 1);\n  } else {\n    return fallback;\n  }\n}\n/**\n * Resolve CSS variables from\n *\n * @internal\n */\nfunction resolveCSSVariables(visualElement, {\n  ...target\n}, transitionEnd) {\n  const element = visualElement.current;\n  if (!(element instanceof Element)) return {\n    target,\n    transitionEnd\n  };\n  // If `transitionEnd` isn't `undefined`, clone it. We could clone `target` and `transitionEnd`\n  // only if they change but I think this reads clearer and this isn't a performance-critical path.\n  if (transitionEnd) {\n    transitionEnd = {\n      ...transitionEnd\n    };\n  }\n  // Go through existing `MotionValue`s and ensure any existing CSS variables are resolved\n  visualElement.values.forEach(value => {\n    const current = value.get();\n    if (!isCSSVariableToken(current)) return;\n    const resolved = getVariableValue(current, element);\n    if (resolved) value.set(resolved);\n  });\n  // Cycle through every target property and resolve CSS variables. Currently\n  // we only read single-var properties like `var(--foo)`, not `calc(var(--foo) + 20px)`\n  for (const key in target) {\n    const current = target[key];\n    if (!isCSSVariableToken(current)) continue;\n    const resolved = getVariableValue(current, element);\n    if (!resolved) continue;\n    // Clone target if it hasn't already been\n    target[key] = resolved;\n    if (!transitionEnd) transitionEnd = {};\n    // If the user hasn't already set this key on `transitionEnd`, set it to the unresolved\n    // CSS variable. This will ensure that after the animation the component will reflect\n    // changes in the value of the CSS variable.\n    if (transitionEnd[key] === undefined) {\n      transitionEnd[key] = current;\n    }\n  }\n  return {\n    target,\n    transitionEnd\n  };\n}\nexport { parseCSSVariable, resolveCSSVariables };", "map": {"version": 3, "names": ["invariant", "isNumericalString", "isCSSVariableToken", "splitCSSVariableRegex", "parseCSSVariable", "current", "match", "exec", "token", "fallback", "max<PERSON><PERSON><PERSON>", "getVariableValue", "element", "depth", "resolved", "window", "getComputedStyle", "getPropertyValue", "trimmed", "trim", "parseFloat", "resolveCSSVariables", "visualElement", "target", "transitionEnd", "Element", "values", "for<PERSON>ach", "value", "get", "set", "key", "undefined"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/framer-motion/dist/es/render/dom/utils/css-variables-conversion.mjs"], "sourcesContent": ["import { invariant } from '../../../utils/errors.mjs';\nimport { isNumericalString } from '../../../utils/is-numerical-string.mjs';\nimport { isCSSVariableToken } from './is-css-variable.mjs';\n\n/**\n * Parse <PERSON><PERSON>r's special CSS variable format into a CSS token and a fallback.\n *\n * ```\n * `var(--foo, #fff)` => [`--foo`, '#fff']\n * ```\n *\n * @param current\n */\nconst splitCSSVariableRegex = /var\\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\\)/;\nfunction parseCSSVariable(current) {\n    const match = splitCSSVariableRegex.exec(current);\n    if (!match)\n        return [,];\n    const [, token, fallback] = match;\n    return [token, fallback];\n}\nconst maxDepth = 4;\nfunction getVariableValue(current, element, depth = 1) {\n    invariant(depth <= maxDepth, `Max CSS variable fallback depth detected in property \"${current}\". This may indicate a circular fallback dependency.`);\n    const [token, fallback] = parseCSSVariable(current);\n    // No CSS variable detected\n    if (!token)\n        return;\n    // Attempt to read this CSS variable off the element\n    const resolved = window.getComputedStyle(element).getPropertyValue(token);\n    if (resolved) {\n        const trimmed = resolved.trim();\n        return isNumericalString(trimmed) ? parseFloat(trimmed) : trimmed;\n    }\n    else if (isCSSVariableToken(fallback)) {\n        // The fallback might itself be a CSS variable, in which case we attempt to resolve it too.\n        return getVariableValue(fallback, element, depth + 1);\n    }\n    else {\n        return fallback;\n    }\n}\n/**\n * Resolve CSS variables from\n *\n * @internal\n */\nfunction resolveCSSVariables(visualElement, { ...target }, transitionEnd) {\n    const element = visualElement.current;\n    if (!(element instanceof Element))\n        return { target, transitionEnd };\n    // If `transitionEnd` isn't `undefined`, clone it. We could clone `target` and `transitionEnd`\n    // only if they change but I think this reads clearer and this isn't a performance-critical path.\n    if (transitionEnd) {\n        transitionEnd = { ...transitionEnd };\n    }\n    // Go through existing `MotionValue`s and ensure any existing CSS variables are resolved\n    visualElement.values.forEach((value) => {\n        const current = value.get();\n        if (!isCSSVariableToken(current))\n            return;\n        const resolved = getVariableValue(current, element);\n        if (resolved)\n            value.set(resolved);\n    });\n    // Cycle through every target property and resolve CSS variables. Currently\n    // we only read single-var properties like `var(--foo)`, not `calc(var(--foo) + 20px)`\n    for (const key in target) {\n        const current = target[key];\n        if (!isCSSVariableToken(current))\n            continue;\n        const resolved = getVariableValue(current, element);\n        if (!resolved)\n            continue;\n        // Clone target if it hasn't already been\n        target[key] = resolved;\n        if (!transitionEnd)\n            transitionEnd = {};\n        // If the user hasn't already set this key on `transitionEnd`, set it to the unresolved\n        // CSS variable. This will ensure that after the animation the component will reflect\n        // changes in the value of the CSS variable.\n        if (transitionEnd[key] === undefined) {\n            transitionEnd[key] = current;\n        }\n    }\n    return { target, transitionEnd };\n}\n\nexport { parseCSSVariable, resolveCSSVariables };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,2BAA2B;AACrD,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,kBAAkB,QAAQ,uBAAuB;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG,sDAAsD;AACpF,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EAC/B,MAAMC,KAAK,GAAGH,qBAAqB,CAACI,IAAI,CAACF,OAAO,CAAC;EACjD,IAAI,CAACC,KAAK,EACN,OAAO,GAAG;EACd,MAAM,GAAGE,KAAK,EAAEC,QAAQ,CAAC,GAAGH,KAAK;EACjC,OAAO,CAACE,KAAK,EAAEC,QAAQ,CAAC;AAC5B;AACA,MAAMC,QAAQ,GAAG,CAAC;AAClB,SAASC,gBAAgBA,CAACN,OAAO,EAAEO,OAAO,EAAEC,KAAK,GAAG,CAAC,EAAE;EACnDb,SAAS,CAACa,KAAK,IAAIH,QAAQ,EAAE,yDAAyDL,OAAO,sDAAsD,CAAC;EACpJ,MAAM,CAACG,KAAK,EAAEC,QAAQ,CAAC,GAAGL,gBAAgB,CAACC,OAAO,CAAC;EACnD;EACA,IAAI,CAACG,KAAK,EACN;EACJ;EACA,MAAMM,QAAQ,GAAGC,MAAM,CAACC,gBAAgB,CAACJ,OAAO,CAAC,CAACK,gBAAgB,CAACT,KAAK,CAAC;EACzE,IAAIM,QAAQ,EAAE;IACV,MAAMI,OAAO,GAAGJ,QAAQ,CAACK,IAAI,CAAC,CAAC;IAC/B,OAAOlB,iBAAiB,CAACiB,OAAO,CAAC,GAAGE,UAAU,CAACF,OAAO,CAAC,GAAGA,OAAO;EACrE,CAAC,MACI,IAAIhB,kBAAkB,CAACO,QAAQ,CAAC,EAAE;IACnC;IACA,OAAOE,gBAAgB,CAACF,QAAQ,EAAEG,OAAO,EAAEC,KAAK,GAAG,CAAC,CAAC;EACzD,CAAC,MACI;IACD,OAAOJ,QAAQ;EACnB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,mBAAmBA,CAACC,aAAa,EAAE;EAAE,GAAGC;AAAO,CAAC,EAAEC,aAAa,EAAE;EACtE,MAAMZ,OAAO,GAAGU,aAAa,CAACjB,OAAO;EACrC,IAAI,EAAEO,OAAO,YAAYa,OAAO,CAAC,EAC7B,OAAO;IAAEF,MAAM;IAAEC;EAAc,CAAC;EACpC;EACA;EACA,IAAIA,aAAa,EAAE;IACfA,aAAa,GAAG;MAAE,GAAGA;IAAc,CAAC;EACxC;EACA;EACAF,aAAa,CAACI,MAAM,CAACC,OAAO,CAAEC,KAAK,IAAK;IACpC,MAAMvB,OAAO,GAAGuB,KAAK,CAACC,GAAG,CAAC,CAAC;IAC3B,IAAI,CAAC3B,kBAAkB,CAACG,OAAO,CAAC,EAC5B;IACJ,MAAMS,QAAQ,GAAGH,gBAAgB,CAACN,OAAO,EAAEO,OAAO,CAAC;IACnD,IAAIE,QAAQ,EACRc,KAAK,CAACE,GAAG,CAAChB,QAAQ,CAAC;EAC3B,CAAC,CAAC;EACF;EACA;EACA,KAAK,MAAMiB,GAAG,IAAIR,MAAM,EAAE;IACtB,MAAMlB,OAAO,GAAGkB,MAAM,CAACQ,GAAG,CAAC;IAC3B,IAAI,CAAC7B,kBAAkB,CAACG,OAAO,CAAC,EAC5B;IACJ,MAAMS,QAAQ,GAAGH,gBAAgB,CAACN,OAAO,EAAEO,OAAO,CAAC;IACnD,IAAI,CAACE,QAAQ,EACT;IACJ;IACAS,MAAM,CAACQ,GAAG,CAAC,GAAGjB,QAAQ;IACtB,IAAI,CAACU,aAAa,EACdA,aAAa,GAAG,CAAC,CAAC;IACtB;IACA;IACA;IACA,IAAIA,aAAa,CAACO,GAAG,CAAC,KAAKC,SAAS,EAAE;MAClCR,aAAa,CAACO,GAAG,CAAC,GAAG1B,OAAO;IAChC;EACJ;EACA,OAAO;IAAEkB,MAAM;IAAEC;EAAc,CAAC;AACpC;AAEA,SAASpB,gBAAgB,EAAEiB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}