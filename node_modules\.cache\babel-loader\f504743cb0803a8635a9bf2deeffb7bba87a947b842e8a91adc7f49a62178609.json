{"ast": null, "code": "/**\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('../state.js').State} State\n */\n\nimport { normalizeUri } from 'micromark-util-sanitize-uri';\nimport { revert } from '../revert.js';\n\n/**\n * Turn an mdast `imageReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ImageReference} node\n *   mdast node.\n * @returns {ElementContent | Array<ElementContent>}\n *   hast node.\n */\nexport function imageReference(state, node) {\n  const def = state.definition(node.identifier);\n  if (!def) {\n    return revert(state, node);\n  }\n\n  /** @type {Properties} */\n  const properties = {\n    src: normalizeUri(def.url || ''),\n    alt: node.alt\n  };\n  if (def.title !== null && def.title !== undefined) {\n    properties.title = def.title;\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'img',\n    properties,\n    children: []\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": {"version": 3, "names": ["normalizeUri", "revert", "imageReference", "state", "node", "def", "definition", "identifier", "properties", "src", "url", "alt", "title", "undefined", "result", "type", "tagName", "children", "patch", "applyData"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/mdast-util-to-hast/lib/handlers/image-reference.js"], "sourcesContent": ["/**\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\nimport {revert} from '../revert.js'\n\n/**\n * Turn an mdast `imageReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ImageReference} node\n *   mdast node.\n * @returns {ElementContent | Array<ElementContent>}\n *   hast node.\n */\nexport function imageReference(state, node) {\n  const def = state.definition(node.identifier)\n\n  if (!def) {\n    return revert(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {src: normalizeUri(def.url || ''), alt: node.alt}\n\n  if (def.title !== null && def.title !== undefined) {\n    properties.title = def.title\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'img', properties, children: []}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,6BAA6B;AACxD,SAAQC,MAAM,QAAO,cAAc;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC1C,MAAMC,GAAG,GAAGF,KAAK,CAACG,UAAU,CAACF,IAAI,CAACG,UAAU,CAAC;EAE7C,IAAI,CAACF,GAAG,EAAE;IACR,OAAOJ,MAAM,CAACE,KAAK,EAAEC,IAAI,CAAC;EAC5B;;EAEA;EACA,MAAMI,UAAU,GAAG;IAACC,GAAG,EAAET,YAAY,CAACK,GAAG,CAACK,GAAG,IAAI,EAAE,CAAC;IAAEC,GAAG,EAAEP,IAAI,CAACO;EAAG,CAAC;EAEpE,IAAIN,GAAG,CAACO,KAAK,KAAK,IAAI,IAAIP,GAAG,CAACO,KAAK,KAAKC,SAAS,EAAE;IACjDL,UAAU,CAACI,KAAK,GAAGP,GAAG,CAACO,KAAK;EAC9B;;EAEA;EACA,MAAME,MAAM,GAAG;IAACC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,KAAK;IAAER,UAAU;IAAES,QAAQ,EAAE;EAAE,CAAC;EAC1Ed,KAAK,CAACe,KAAK,CAACd,IAAI,EAAEU,MAAM,CAAC;EACzB,OAAOX,KAAK,CAACgB,SAAS,CAACf,IAAI,EAAEU,MAAM,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}