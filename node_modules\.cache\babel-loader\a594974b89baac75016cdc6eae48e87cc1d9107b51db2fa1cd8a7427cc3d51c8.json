{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport { markdownLineEnding } from 'micromark-util-character';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\n\n/** @type {Construct} */\nexport const hardBreakEscape = {\n  name: 'hardBreakEscape',\n  tokenize: tokenizeHardBreakEscape\n};\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeHardBreakEscape(effects, ok, nok) {\n  return start;\n\n  /**\n   * Start of a hard break (escape).\n   *\n   * ```markdown\n   * > | a\\\n   *      ^\n   *   | b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.backslash, 'expected `\\\\`');\n    effects.enter(types.hardBreakEscape);\n    effects.consume(code);\n    return after;\n  }\n\n  /**\n   * After `\\`, at eol.\n   *\n   * ```markdown\n   * > | a\\\n   *       ^\n   *   | b\n   * ```\n   *\n   *  @type {State}\n   */\n  function after(code) {\n    if (markdownLineEnding(code)) {\n      effects.exit(types.hardBreakEscape);\n      return ok(code);\n    }\n    return nok(code);\n  }\n}", "map": {"version": 3, "names": ["markdownLineEnding", "codes", "types", "ok", "assert", "hardBreakEscape", "name", "tokenize", "tokenizeHardBreakEscape", "effects", "nok", "start", "code", "backslash", "enter", "consume", "after", "exit"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/** @type {Construct} */\nexport const hardBreakEscape = {\n  name: 'hardBreakEscape',\n  tokenize: tokenizeHardBreakEscape\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeHardBreakEscape(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of a hard break (escape).\n   *\n   * ```markdown\n   * > | a\\\n   *      ^\n   *   | b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.backslash, 'expected `\\\\`')\n    effects.enter(types.hardBreakEscape)\n    effects.consume(code)\n    return after\n  }\n\n  /**\n   * After `\\`, at eol.\n   *\n   * ```markdown\n   * > | a\\\n   *       ^\n   *   | b\n   * ```\n   *\n   *  @type {State}\n   */\n  function after(code) {\n    if (markdownLineEnding(code)) {\n      effects.exit(types.hardBreakEscape)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;;AAEvC;AACA,OAAO,MAAMC,eAAe,GAAG;EAC7BC,IAAI,EAAE,iBAAiB;EACvBC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASA,uBAAuBA,CAACC,OAAO,EAAEN,EAAE,EAAEO,GAAG,EAAE;EACjD,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnBR,MAAM,CAACQ,IAAI,KAAKX,KAAK,CAACY,SAAS,EAAE,eAAe,CAAC;IACjDJ,OAAO,CAACK,KAAK,CAACZ,KAAK,CAACG,eAAe,CAAC;IACpCI,OAAO,CAACM,OAAO,CAACH,IAAI,CAAC;IACrB,OAAOI,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACJ,IAAI,EAAE;IACnB,IAAIZ,kBAAkB,CAACY,IAAI,CAAC,EAAE;MAC5BH,OAAO,CAACQ,IAAI,CAACf,KAAK,CAACG,eAAe,CAAC;MACnC,OAAOF,EAAE,CAACS,IAAI,CAAC;IACjB;IAEA,OAAOF,GAAG,CAACE,IAAI,CAAC;EAClB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}