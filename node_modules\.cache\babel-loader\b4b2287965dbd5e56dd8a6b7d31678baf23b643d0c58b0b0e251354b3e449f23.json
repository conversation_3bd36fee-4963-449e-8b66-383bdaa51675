{"ast": null, "code": "import { create } from './util/create.js';\nimport { caseInsensitiveTransform } from './util/case-insensitive-transform.js';\nexport const xmlns = create({\n  space: 'xmlns',\n  attributes: {\n    xmlnsxlink: 'xmlns:xlink'\n  },\n  transform: caseInsensitiveTransform,\n  properties: {\n    xmlns: null,\n    xmlnsXLink: null\n  }\n});", "map": {"version": 3, "names": ["create", "caseInsensitiveTransform", "xmlns", "space", "attributes", "xmlnsxlink", "transform", "properties", "xmlnsXLink"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/property-information/lib/xmlns.js"], "sourcesContent": ["import {create} from './util/create.js'\nimport {caseInsensitiveTransform} from './util/case-insensitive-transform.js'\n\nexport const xmlns = create({\n  space: 'xmlns',\n  attributes: {xmlnsxlink: 'xmlns:xlink'},\n  transform: caseInsensitiveTransform,\n  properties: {xmlns: null, xmlnsXLink: null}\n})\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,kBAAkB;AACvC,SAAQC,wBAAwB,QAAO,sCAAsC;AAE7E,OAAO,MAAMC,KAAK,GAAGF,MAAM,CAAC;EAC1BG,KAAK,EAAE,OAAO;EACdC,UAAU,EAAE;IAACC,UAAU,EAAE;EAAa,CAAC;EACvCC,SAAS,EAAEL,wBAAwB;EACnCM,UAAU,EAAE;IAACL,KAAK,EAAE,IAAI;IAAEM,UAAU,EAAE;EAAI;AAC5C,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}