{"ast": null, "code": "/**\n * @template T\n * @typedef {import('react').ComponentType<T>} ComponentType<T>\n */\n\n/**\n * @template {import('react').ElementType} T\n * @typedef {import('react').ComponentPropsWithoutRef<T>} ComponentPropsWithoutRef<T>\n */\n\n/**\n * @typedef {import('react').ReactNode} ReactNode\n * @typedef {import('unist').Position} Position\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Text} Text\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').DocType} Doctype\n * @typedef {import('property-information').Info} Info\n * @typedef {import('property-information').Schema} Schema\n * @typedef {import('./complex-types.js').ReactMarkdownProps} ReactMarkdownProps\n *\n * @typedef Raw\n * @property {'raw'} type\n * @property {string} value\n *\n * @typedef Context\n * @property {Options} options\n * @property {Schema} schema\n * @property {number} listDepth\n *\n * @callback TransformLink\n * @param {string} href\n * @param {Array<ElementContent>} children\n * @param {string?} title\n * @returns {string}\n *\n * @callback TransformImage\n * @param {string} src\n * @param {string} alt\n * @param {string?} title\n * @returns {string}\n *\n * @typedef {import('react').HTMLAttributeAnchorTarget} TransformLinkTargetType\n *\n * @callback TransformLinkTarget\n * @param {string} href\n * @param {Array<ElementContent>} children\n * @param {string?} title\n * @returns {TransformLinkTargetType|undefined}\n *\n * @typedef {keyof JSX.IntrinsicElements} ReactMarkdownNames\n *\n * To do: is `data-sourcepos` typeable?\n *\n * @typedef {ComponentPropsWithoutRef<'code'> & ReactMarkdownProps & {inline?: boolean}} CodeProps\n * @typedef {ComponentPropsWithoutRef<'h1'> & ReactMarkdownProps & {level: number}} HeadingProps\n * @typedef {ComponentPropsWithoutRef<'li'> & ReactMarkdownProps & {checked: boolean|null, index: number, ordered: boolean}} LiProps\n * @typedef {ComponentPropsWithoutRef<'ol'> & ReactMarkdownProps & {depth: number, ordered: true}} OrderedListProps\n * @typedef {ComponentPropsWithoutRef<'td'> & ReactMarkdownProps & {style?: Record<string, unknown>, isHeader: false}} TableDataCellProps\n * @typedef {ComponentPropsWithoutRef<'th'> & ReactMarkdownProps & {style?: Record<string, unknown>, isHeader: true}} TableHeaderCellProps\n * @typedef {ComponentPropsWithoutRef<'tr'> & ReactMarkdownProps & {isHeader: boolean}} TableRowProps\n * @typedef {ComponentPropsWithoutRef<'ul'> & ReactMarkdownProps & {depth: number, ordered: false}} UnorderedListProps\n *\n * @typedef {ComponentType<CodeProps>} CodeComponent\n * @typedef {ComponentType<HeadingProps>} HeadingComponent\n * @typedef {ComponentType<LiProps>} LiComponent\n * @typedef {ComponentType<OrderedListProps>} OrderedListComponent\n * @typedef {ComponentType<TableDataCellProps>} TableDataCellComponent\n * @typedef {ComponentType<TableHeaderCellProps>} TableHeaderCellComponent\n * @typedef {ComponentType<TableRowProps>} TableRowComponent\n * @typedef {ComponentType<UnorderedListProps>} UnorderedListComponent\n *\n * @typedef SpecialComponents\n * @property {CodeComponent|ReactMarkdownNames} code\n * @property {HeadingComponent|ReactMarkdownNames} h1\n * @property {HeadingComponent|ReactMarkdownNames} h2\n * @property {HeadingComponent|ReactMarkdownNames} h3\n * @property {HeadingComponent|ReactMarkdownNames} h4\n * @property {HeadingComponent|ReactMarkdownNames} h5\n * @property {HeadingComponent|ReactMarkdownNames} h6\n * @property {LiComponent|ReactMarkdownNames} li\n * @property {OrderedListComponent|ReactMarkdownNames} ol\n * @property {TableDataCellComponent|ReactMarkdownNames} td\n * @property {TableHeaderCellComponent|ReactMarkdownNames} th\n * @property {TableRowComponent|ReactMarkdownNames} tr\n * @property {UnorderedListComponent|ReactMarkdownNames} ul\n *\n * @typedef {Partial<Omit<import('./complex-types.js').NormalComponents, keyof SpecialComponents> & SpecialComponents>} Components\n *\n * @typedef Options\n * @property {boolean} [sourcePos=false]\n * @property {boolean} [rawSourcePos=false]\n * @property {boolean} [skipHtml=false]\n * @property {boolean} [includeElementIndex=false]\n * @property {null|false|TransformLink} [transformLinkUri]\n * @property {TransformImage} [transformImageUri]\n * @property {TransformLinkTargetType|TransformLinkTarget} [linkTarget]\n * @property {Components} [components]\n */\n\nimport React from 'react';\nimport ReactIs from 'react-is';\nimport { whitespace } from 'hast-util-whitespace';\nimport { svg, find, hastToReact } from 'property-information';\nimport { stringify as spaces } from 'space-separated-tokens';\nimport { stringify as commas } from 'comma-separated-tokens';\nimport style from 'style-to-object';\nimport { uriTransformer } from './uri-transformer.js';\nconst own = {}.hasOwnProperty;\n\n// The table-related elements that must not contain whitespace text according\n// to React.\nconst tableElements = new Set(['table', 'thead', 'tbody', 'tfoot', 'tr']);\n\n/**\n * @param {Context} context\n * @param {Element|Root} node\n */\nexport function childrenToReact(context, node) {\n  /** @type {Array<ReactNode>} */\n  const children = [];\n  let childIndex = -1;\n  /** @type {Comment|Doctype|Element|Raw|Text} */\n  let child;\n  while (++childIndex < node.children.length) {\n    child = node.children[childIndex];\n    if (child.type === 'element') {\n      children.push(toReact(context, child, childIndex, node));\n    } else if (child.type === 'text') {\n      // Currently, a warning is triggered by react for *any* white space in\n      // tables.\n      // So we drop it.\n      // See: <https://github.com/facebook/react/pull/7081>.\n      // See: <https://github.com/facebook/react/pull/7515>.\n      // See: <https://github.com/remarkjs/remark-react/issues/64>.\n      // See: <https://github.com/remarkjs/react-markdown/issues/576>.\n      if (node.type !== 'element' || !tableElements.has(node.tagName) || !whitespace(child)) {\n        children.push(child.value);\n      }\n    } else if (child.type === 'raw' && !context.options.skipHtml) {\n      // Default behavior is to show (encoded) HTML.\n      children.push(child.value);\n    }\n  }\n  return children;\n}\n\n/**\n * @param {Context} context\n * @param {Element} node\n * @param {number} index\n * @param {Element|Root} parent\n */\nfunction toReact(context, node, index, parent) {\n  const options = context.options;\n  const transform = options.transformLinkUri === undefined ? uriTransformer : options.transformLinkUri;\n  const parentSchema = context.schema;\n  /** @type {ReactMarkdownNames} */\n  // @ts-expect-error assume a known HTML/SVG element.\n  const name = node.tagName;\n  /** @type {Record<string, unknown>} */\n  const properties = {};\n  let schema = parentSchema;\n  /** @type {string} */\n  let property;\n  if (parentSchema.space === 'html' && name === 'svg') {\n    schema = svg;\n    context.schema = schema;\n  }\n  if (node.properties) {\n    for (property in node.properties) {\n      if (own.call(node.properties, property)) {\n        addProperty(properties, property, node.properties[property], context);\n      }\n    }\n  }\n  if (name === 'ol' || name === 'ul') {\n    context.listDepth++;\n  }\n  const children = childrenToReact(context, node);\n  if (name === 'ol' || name === 'ul') {\n    context.listDepth--;\n  }\n\n  // Restore parent schema.\n  context.schema = parentSchema;\n\n  // Nodes created by plugins do not have positional info, in which case we use\n  // an object that matches the position interface.\n  const position = node.position || {\n    start: {\n      line: null,\n      column: null,\n      offset: null\n    },\n    end: {\n      line: null,\n      column: null,\n      offset: null\n    }\n  };\n  const component = options.components && own.call(options.components, name) ? options.components[name] : name;\n  const basic = typeof component === 'string' || component === React.Fragment;\n  if (!ReactIs.isValidElementType(component)) {\n    throw new TypeError(`Component for name \\`${name}\\` not defined or is not renderable`);\n  }\n  properties.key = index;\n  if (name === 'a' && options.linkTarget) {\n    properties.target = typeof options.linkTarget === 'function' ? options.linkTarget(String(properties.href || ''), node.children, typeof properties.title === 'string' ? properties.title : null) : options.linkTarget;\n  }\n  if (name === 'a' && transform) {\n    properties.href = transform(String(properties.href || ''), node.children, typeof properties.title === 'string' ? properties.title : null);\n  }\n  if (!basic && name === 'code' && parent.type === 'element' && parent.tagName !== 'pre') {\n    properties.inline = true;\n  }\n  if (!basic && (name === 'h1' || name === 'h2' || name === 'h3' || name === 'h4' || name === 'h5' || name === 'h6')) {\n    properties.level = Number.parseInt(name.charAt(1), 10);\n  }\n  if (name === 'img' && options.transformImageUri) {\n    properties.src = options.transformImageUri(String(properties.src || ''), String(properties.alt || ''), typeof properties.title === 'string' ? properties.title : null);\n  }\n  if (!basic && name === 'li' && parent.type === 'element') {\n    const input = getInputElement(node);\n    properties.checked = input && input.properties ? Boolean(input.properties.checked) : null;\n    properties.index = getElementsBeforeCount(parent, node);\n    properties.ordered = parent.tagName === 'ol';\n  }\n  if (!basic && (name === 'ol' || name === 'ul')) {\n    properties.ordered = name === 'ol';\n    properties.depth = context.listDepth;\n  }\n  if (name === 'td' || name === 'th') {\n    if (properties.align) {\n      if (!properties.style) properties.style = {};\n      // @ts-expect-error assume `style` is an object\n      properties.style.textAlign = properties.align;\n      delete properties.align;\n    }\n    if (!basic) {\n      properties.isHeader = name === 'th';\n    }\n  }\n  if (!basic && name === 'tr' && parent.type === 'element') {\n    properties.isHeader = Boolean(parent.tagName === 'thead');\n  }\n\n  // If `sourcePos` is given, pass source information (line/column info from markdown source).\n  if (options.sourcePos) {\n    properties['data-sourcepos'] = flattenPosition(position);\n  }\n  if (!basic && options.rawSourcePos) {\n    properties.sourcePosition = node.position;\n  }\n\n  // If `includeElementIndex` is given, pass node index info to components.\n  if (!basic && options.includeElementIndex) {\n    properties.index = getElementsBeforeCount(parent, node);\n    properties.siblingCount = getElementsBeforeCount(parent);\n  }\n  if (!basic) {\n    properties.node = node;\n  }\n\n  // Ensure no React warnings are emitted for void elements w/ children.\n  return children.length > 0 ? React.createElement(component, properties, children) : React.createElement(component, properties);\n}\n\n/**\n * @param {Element|Root} node\n * @returns {Element?}\n */\nfunction getInputElement(node) {\n  let index = -1;\n  while (++index < node.children.length) {\n    const child = node.children[index];\n    if (child.type === 'element' && child.tagName === 'input') {\n      return child;\n    }\n  }\n  return null;\n}\n\n/**\n * @param {Element|Root} parent\n * @param {Element} [node]\n * @returns {number}\n */\nfunction getElementsBeforeCount(parent, node) {\n  let index = -1;\n  let count = 0;\n  while (++index < parent.children.length) {\n    if (parent.children[index] === node) break;\n    if (parent.children[index].type === 'element') count++;\n  }\n  return count;\n}\n\n/**\n * @param {Record<string, unknown>} props\n * @param {string} prop\n * @param {unknown} value\n * @param {Context} ctx\n */\nfunction addProperty(props, prop, value, ctx) {\n  const info = find(ctx.schema, prop);\n  let result = value;\n\n  // Ignore nullish and `NaN` values.\n  // eslint-disable-next-line no-self-compare\n  if (result === null || result === undefined || result !== result) {\n    return;\n  }\n\n  // Accept `array`.\n  // Most props are space-separated.\n  if (Array.isArray(result)) {\n    result = info.commaSeparated ? commas(result) : spaces(result);\n  }\n  if (info.property === 'style' && typeof result === 'string') {\n    result = parseStyle(result);\n  }\n  if (info.space && info.property) {\n    props[own.call(hastToReact, info.property) ? hastToReact[info.property] : info.property] = result;\n  } else if (info.attribute) {\n    props[info.attribute] = result;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {Record<string, string>}\n */\nfunction parseStyle(value) {\n  /** @type {Record<string, string>} */\n  const result = {};\n  try {\n    style(value, iterator);\n  } catch {\n    // Silent.\n  }\n  return result;\n\n  /**\n   * @param {string} name\n   * @param {string} v\n   */\n  function iterator(name, v) {\n    const k = name.slice(0, 4) === '-ms-' ? `ms-${name.slice(4)}` : name;\n    result[k.replace(/-([a-z])/g, styleReplacer)] = v;\n  }\n}\n\n/**\n * @param {unknown} _\n * @param {string} $1\n */\nfunction styleReplacer(_, $1) {\n  return $1.toUpperCase();\n}\n\n/**\n * @param {Position|{start: {line: null, column: null, offset: null}, end: {line: null, column: null, offset: null}}} pos\n * @returns {string}\n */\nfunction flattenPosition(pos) {\n  return [pos.start.line, ':', pos.start.column, '-', pos.end.line, ':', pos.end.column].map(String).join('');\n}", "map": {"version": 3, "names": ["React", "ReactIs", "whitespace", "svg", "find", "hastToReact", "stringify", "spaces", "commas", "style", "uriTransformer", "own", "hasOwnProperty", "tableElements", "Set", "childrenToReact", "context", "node", "children", "childIndex", "child", "length", "type", "push", "toReact", "has", "tagName", "value", "options", "skipHtml", "index", "parent", "transform", "transformLinkUri", "undefined", "parentSchema", "schema", "name", "properties", "property", "space", "call", "addProperty", "listDepth", "position", "start", "line", "column", "offset", "end", "component", "components", "basic", "Fragment", "isValidElementType", "TypeError", "key", "linkTarget", "target", "String", "href", "title", "inline", "level", "Number", "parseInt", "char<PERSON>t", "transformImageUri", "src", "alt", "input", "getInputElement", "checked", "Boolean", "getElementsBeforeCount", "ordered", "depth", "align", "textAlign", "<PERSON><PERSON><PERSON><PERSON>", "sourcePos", "flattenPosition", "rawSourcePos", "sourcePosition", "includeElementIndex", "siblingCount", "createElement", "count", "props", "prop", "ctx", "info", "result", "Array", "isArray", "commaSeparated", "parseStyle", "attribute", "iterator", "v", "k", "slice", "replace", "styleReplacer", "_", "$1", "toUpperCase", "pos", "map", "join"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/react-markdown/lib/ast-to-react.js"], "sourcesContent": ["/**\n * @template T\n * @typedef {import('react').ComponentType<T>} ComponentType<T>\n */\n\n/**\n * @template {import('react').ElementType} T\n * @typedef {import('react').ComponentPropsWithoutRef<T>} ComponentPropsWithoutRef<T>\n */\n\n/**\n * @typedef {import('react').ReactNode} ReactNode\n * @typedef {import('unist').Position} Position\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Text} Text\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').DocType} Doctype\n * @typedef {import('property-information').Info} Info\n * @typedef {import('property-information').Schema} Schema\n * @typedef {import('./complex-types.js').ReactMarkdownProps} ReactMarkdownProps\n *\n * @typedef Raw\n * @property {'raw'} type\n * @property {string} value\n *\n * @typedef Context\n * @property {Options} options\n * @property {Schema} schema\n * @property {number} listDepth\n *\n * @callback TransformLink\n * @param {string} href\n * @param {Array<ElementContent>} children\n * @param {string?} title\n * @returns {string}\n *\n * @callback TransformImage\n * @param {string} src\n * @param {string} alt\n * @param {string?} title\n * @returns {string}\n *\n * @typedef {import('react').HTMLAttributeAnchorTarget} TransformLinkTargetType\n *\n * @callback TransformLinkTarget\n * @param {string} href\n * @param {Array<ElementContent>} children\n * @param {string?} title\n * @returns {TransformLinkTargetType|undefined}\n *\n * @typedef {keyof JSX.IntrinsicElements} ReactMarkdownNames\n *\n * To do: is `data-sourcepos` typeable?\n *\n * @typedef {ComponentPropsWithoutRef<'code'> & ReactMarkdownProps & {inline?: boolean}} CodeProps\n * @typedef {ComponentPropsWithoutRef<'h1'> & ReactMarkdownProps & {level: number}} HeadingProps\n * @typedef {ComponentPropsWithoutRef<'li'> & ReactMarkdownProps & {checked: boolean|null, index: number, ordered: boolean}} LiProps\n * @typedef {ComponentPropsWithoutRef<'ol'> & ReactMarkdownProps & {depth: number, ordered: true}} OrderedListProps\n * @typedef {ComponentPropsWithoutRef<'td'> & ReactMarkdownProps & {style?: Record<string, unknown>, isHeader: false}} TableDataCellProps\n * @typedef {ComponentPropsWithoutRef<'th'> & ReactMarkdownProps & {style?: Record<string, unknown>, isHeader: true}} TableHeaderCellProps\n * @typedef {ComponentPropsWithoutRef<'tr'> & ReactMarkdownProps & {isHeader: boolean}} TableRowProps\n * @typedef {ComponentPropsWithoutRef<'ul'> & ReactMarkdownProps & {depth: number, ordered: false}} UnorderedListProps\n *\n * @typedef {ComponentType<CodeProps>} CodeComponent\n * @typedef {ComponentType<HeadingProps>} HeadingComponent\n * @typedef {ComponentType<LiProps>} LiComponent\n * @typedef {ComponentType<OrderedListProps>} OrderedListComponent\n * @typedef {ComponentType<TableDataCellProps>} TableDataCellComponent\n * @typedef {ComponentType<TableHeaderCellProps>} TableHeaderCellComponent\n * @typedef {ComponentType<TableRowProps>} TableRowComponent\n * @typedef {ComponentType<UnorderedListProps>} UnorderedListComponent\n *\n * @typedef SpecialComponents\n * @property {CodeComponent|ReactMarkdownNames} code\n * @property {HeadingComponent|ReactMarkdownNames} h1\n * @property {HeadingComponent|ReactMarkdownNames} h2\n * @property {HeadingComponent|ReactMarkdownNames} h3\n * @property {HeadingComponent|ReactMarkdownNames} h4\n * @property {HeadingComponent|ReactMarkdownNames} h5\n * @property {HeadingComponent|ReactMarkdownNames} h6\n * @property {LiComponent|ReactMarkdownNames} li\n * @property {OrderedListComponent|ReactMarkdownNames} ol\n * @property {TableDataCellComponent|ReactMarkdownNames} td\n * @property {TableHeaderCellComponent|ReactMarkdownNames} th\n * @property {TableRowComponent|ReactMarkdownNames} tr\n * @property {UnorderedListComponent|ReactMarkdownNames} ul\n *\n * @typedef {Partial<Omit<import('./complex-types.js').NormalComponents, keyof SpecialComponents> & SpecialComponents>} Components\n *\n * @typedef Options\n * @property {boolean} [sourcePos=false]\n * @property {boolean} [rawSourcePos=false]\n * @property {boolean} [skipHtml=false]\n * @property {boolean} [includeElementIndex=false]\n * @property {null|false|TransformLink} [transformLinkUri]\n * @property {TransformImage} [transformImageUri]\n * @property {TransformLinkTargetType|TransformLinkTarget} [linkTarget]\n * @property {Components} [components]\n */\n\nimport React from 'react'\nimport ReactIs from 'react-is'\nimport {whitespace} from 'hast-util-whitespace'\nimport {svg, find, hastToReact} from 'property-information'\nimport {stringify as spaces} from 'space-separated-tokens'\nimport {stringify as commas} from 'comma-separated-tokens'\nimport style from 'style-to-object'\nimport {uriTransformer} from './uri-transformer.js'\n\nconst own = {}.hasOwnProperty\n\n// The table-related elements that must not contain whitespace text according\n// to React.\nconst tableElements = new Set(['table', 'thead', 'tbody', 'tfoot', 'tr'])\n\n/**\n * @param {Context} context\n * @param {Element|Root} node\n */\nexport function childrenToReact(context, node) {\n  /** @type {Array<ReactNode>} */\n  const children = []\n  let childIndex = -1\n  /** @type {Comment|Doctype|Element|Raw|Text} */\n  let child\n\n  while (++childIndex < node.children.length) {\n    child = node.children[childIndex]\n\n    if (child.type === 'element') {\n      children.push(toReact(context, child, childIndex, node))\n    } else if (child.type === 'text') {\n      // Currently, a warning is triggered by react for *any* white space in\n      // tables.\n      // So we drop it.\n      // See: <https://github.com/facebook/react/pull/7081>.\n      // See: <https://github.com/facebook/react/pull/7515>.\n      // See: <https://github.com/remarkjs/remark-react/issues/64>.\n      // See: <https://github.com/remarkjs/react-markdown/issues/576>.\n      if (\n        node.type !== 'element' ||\n        !tableElements.has(node.tagName) ||\n        !whitespace(child)\n      ) {\n        children.push(child.value)\n      }\n    } else if (child.type === 'raw' && !context.options.skipHtml) {\n      // Default behavior is to show (encoded) HTML.\n      children.push(child.value)\n    }\n  }\n\n  return children\n}\n\n/**\n * @param {Context} context\n * @param {Element} node\n * @param {number} index\n * @param {Element|Root} parent\n */\nfunction toReact(context, node, index, parent) {\n  const options = context.options\n  const transform =\n    options.transformLinkUri === undefined\n      ? uriTransformer\n      : options.transformLinkUri\n  const parentSchema = context.schema\n  /** @type {ReactMarkdownNames} */\n  // @ts-expect-error assume a known HTML/SVG element.\n  const name = node.tagName\n  /** @type {Record<string, unknown>} */\n  const properties = {}\n  let schema = parentSchema\n  /** @type {string} */\n  let property\n\n  if (parentSchema.space === 'html' && name === 'svg') {\n    schema = svg\n    context.schema = schema\n  }\n\n  if (node.properties) {\n    for (property in node.properties) {\n      if (own.call(node.properties, property)) {\n        addProperty(properties, property, node.properties[property], context)\n      }\n    }\n  }\n\n  if (name === 'ol' || name === 'ul') {\n    context.listDepth++\n  }\n\n  const children = childrenToReact(context, node)\n\n  if (name === 'ol' || name === 'ul') {\n    context.listDepth--\n  }\n\n  // Restore parent schema.\n  context.schema = parentSchema\n\n  // Nodes created by plugins do not have positional info, in which case we use\n  // an object that matches the position interface.\n  const position = node.position || {\n    start: {line: null, column: null, offset: null},\n    end: {line: null, column: null, offset: null}\n  }\n  const component =\n    options.components && own.call(options.components, name)\n      ? options.components[name]\n      : name\n  const basic = typeof component === 'string' || component === React.Fragment\n\n  if (!ReactIs.isValidElementType(component)) {\n    throw new TypeError(\n      `Component for name \\`${name}\\` not defined or is not renderable`\n    )\n  }\n\n  properties.key = index\n\n  if (name === 'a' && options.linkTarget) {\n    properties.target =\n      typeof options.linkTarget === 'function'\n        ? options.linkTarget(\n            String(properties.href || ''),\n            node.children,\n            typeof properties.title === 'string' ? properties.title : null\n          )\n        : options.linkTarget\n  }\n\n  if (name === 'a' && transform) {\n    properties.href = transform(\n      String(properties.href || ''),\n      node.children,\n      typeof properties.title === 'string' ? properties.title : null\n    )\n  }\n\n  if (\n    !basic &&\n    name === 'code' &&\n    parent.type === 'element' &&\n    parent.tagName !== 'pre'\n  ) {\n    properties.inline = true\n  }\n\n  if (\n    !basic &&\n    (name === 'h1' ||\n      name === 'h2' ||\n      name === 'h3' ||\n      name === 'h4' ||\n      name === 'h5' ||\n      name === 'h6')\n  ) {\n    properties.level = Number.parseInt(name.charAt(1), 10)\n  }\n\n  if (name === 'img' && options.transformImageUri) {\n    properties.src = options.transformImageUri(\n      String(properties.src || ''),\n      String(properties.alt || ''),\n      typeof properties.title === 'string' ? properties.title : null\n    )\n  }\n\n  if (!basic && name === 'li' && parent.type === 'element') {\n    const input = getInputElement(node)\n    properties.checked =\n      input && input.properties ? Boolean(input.properties.checked) : null\n    properties.index = getElementsBeforeCount(parent, node)\n    properties.ordered = parent.tagName === 'ol'\n  }\n\n  if (!basic && (name === 'ol' || name === 'ul')) {\n    properties.ordered = name === 'ol'\n    properties.depth = context.listDepth\n  }\n\n  if (name === 'td' || name === 'th') {\n    if (properties.align) {\n      if (!properties.style) properties.style = {}\n      // @ts-expect-error assume `style` is an object\n      properties.style.textAlign = properties.align\n      delete properties.align\n    }\n\n    if (!basic) {\n      properties.isHeader = name === 'th'\n    }\n  }\n\n  if (!basic && name === 'tr' && parent.type === 'element') {\n    properties.isHeader = Boolean(parent.tagName === 'thead')\n  }\n\n  // If `sourcePos` is given, pass source information (line/column info from markdown source).\n  if (options.sourcePos) {\n    properties['data-sourcepos'] = flattenPosition(position)\n  }\n\n  if (!basic && options.rawSourcePos) {\n    properties.sourcePosition = node.position\n  }\n\n  // If `includeElementIndex` is given, pass node index info to components.\n  if (!basic && options.includeElementIndex) {\n    properties.index = getElementsBeforeCount(parent, node)\n    properties.siblingCount = getElementsBeforeCount(parent)\n  }\n\n  if (!basic) {\n    properties.node = node\n  }\n\n  // Ensure no React warnings are emitted for void elements w/ children.\n  return children.length > 0\n    ? React.createElement(component, properties, children)\n    : React.createElement(component, properties)\n}\n\n/**\n * @param {Element|Root} node\n * @returns {Element?}\n */\nfunction getInputElement(node) {\n  let index = -1\n\n  while (++index < node.children.length) {\n    const child = node.children[index]\n\n    if (child.type === 'element' && child.tagName === 'input') {\n      return child\n    }\n  }\n\n  return null\n}\n\n/**\n * @param {Element|Root} parent\n * @param {Element} [node]\n * @returns {number}\n */\nfunction getElementsBeforeCount(parent, node) {\n  let index = -1\n  let count = 0\n\n  while (++index < parent.children.length) {\n    if (parent.children[index] === node) break\n    if (parent.children[index].type === 'element') count++\n  }\n\n  return count\n}\n\n/**\n * @param {Record<string, unknown>} props\n * @param {string} prop\n * @param {unknown} value\n * @param {Context} ctx\n */\nfunction addProperty(props, prop, value, ctx) {\n  const info = find(ctx.schema, prop)\n  let result = value\n\n  // Ignore nullish and `NaN` values.\n  // eslint-disable-next-line no-self-compare\n  if (result === null || result === undefined || result !== result) {\n    return\n  }\n\n  // Accept `array`.\n  // Most props are space-separated.\n  if (Array.isArray(result)) {\n    result = info.commaSeparated ? commas(result) : spaces(result)\n  }\n\n  if (info.property === 'style' && typeof result === 'string') {\n    result = parseStyle(result)\n  }\n\n  if (info.space && info.property) {\n    props[\n      own.call(hastToReact, info.property)\n        ? hastToReact[info.property]\n        : info.property\n    ] = result\n  } else if (info.attribute) {\n    props[info.attribute] = result\n  }\n}\n\n/**\n * @param {string} value\n * @returns {Record<string, string>}\n */\nfunction parseStyle(value) {\n  /** @type {Record<string, string>} */\n  const result = {}\n\n  try {\n    style(value, iterator)\n  } catch {\n    // Silent.\n  }\n\n  return result\n\n  /**\n   * @param {string} name\n   * @param {string} v\n   */\n  function iterator(name, v) {\n    const k = name.slice(0, 4) === '-ms-' ? `ms-${name.slice(4)}` : name\n    result[k.replace(/-([a-z])/g, styleReplacer)] = v\n  }\n}\n\n/**\n * @param {unknown} _\n * @param {string} $1\n */\nfunction styleReplacer(_, $1) {\n  return $1.toUpperCase()\n}\n\n/**\n * @param {Position|{start: {line: null, column: null, offset: null}, end: {line: null, column: null, offset: null}}} pos\n * @returns {string}\n */\nfunction flattenPosition(pos) {\n  return [\n    pos.start.line,\n    ':',\n    pos.start.column,\n    '-',\n    pos.end.line,\n    ':',\n    pos.end.column\n  ]\n    .map(String)\n    .join('')\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,UAAU;AAC9B,SAAQC,UAAU,QAAO,sBAAsB;AAC/C,SAAQC,GAAG,EAAEC,IAAI,EAAEC,WAAW,QAAO,sBAAsB;AAC3D,SAAQC,SAAS,IAAIC,MAAM,QAAO,wBAAwB;AAC1D,SAAQD,SAAS,IAAIE,MAAM,QAAO,wBAAwB;AAC1D,OAAOC,KAAK,MAAM,iBAAiB;AACnC,SAAQC,cAAc,QAAO,sBAAsB;AAEnD,MAAMC,GAAG,GAAG,CAAC,CAAC,CAACC,cAAc;;AAE7B;AACA;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;;AAEzE;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,OAAO,EAAEC,IAAI,EAAE;EAC7C;EACA,MAAMC,QAAQ,GAAG,EAAE;EACnB,IAAIC,UAAU,GAAG,CAAC,CAAC;EACnB;EACA,IAAIC,KAAK;EAET,OAAO,EAAED,UAAU,GAAGF,IAAI,CAACC,QAAQ,CAACG,MAAM,EAAE;IAC1CD,KAAK,GAAGH,IAAI,CAACC,QAAQ,CAACC,UAAU,CAAC;IAEjC,IAAIC,KAAK,CAACE,IAAI,KAAK,SAAS,EAAE;MAC5BJ,QAAQ,CAACK,IAAI,CAACC,OAAO,CAACR,OAAO,EAAEI,KAAK,EAAED,UAAU,EAAEF,IAAI,CAAC,CAAC;IAC1D,CAAC,MAAM,IAAIG,KAAK,CAACE,IAAI,KAAK,MAAM,EAAE;MAChC;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IACEL,IAAI,CAACK,IAAI,KAAK,SAAS,IACvB,CAACT,aAAa,CAACY,GAAG,CAACR,IAAI,CAACS,OAAO,CAAC,IAChC,CAACxB,UAAU,CAACkB,KAAK,CAAC,EAClB;QACAF,QAAQ,CAACK,IAAI,CAACH,KAAK,CAACO,KAAK,CAAC;MAC5B;IACF,CAAC,MAAM,IAAIP,KAAK,CAACE,IAAI,KAAK,KAAK,IAAI,CAACN,OAAO,CAACY,OAAO,CAACC,QAAQ,EAAE;MAC5D;MACAX,QAAQ,CAACK,IAAI,CAACH,KAAK,CAACO,KAAK,CAAC;IAC5B;EACF;EAEA,OAAOT,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,OAAOA,CAACR,OAAO,EAAEC,IAAI,EAAEa,KAAK,EAAEC,MAAM,EAAE;EAC7C,MAAMH,OAAO,GAAGZ,OAAO,CAACY,OAAO;EAC/B,MAAMI,SAAS,GACbJ,OAAO,CAACK,gBAAgB,KAAKC,SAAS,GAClCxB,cAAc,GACdkB,OAAO,CAACK,gBAAgB;EAC9B,MAAME,YAAY,GAAGnB,OAAO,CAACoB,MAAM;EACnC;EACA;EACA,MAAMC,IAAI,GAAGpB,IAAI,CAACS,OAAO;EACzB;EACA,MAAMY,UAAU,GAAG,CAAC,CAAC;EACrB,IAAIF,MAAM,GAAGD,YAAY;EACzB;EACA,IAAII,QAAQ;EAEZ,IAAIJ,YAAY,CAACK,KAAK,KAAK,MAAM,IAAIH,IAAI,KAAK,KAAK,EAAE;IACnDD,MAAM,GAAGjC,GAAG;IACZa,OAAO,CAACoB,MAAM,GAAGA,MAAM;EACzB;EAEA,IAAInB,IAAI,CAACqB,UAAU,EAAE;IACnB,KAAKC,QAAQ,IAAItB,IAAI,CAACqB,UAAU,EAAE;MAChC,IAAI3B,GAAG,CAAC8B,IAAI,CAACxB,IAAI,CAACqB,UAAU,EAAEC,QAAQ,CAAC,EAAE;QACvCG,WAAW,CAACJ,UAAU,EAAEC,QAAQ,EAAEtB,IAAI,CAACqB,UAAU,CAACC,QAAQ,CAAC,EAAEvB,OAAO,CAAC;MACvE;IACF;EACF;EAEA,IAAIqB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,IAAI,EAAE;IAClCrB,OAAO,CAAC2B,SAAS,EAAE;EACrB;EAEA,MAAMzB,QAAQ,GAAGH,eAAe,CAACC,OAAO,EAAEC,IAAI,CAAC;EAE/C,IAAIoB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,IAAI,EAAE;IAClCrB,OAAO,CAAC2B,SAAS,EAAE;EACrB;;EAEA;EACA3B,OAAO,CAACoB,MAAM,GAAGD,YAAY;;EAE7B;EACA;EACA,MAAMS,QAAQ,GAAG3B,IAAI,CAAC2B,QAAQ,IAAI;IAChCC,KAAK,EAAE;MAACC,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAI,CAAC;IAC/CC,GAAG,EAAE;MAACH,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAI;EAC9C,CAAC;EACD,MAAME,SAAS,GACbtB,OAAO,CAACuB,UAAU,IAAIxC,GAAG,CAAC8B,IAAI,CAACb,OAAO,CAACuB,UAAU,EAAEd,IAAI,CAAC,GACpDT,OAAO,CAACuB,UAAU,CAACd,IAAI,CAAC,GACxBA,IAAI;EACV,MAAMe,KAAK,GAAG,OAAOF,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAKlD,KAAK,CAACqD,QAAQ;EAE3E,IAAI,CAACpD,OAAO,CAACqD,kBAAkB,CAACJ,SAAS,CAAC,EAAE;IAC1C,MAAM,IAAIK,SAAS,CACjB,wBAAwBlB,IAAI,qCAC9B,CAAC;EACH;EAEAC,UAAU,CAACkB,GAAG,GAAG1B,KAAK;EAEtB,IAAIO,IAAI,KAAK,GAAG,IAAIT,OAAO,CAAC6B,UAAU,EAAE;IACtCnB,UAAU,CAACoB,MAAM,GACf,OAAO9B,OAAO,CAAC6B,UAAU,KAAK,UAAU,GACpC7B,OAAO,CAAC6B,UAAU,CAChBE,MAAM,CAACrB,UAAU,CAACsB,IAAI,IAAI,EAAE,CAAC,EAC7B3C,IAAI,CAACC,QAAQ,EACb,OAAOoB,UAAU,CAACuB,KAAK,KAAK,QAAQ,GAAGvB,UAAU,CAACuB,KAAK,GAAG,IAC5D,CAAC,GACDjC,OAAO,CAAC6B,UAAU;EAC1B;EAEA,IAAIpB,IAAI,KAAK,GAAG,IAAIL,SAAS,EAAE;IAC7BM,UAAU,CAACsB,IAAI,GAAG5B,SAAS,CACzB2B,MAAM,CAACrB,UAAU,CAACsB,IAAI,IAAI,EAAE,CAAC,EAC7B3C,IAAI,CAACC,QAAQ,EACb,OAAOoB,UAAU,CAACuB,KAAK,KAAK,QAAQ,GAAGvB,UAAU,CAACuB,KAAK,GAAG,IAC5D,CAAC;EACH;EAEA,IACE,CAACT,KAAK,IACNf,IAAI,KAAK,MAAM,IACfN,MAAM,CAACT,IAAI,KAAK,SAAS,IACzBS,MAAM,CAACL,OAAO,KAAK,KAAK,EACxB;IACAY,UAAU,CAACwB,MAAM,GAAG,IAAI;EAC1B;EAEA,IACE,CAACV,KAAK,KACLf,IAAI,KAAK,IAAI,IACZA,IAAI,KAAK,IAAI,IACbA,IAAI,KAAK,IAAI,IACbA,IAAI,KAAK,IAAI,IACbA,IAAI,KAAK,IAAI,IACbA,IAAI,KAAK,IAAI,CAAC,EAChB;IACAC,UAAU,CAACyB,KAAK,GAAGC,MAAM,CAACC,QAAQ,CAAC5B,IAAI,CAAC6B,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACxD;EAEA,IAAI7B,IAAI,KAAK,KAAK,IAAIT,OAAO,CAACuC,iBAAiB,EAAE;IAC/C7B,UAAU,CAAC8B,GAAG,GAAGxC,OAAO,CAACuC,iBAAiB,CACxCR,MAAM,CAACrB,UAAU,CAAC8B,GAAG,IAAI,EAAE,CAAC,EAC5BT,MAAM,CAACrB,UAAU,CAAC+B,GAAG,IAAI,EAAE,CAAC,EAC5B,OAAO/B,UAAU,CAACuB,KAAK,KAAK,QAAQ,GAAGvB,UAAU,CAACuB,KAAK,GAAG,IAC5D,CAAC;EACH;EAEA,IAAI,CAACT,KAAK,IAAIf,IAAI,KAAK,IAAI,IAAIN,MAAM,CAACT,IAAI,KAAK,SAAS,EAAE;IACxD,MAAMgD,KAAK,GAAGC,eAAe,CAACtD,IAAI,CAAC;IACnCqB,UAAU,CAACkC,OAAO,GAChBF,KAAK,IAAIA,KAAK,CAAChC,UAAU,GAAGmC,OAAO,CAACH,KAAK,CAAChC,UAAU,CAACkC,OAAO,CAAC,GAAG,IAAI;IACtElC,UAAU,CAACR,KAAK,GAAG4C,sBAAsB,CAAC3C,MAAM,EAAEd,IAAI,CAAC;IACvDqB,UAAU,CAACqC,OAAO,GAAG5C,MAAM,CAACL,OAAO,KAAK,IAAI;EAC9C;EAEA,IAAI,CAAC0B,KAAK,KAAKf,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,IAAI,CAAC,EAAE;IAC9CC,UAAU,CAACqC,OAAO,GAAGtC,IAAI,KAAK,IAAI;IAClCC,UAAU,CAACsC,KAAK,GAAG5D,OAAO,CAAC2B,SAAS;EACtC;EAEA,IAAIN,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,IAAI,EAAE;IAClC,IAAIC,UAAU,CAACuC,KAAK,EAAE;MACpB,IAAI,CAACvC,UAAU,CAAC7B,KAAK,EAAE6B,UAAU,CAAC7B,KAAK,GAAG,CAAC,CAAC;MAC5C;MACA6B,UAAU,CAAC7B,KAAK,CAACqE,SAAS,GAAGxC,UAAU,CAACuC,KAAK;MAC7C,OAAOvC,UAAU,CAACuC,KAAK;IACzB;IAEA,IAAI,CAACzB,KAAK,EAAE;MACVd,UAAU,CAACyC,QAAQ,GAAG1C,IAAI,KAAK,IAAI;IACrC;EACF;EAEA,IAAI,CAACe,KAAK,IAAIf,IAAI,KAAK,IAAI,IAAIN,MAAM,CAACT,IAAI,KAAK,SAAS,EAAE;IACxDgB,UAAU,CAACyC,QAAQ,GAAGN,OAAO,CAAC1C,MAAM,CAACL,OAAO,KAAK,OAAO,CAAC;EAC3D;;EAEA;EACA,IAAIE,OAAO,CAACoD,SAAS,EAAE;IACrB1C,UAAU,CAAC,gBAAgB,CAAC,GAAG2C,eAAe,CAACrC,QAAQ,CAAC;EAC1D;EAEA,IAAI,CAACQ,KAAK,IAAIxB,OAAO,CAACsD,YAAY,EAAE;IAClC5C,UAAU,CAAC6C,cAAc,GAAGlE,IAAI,CAAC2B,QAAQ;EAC3C;;EAEA;EACA,IAAI,CAACQ,KAAK,IAAIxB,OAAO,CAACwD,mBAAmB,EAAE;IACzC9C,UAAU,CAACR,KAAK,GAAG4C,sBAAsB,CAAC3C,MAAM,EAAEd,IAAI,CAAC;IACvDqB,UAAU,CAAC+C,YAAY,GAAGX,sBAAsB,CAAC3C,MAAM,CAAC;EAC1D;EAEA,IAAI,CAACqB,KAAK,EAAE;IACVd,UAAU,CAACrB,IAAI,GAAGA,IAAI;EACxB;;EAEA;EACA,OAAOC,QAAQ,CAACG,MAAM,GAAG,CAAC,GACtBrB,KAAK,CAACsF,aAAa,CAACpC,SAAS,EAAEZ,UAAU,EAAEpB,QAAQ,CAAC,GACpDlB,KAAK,CAACsF,aAAa,CAACpC,SAAS,EAAEZ,UAAU,CAAC;AAChD;;AAEA;AACA;AACA;AACA;AACA,SAASiC,eAAeA,CAACtD,IAAI,EAAE;EAC7B,IAAIa,KAAK,GAAG,CAAC,CAAC;EAEd,OAAO,EAAEA,KAAK,GAAGb,IAAI,CAACC,QAAQ,CAACG,MAAM,EAAE;IACrC,MAAMD,KAAK,GAAGH,IAAI,CAACC,QAAQ,CAACY,KAAK,CAAC;IAElC,IAAIV,KAAK,CAACE,IAAI,KAAK,SAAS,IAAIF,KAAK,CAACM,OAAO,KAAK,OAAO,EAAE;MACzD,OAAON,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASsD,sBAAsBA,CAAC3C,MAAM,EAAEd,IAAI,EAAE;EAC5C,IAAIa,KAAK,GAAG,CAAC,CAAC;EACd,IAAIyD,KAAK,GAAG,CAAC;EAEb,OAAO,EAAEzD,KAAK,GAAGC,MAAM,CAACb,QAAQ,CAACG,MAAM,EAAE;IACvC,IAAIU,MAAM,CAACb,QAAQ,CAACY,KAAK,CAAC,KAAKb,IAAI,EAAE;IACrC,IAAIc,MAAM,CAACb,QAAQ,CAACY,KAAK,CAAC,CAACR,IAAI,KAAK,SAAS,EAAEiE,KAAK,EAAE;EACxD;EAEA,OAAOA,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS7C,WAAWA,CAAC8C,KAAK,EAAEC,IAAI,EAAE9D,KAAK,EAAE+D,GAAG,EAAE;EAC5C,MAAMC,IAAI,GAAGvF,IAAI,CAACsF,GAAG,CAACtD,MAAM,EAAEqD,IAAI,CAAC;EACnC,IAAIG,MAAM,GAAGjE,KAAK;;EAElB;EACA;EACA,IAAIiE,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK1D,SAAS,IAAI0D,MAAM,KAAKA,MAAM,EAAE;IAChE;EACF;;EAEA;EACA;EACA,IAAIC,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;IACzBA,MAAM,GAAGD,IAAI,CAACI,cAAc,GAAGvF,MAAM,CAACoF,MAAM,CAAC,GAAGrF,MAAM,CAACqF,MAAM,CAAC;EAChE;EAEA,IAAID,IAAI,CAACpD,QAAQ,KAAK,OAAO,IAAI,OAAOqD,MAAM,KAAK,QAAQ,EAAE;IAC3DA,MAAM,GAAGI,UAAU,CAACJ,MAAM,CAAC;EAC7B;EAEA,IAAID,IAAI,CAACnD,KAAK,IAAImD,IAAI,CAACpD,QAAQ,EAAE;IAC/BiD,KAAK,CACH7E,GAAG,CAAC8B,IAAI,CAACpC,WAAW,EAAEsF,IAAI,CAACpD,QAAQ,CAAC,GAChClC,WAAW,CAACsF,IAAI,CAACpD,QAAQ,CAAC,GAC1BoD,IAAI,CAACpD,QAAQ,CAClB,GAAGqD,MAAM;EACZ,CAAC,MAAM,IAAID,IAAI,CAACM,SAAS,EAAE;IACzBT,KAAK,CAACG,IAAI,CAACM,SAAS,CAAC,GAAGL,MAAM;EAChC;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASI,UAAUA,CAACrE,KAAK,EAAE;EACzB;EACA,MAAMiE,MAAM,GAAG,CAAC,CAAC;EAEjB,IAAI;IACFnF,KAAK,CAACkB,KAAK,EAAEuE,QAAQ,CAAC;EACxB,CAAC,CAAC,MAAM;IACN;EAAA;EAGF,OAAON,MAAM;;EAEb;AACF;AACA;AACA;EACE,SAASM,QAAQA,CAAC7D,IAAI,EAAE8D,CAAC,EAAE;IACzB,MAAMC,CAAC,GAAG/D,IAAI,CAACgE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,GAAG,MAAMhE,IAAI,CAACgE,KAAK,CAAC,CAAC,CAAC,EAAE,GAAGhE,IAAI;IACpEuD,MAAM,CAACQ,CAAC,CAACE,OAAO,CAAC,WAAW,EAAEC,aAAa,CAAC,CAAC,GAAGJ,CAAC;EACnD;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASI,aAAaA,CAACC,CAAC,EAAEC,EAAE,EAAE;EAC5B,OAAOA,EAAE,CAACC,WAAW,CAAC,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA,SAASzB,eAAeA,CAAC0B,GAAG,EAAE;EAC5B,OAAO,CACLA,GAAG,CAAC9D,KAAK,CAACC,IAAI,EACd,GAAG,EACH6D,GAAG,CAAC9D,KAAK,CAACE,MAAM,EAChB,GAAG,EACH4D,GAAG,CAAC1D,GAAG,CAACH,IAAI,EACZ,GAAG,EACH6D,GAAG,CAAC1D,GAAG,CAACF,MAAM,CACf,CACE6D,GAAG,CAACjD,MAAM,CAAC,CACXkD,IAAI,CAAC,EAAE,CAAC;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}