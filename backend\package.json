{"name": "agentic-ai-backend", "version": "1.0.0", "description": "Backend API for Agentic AI Billing Assistant", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "pdf-parse": "^1.1.1", "openai": "^4.20.1", "dotenv": "^16.3.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "compression": "^1.7.4", "morgan": "^1.10.0", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["ai", "billing", "chatbot", "openai", "pdf"], "author": "Paymentus", "license": "ISC"}