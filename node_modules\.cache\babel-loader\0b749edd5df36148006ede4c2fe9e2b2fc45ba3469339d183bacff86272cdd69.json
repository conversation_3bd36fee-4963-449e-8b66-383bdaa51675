{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport { decodeNamedCharacterReference } from 'decode-named-character-reference';\nimport { asciiAlphanumeric, asciiDigit, asciiHexDigit } from 'micromark-util-character';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { constants } from 'micromark-util-symbol/constants.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\n\n/** @type {Construct} */\nexport const characterReference = {\n  name: 'characterReference',\n  tokenize: tokenizeCharacterReference\n};\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterReference(effects, ok, nok) {\n  const self = this;\n  let size = 0;\n  /** @type {number} */\n  let max;\n  /** @type {(code: Code) => boolean} */\n  let test;\n  return start;\n\n  /**\n   * Start of character reference.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *      ^\n   * > | a&#123;b\n   *      ^\n   * > | a&#x9;b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.ampersand, 'expected `&`');\n    effects.enter(types.characterReference);\n    effects.enter(types.characterReferenceMarker);\n    effects.consume(code);\n    effects.exit(types.characterReferenceMarker);\n    return open;\n  }\n\n  /**\n   * After `&`, at `#` for numeric references or alphanumeric for named\n   * references.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^\n   * > | a&#123;b\n   *       ^\n   * > | a&#x9;b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.numberSign) {\n      effects.enter(types.characterReferenceMarkerNumeric);\n      effects.consume(code);\n      effects.exit(types.characterReferenceMarkerNumeric);\n      return numeric;\n    }\n    effects.enter(types.characterReferenceValue);\n    max = constants.characterReferenceNamedSizeMax;\n    test = asciiAlphanumeric;\n    return value(code);\n  }\n\n  /**\n   * After `#`, at `x` for hexadecimals or digit for decimals.\n   *\n   * ```markdown\n   * > | a&#123;b\n   *        ^\n   * > | a&#x9;b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function numeric(code) {\n    if (code === codes.uppercaseX || code === codes.lowercaseX) {\n      effects.enter(types.characterReferenceMarkerHexadecimal);\n      effects.consume(code);\n      effects.exit(types.characterReferenceMarkerHexadecimal);\n      effects.enter(types.characterReferenceValue);\n      max = constants.characterReferenceHexadecimalSizeMax;\n      test = asciiHexDigit;\n      return value;\n    }\n    effects.enter(types.characterReferenceValue);\n    max = constants.characterReferenceDecimalSizeMax;\n    test = asciiDigit;\n    return value(code);\n  }\n\n  /**\n   * After markers (`&#x`, `&#`, or `&`), in value, before `;`.\n   *\n   * The character reference kind defines what and how many characters are\n   * allowed.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^^^\n   * > | a&#123;b\n   *        ^^^\n   * > | a&#x9;b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function value(code) {\n    if (code === codes.semicolon && size) {\n      const token = effects.exit(types.characterReferenceValue);\n      if (test === asciiAlphanumeric && !decodeNamedCharacterReference(self.sliceSerialize(token))) {\n        return nok(code);\n      }\n\n      // To do: `markdown-rs` uses a different name:\n      // `CharacterReferenceMarkerSemi`.\n      effects.enter(types.characterReferenceMarker);\n      effects.consume(code);\n      effects.exit(types.characterReferenceMarker);\n      effects.exit(types.characterReference);\n      return ok;\n    }\n    if (test(code) && size++ < max) {\n      effects.consume(code);\n      return value;\n    }\n    return nok(code);\n  }\n}", "map": {"version": 3, "names": ["decodeNamedCharacterReference", "asciiAlphanumeric", "asciiDigit", "asciiHexDigit", "codes", "constants", "types", "ok", "assert", "characterReference", "name", "tokenize", "tokenizeCharacterReference", "effects", "nok", "self", "size", "max", "test", "start", "code", "ampersand", "enter", "character<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "consume", "exit", "open", "numberSign", "characterReferenceMarkerNumeric", "numeric", "characterReferenceValue", "characterReferenceNamedSizeMax", "value", "uppercaseX", "lowercaseX", "characterReferenceMarkerHexadecimal", "characterReferenceHexadecimalSizeMax", "characterReferenceDecimalSizeMax", "semicolon", "token", "sliceSerialize"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/micromark-core-commonmark/dev/lib/character-reference.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {\n  asciiAlphanumeric,\n  asciiDigit,\n  asciiHexDigit\n} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/** @type {Construct} */\nexport const characterReference = {\n  name: 'characterReference',\n  tokenize: tokenizeCharacterReference\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterReference(effects, ok, nok) {\n  const self = this\n  let size = 0\n  /** @type {number} */\n  let max\n  /** @type {(code: Code) => boolean} */\n  let test\n\n  return start\n\n  /**\n   * Start of character reference.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *      ^\n   * > | a&#123;b\n   *      ^\n   * > | a&#x9;b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.ampersand, 'expected `&`')\n    effects.enter(types.characterReference)\n    effects.enter(types.characterReferenceMarker)\n    effects.consume(code)\n    effects.exit(types.characterReferenceMarker)\n    return open\n  }\n\n  /**\n   * After `&`, at `#` for numeric references or alphanumeric for named\n   * references.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^\n   * > | a&#123;b\n   *       ^\n   * > | a&#x9;b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.numberSign) {\n      effects.enter(types.characterReferenceMarkerNumeric)\n      effects.consume(code)\n      effects.exit(types.characterReferenceMarkerNumeric)\n      return numeric\n    }\n\n    effects.enter(types.characterReferenceValue)\n    max = constants.characterReferenceNamedSizeMax\n    test = asciiAlphanumeric\n    return value(code)\n  }\n\n  /**\n   * After `#`, at `x` for hexadecimals or digit for decimals.\n   *\n   * ```markdown\n   * > | a&#123;b\n   *        ^\n   * > | a&#x9;b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function numeric(code) {\n    if (code === codes.uppercaseX || code === codes.lowercaseX) {\n      effects.enter(types.characterReferenceMarkerHexadecimal)\n      effects.consume(code)\n      effects.exit(types.characterReferenceMarkerHexadecimal)\n      effects.enter(types.characterReferenceValue)\n      max = constants.characterReferenceHexadecimalSizeMax\n      test = asciiHexDigit\n      return value\n    }\n\n    effects.enter(types.characterReferenceValue)\n    max = constants.characterReferenceDecimalSizeMax\n    test = asciiDigit\n    return value(code)\n  }\n\n  /**\n   * After markers (`&#x`, `&#`, or `&`), in value, before `;`.\n   *\n   * The character reference kind defines what and how many characters are\n   * allowed.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^^^\n   * > | a&#123;b\n   *        ^^^\n   * > | a&#x9;b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function value(code) {\n    if (code === codes.semicolon && size) {\n      const token = effects.exit(types.characterReferenceValue)\n\n      if (\n        test === asciiAlphanumeric &&\n        !decodeNamedCharacterReference(self.sliceSerialize(token))\n      ) {\n        return nok(code)\n      }\n\n      // To do: `markdown-rs` uses a different name:\n      // `CharacterReferenceMarkerSemi`.\n      effects.enter(types.characterReferenceMarker)\n      effects.consume(code)\n      effects.exit(types.characterReferenceMarker)\n      effects.exit(types.characterReference)\n      return ok\n    }\n\n    if (test(code) && size++ < max) {\n      effects.consume(code)\n      return value\n    }\n\n    return nok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,6BAA6B,QAAO,kCAAkC;AAC9E,SACEC,iBAAiB,EACjBC,UAAU,EACVC,aAAa,QACR,0BAA0B;AACjC,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,SAAS,QAAO,oCAAoC;AAC5D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;;AAEvC;AACA,OAAO,MAAMC,kBAAkB,GAAG;EAChCC,IAAI,EAAE,oBAAoB;EAC1BC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASA,0BAA0BA,CAACC,OAAO,EAAEN,EAAE,EAAEO,GAAG,EAAE;EACpD,MAAMC,IAAI,GAAG,IAAI;EACjB,IAAIC,IAAI,GAAG,CAAC;EACZ;EACA,IAAIC,GAAG;EACP;EACA,IAAIC,IAAI;EAER,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnBZ,MAAM,CAACY,IAAI,KAAKhB,KAAK,CAACiB,SAAS,EAAE,cAAc,CAAC;IAChDR,OAAO,CAACS,KAAK,CAAChB,KAAK,CAACG,kBAAkB,CAAC;IACvCI,OAAO,CAACS,KAAK,CAAChB,KAAK,CAACiB,wBAAwB,CAAC;IAC7CV,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;IACrBP,OAAO,CAACY,IAAI,CAACnB,KAAK,CAACiB,wBAAwB,CAAC;IAC5C,OAAOG,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,IAAIA,CAACN,IAAI,EAAE;IAClB,IAAIA,IAAI,KAAKhB,KAAK,CAACuB,UAAU,EAAE;MAC7Bd,OAAO,CAACS,KAAK,CAAChB,KAAK,CAACsB,+BAA+B,CAAC;MACpDf,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrBP,OAAO,CAACY,IAAI,CAACnB,KAAK,CAACsB,+BAA+B,CAAC;MACnD,OAAOC,OAAO;IAChB;IAEAhB,OAAO,CAACS,KAAK,CAAChB,KAAK,CAACwB,uBAAuB,CAAC;IAC5Cb,GAAG,GAAGZ,SAAS,CAAC0B,8BAA8B;IAC9Cb,IAAI,GAAGjB,iBAAiB;IACxB,OAAO+B,KAAK,CAACZ,IAAI,CAAC;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASS,OAAOA,CAACT,IAAI,EAAE;IACrB,IAAIA,IAAI,KAAKhB,KAAK,CAAC6B,UAAU,IAAIb,IAAI,KAAKhB,KAAK,CAAC8B,UAAU,EAAE;MAC1DrB,OAAO,CAACS,KAAK,CAAChB,KAAK,CAAC6B,mCAAmC,CAAC;MACxDtB,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrBP,OAAO,CAACY,IAAI,CAACnB,KAAK,CAAC6B,mCAAmC,CAAC;MACvDtB,OAAO,CAACS,KAAK,CAAChB,KAAK,CAACwB,uBAAuB,CAAC;MAC5Cb,GAAG,GAAGZ,SAAS,CAAC+B,oCAAoC;MACpDlB,IAAI,GAAGf,aAAa;MACpB,OAAO6B,KAAK;IACd;IAEAnB,OAAO,CAACS,KAAK,CAAChB,KAAK,CAACwB,uBAAuB,CAAC;IAC5Cb,GAAG,GAAGZ,SAAS,CAACgC,gCAAgC;IAChDnB,IAAI,GAAGhB,UAAU;IACjB,OAAO8B,KAAK,CAACZ,IAAI,CAAC;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASY,KAAKA,CAACZ,IAAI,EAAE;IACnB,IAAIA,IAAI,KAAKhB,KAAK,CAACkC,SAAS,IAAItB,IAAI,EAAE;MACpC,MAAMuB,KAAK,GAAG1B,OAAO,CAACY,IAAI,CAACnB,KAAK,CAACwB,uBAAuB,CAAC;MAEzD,IACEZ,IAAI,KAAKjB,iBAAiB,IAC1B,CAACD,6BAA6B,CAACe,IAAI,CAACyB,cAAc,CAACD,KAAK,CAAC,CAAC,EAC1D;QACA,OAAOzB,GAAG,CAACM,IAAI,CAAC;MAClB;;MAEA;MACA;MACAP,OAAO,CAACS,KAAK,CAAChB,KAAK,CAACiB,wBAAwB,CAAC;MAC7CV,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrBP,OAAO,CAACY,IAAI,CAACnB,KAAK,CAACiB,wBAAwB,CAAC;MAC5CV,OAAO,CAACY,IAAI,CAACnB,KAAK,CAACG,kBAAkB,CAAC;MACtC,OAAOF,EAAE;IACX;IAEA,IAAIW,IAAI,CAACE,IAAI,CAAC,IAAIJ,IAAI,EAAE,GAAGC,GAAG,EAAE;MAC9BJ,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOY,KAAK;IACd;IAEA,OAAOlB,GAAG,CAACM,IAAI,CAAC;EAClB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}