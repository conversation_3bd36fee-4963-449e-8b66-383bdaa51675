{"ast": null, "code": "import { useCombineMotionValues } from './use-combine-values.mjs';\nimport { isMotionValue } from './utils/is-motion-value.mjs';\n\n/**\n * Combine multiple motion values into a new one using a string template literal.\n *\n * ```jsx\n * import {\n *   motion,\n *   useSpring,\n *   useMotionValue,\n *   useMotionTemplate\n * } from \"framer-motion\"\n *\n * function Component() {\n *   const shadowX = useSpring(0)\n *   const shadowY = useMotionValue(0)\n *   const shadow = useMotionTemplate`drop-shadow(${shadowX}px ${shadowY}px 20px rgba(0,0,0,0.3))`\n *\n *   return <motion.div style={{ filter: shadow }} />\n * }\n * ```\n *\n * @public\n */\nfunction useMotionTemplate(fragments, ...values) {\n  /**\n   * Create a function that will build a string from the latest motion values.\n   */\n  const numFragments = fragments.length;\n  function buildValue() {\n    let output = ``;\n    for (let i = 0; i < numFragments; i++) {\n      output += fragments[i];\n      const value = values[i];\n      if (value) {\n        output += isMotionValue(value) ? value.get() : value;\n      }\n    }\n    return output;\n  }\n  return useCombineMotionValues(values.filter(isMotionValue), buildValue);\n}\nexport { useMotionTemplate };", "map": {"version": 3, "names": ["useCombineMotionValues", "isMotionValue", "useMotionTemplate", "fragments", "values", "numFragments", "length", "buildValue", "output", "i", "value", "get", "filter"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/framer-motion/dist/es/value/use-motion-template.mjs"], "sourcesContent": ["import { useCombineMotionValues } from './use-combine-values.mjs';\nimport { isMotionValue } from './utils/is-motion-value.mjs';\n\n/**\n * Combine multiple motion values into a new one using a string template literal.\n *\n * ```jsx\n * import {\n *   motion,\n *   useSpring,\n *   useMotionValue,\n *   useMotionTemplate\n * } from \"framer-motion\"\n *\n * function Component() {\n *   const shadowX = useSpring(0)\n *   const shadowY = useMotionValue(0)\n *   const shadow = useMotionTemplate`drop-shadow(${shadowX}px ${shadowY}px 20px rgba(0,0,0,0.3))`\n *\n *   return <motion.div style={{ filter: shadow }} />\n * }\n * ```\n *\n * @public\n */\nfunction useMotionTemplate(fragments, ...values) {\n    /**\n     * Create a function that will build a string from the latest motion values.\n     */\n    const numFragments = fragments.length;\n    function buildValue() {\n        let output = ``;\n        for (let i = 0; i < numFragments; i++) {\n            output += fragments[i];\n            const value = values[i];\n            if (value) {\n                output += isMotionValue(value) ? value.get() : value;\n            }\n        }\n        return output;\n    }\n    return useCombineMotionValues(values.filter(isMotionValue), buildValue);\n}\n\nexport { useMotionTemplate };\n"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,0BAA0B;AACjE,SAASC,aAAa,QAAQ,6BAA6B;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,SAAS,EAAE,GAAGC,MAAM,EAAE;EAC7C;AACJ;AACA;EACI,MAAMC,YAAY,GAAGF,SAAS,CAACG,MAAM;EACrC,SAASC,UAAUA,CAAA,EAAG;IAClB,IAAIC,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,EAAEI,CAAC,EAAE,EAAE;MACnCD,MAAM,IAAIL,SAAS,CAACM,CAAC,CAAC;MACtB,MAAMC,KAAK,GAAGN,MAAM,CAACK,CAAC,CAAC;MACvB,IAAIC,KAAK,EAAE;QACPF,MAAM,IAAIP,aAAa,CAACS,KAAK,CAAC,GAAGA,KAAK,CAACC,GAAG,CAAC,CAAC,GAAGD,KAAK;MACxD;IACJ;IACA,OAAOF,MAAM;EACjB;EACA,OAAOR,sBAAsB,CAACI,MAAM,CAACQ,MAAM,CAACX,aAAa,CAAC,EAAEM,UAAU,CAAC;AAC3E;AAEA,SAASL,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}