{"ast": null, "code": "// To do: remove `void`s\n// To do: remove `null` from output of our APIs, allow it as user APIs.\n\n/**\n * @typedef {(error?: Error | null | undefined, ...output: Array<any>) => void} Callback\n *   Callback.\n *\n * @typedef {(...input: Array<any>) => any} Middleware\n *   Ware.\n *\n * @typedef Pipeline\n *   Pipeline.\n * @property {Run} run\n *   Run the pipeline.\n * @property {Use} use\n *   Add middleware.\n *\n * @typedef {(...input: Array<any>) => void} Run\n *   Call all middleware.\n *\n *   Calls `done` on completion with either an error or the output of the\n *   last middleware.\n *\n *   > 👉 **Note**: as the length of input defines whether async functions get a\n *   > `next` function,\n *   > it’s recommended to keep `input` at one value normally.\n\n *\n * @typedef {(fn: Middleware) => Pipeline} Use\n *   Add middleware.\n */\n\n/**\n * Create new middleware.\n *\n * @returns {Pipeline}\n *   Pipeline.\n */\nexport function trough() {\n  /** @type {Array<Middleware>} */\n  const fns = [];\n  /** @type {Pipeline} */\n  const pipeline = {\n    run,\n    use\n  };\n  return pipeline;\n\n  /** @type {Run} */\n  function run(...values) {\n    let middlewareIndex = -1;\n    /** @type {Callback} */\n    const callback = values.pop();\n    if (typeof callback !== 'function') {\n      throw new TypeError('Expected function as last argument, not ' + callback);\n    }\n    next(null, ...values);\n\n    /**\n     * Run the next `fn`, or we’re done.\n     *\n     * @param {Error | null | undefined} error\n     * @param {Array<any>} output\n     */\n    function next(error, ...output) {\n      const fn = fns[++middlewareIndex];\n      let index = -1;\n      if (error) {\n        callback(error);\n        return;\n      }\n\n      // Copy non-nullish input into values.\n      while (++index < values.length) {\n        if (output[index] === null || output[index] === undefined) {\n          output[index] = values[index];\n        }\n      }\n\n      // Save the newly created `output` for the next call.\n      values = output;\n\n      // Next or done.\n      if (fn) {\n        wrap(fn, next)(...output);\n      } else {\n        callback(null, ...output);\n      }\n    }\n  }\n\n  /** @type {Use} */\n  function use(middelware) {\n    if (typeof middelware !== 'function') {\n      throw new TypeError('Expected `middelware` to be a function, not ' + middelware);\n    }\n    fns.push(middelware);\n    return pipeline;\n  }\n}\n\n/**\n * Wrap `middleware` into a uniform interface.\n *\n * You can pass all input to the resulting function.\n * `callback` is then called with the output of `middleware`.\n *\n * If `middleware` accepts more arguments than the later given in input,\n * an extra `done` function is passed to it after that input,\n * which must be called by `middleware`.\n *\n * The first value in `input` is the main input value.\n * All other input values are the rest input values.\n * The values given to `callback` are the input values,\n * merged with every non-nullish output value.\n *\n * * if `middleware` throws an error,\n *   returns a promise that is rejected,\n *   or calls the given `done` function with an error,\n *   `callback` is called with that error\n * * if `middleware` returns a value or returns a promise that is resolved,\n *   that value is the main output value\n * * if `middleware` calls `done`,\n *   all non-nullish values except for the first one (the error) overwrite the\n *   output values\n *\n * @param {Middleware} middleware\n *   Function to wrap.\n * @param {Callback} callback\n *   Callback called with the output of `middleware`.\n * @returns {Run}\n *   Wrapped middleware.\n */\nexport function wrap(middleware, callback) {\n  /** @type {boolean} */\n  let called;\n  return wrapped;\n\n  /**\n   * Call `middleware`.\n   * @this {any}\n   * @param {Array<any>} parameters\n   * @returns {void}\n   */\n  function wrapped(...parameters) {\n    const fnExpectsCallback = middleware.length > parameters.length;\n    /** @type {any} */\n    let result;\n    if (fnExpectsCallback) {\n      parameters.push(done);\n    }\n    try {\n      result = middleware.apply(this, parameters);\n    } catch (error) {\n      const exception = /** @type {Error} */error;\n\n      // Well, this is quite the pickle.\n      // `middleware` received a callback and called it synchronously, but that\n      // threw an error.\n      // The only thing left to do is to throw the thing instead.\n      if (fnExpectsCallback && called) {\n        throw exception;\n      }\n      return done(exception);\n    }\n    if (!fnExpectsCallback) {\n      if (result && result.then && typeof result.then === 'function') {\n        result.then(then, done);\n      } else if (result instanceof Error) {\n        done(result);\n      } else {\n        then(result);\n      }\n    }\n  }\n\n  /**\n   * Call `callback`, only once.\n   *\n   * @type {Callback}\n   */\n  function done(error, ...output) {\n    if (!called) {\n      called = true;\n      callback(error, ...output);\n    }\n  }\n\n  /**\n   * Call `done` with one value.\n   *\n   * @param {any} [value]\n   */\n  function then(value) {\n    done(null, value);\n  }\n}", "map": {"version": 3, "names": ["trough", "fns", "pipeline", "run", "use", "values", "middlewareIndex", "callback", "pop", "TypeError", "next", "error", "output", "fn", "index", "length", "undefined", "wrap", "middelware", "push", "middleware", "called", "wrapped", "parameters", "fnExpectsCallback", "result", "done", "apply", "exception", "then", "Error", "value"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/trough/lib/index.js"], "sourcesContent": ["// To do: remove `void`s\n// To do: remove `null` from output of our APIs, allow it as user APIs.\n\n/**\n * @typedef {(error?: Error | null | undefined, ...output: Array<any>) => void} Callback\n *   Callback.\n *\n * @typedef {(...input: Array<any>) => any} Middleware\n *   Ware.\n *\n * @typedef Pipeline\n *   Pipeline.\n * @property {Run} run\n *   Run the pipeline.\n * @property {Use} use\n *   Add middleware.\n *\n * @typedef {(...input: Array<any>) => void} Run\n *   Call all middleware.\n *\n *   Calls `done` on completion with either an error or the output of the\n *   last middleware.\n *\n *   > 👉 **Note**: as the length of input defines whether async functions get a\n *   > `next` function,\n *   > it’s recommended to keep `input` at one value normally.\n\n *\n * @typedef {(fn: Middleware) => Pipeline} Use\n *   Add middleware.\n */\n\n/**\n * Create new middleware.\n *\n * @returns {Pipeline}\n *   Pipeline.\n */\nexport function trough() {\n  /** @type {Array<Middleware>} */\n  const fns = []\n  /** @type {Pipeline} */\n  const pipeline = {run, use}\n\n  return pipeline\n\n  /** @type {Run} */\n  function run(...values) {\n    let middlewareIndex = -1\n    /** @type {Callback} */\n    const callback = values.pop()\n\n    if (typeof callback !== 'function') {\n      throw new TypeError('Expected function as last argument, not ' + callback)\n    }\n\n    next(null, ...values)\n\n    /**\n     * Run the next `fn`, or we’re done.\n     *\n     * @param {Error | null | undefined} error\n     * @param {Array<any>} output\n     */\n    function next(error, ...output) {\n      const fn = fns[++middlewareIndex]\n      let index = -1\n\n      if (error) {\n        callback(error)\n        return\n      }\n\n      // Copy non-nullish input into values.\n      while (++index < values.length) {\n        if (output[index] === null || output[index] === undefined) {\n          output[index] = values[index]\n        }\n      }\n\n      // Save the newly created `output` for the next call.\n      values = output\n\n      // Next or done.\n      if (fn) {\n        wrap(fn, next)(...output)\n      } else {\n        callback(null, ...output)\n      }\n    }\n  }\n\n  /** @type {Use} */\n  function use(middelware) {\n    if (typeof middelware !== 'function') {\n      throw new TypeError(\n        'Expected `middelware` to be a function, not ' + middelware\n      )\n    }\n\n    fns.push(middelware)\n    return pipeline\n  }\n}\n\n/**\n * Wrap `middleware` into a uniform interface.\n *\n * You can pass all input to the resulting function.\n * `callback` is then called with the output of `middleware`.\n *\n * If `middleware` accepts more arguments than the later given in input,\n * an extra `done` function is passed to it after that input,\n * which must be called by `middleware`.\n *\n * The first value in `input` is the main input value.\n * All other input values are the rest input values.\n * The values given to `callback` are the input values,\n * merged with every non-nullish output value.\n *\n * * if `middleware` throws an error,\n *   returns a promise that is rejected,\n *   or calls the given `done` function with an error,\n *   `callback` is called with that error\n * * if `middleware` returns a value or returns a promise that is resolved,\n *   that value is the main output value\n * * if `middleware` calls `done`,\n *   all non-nullish values except for the first one (the error) overwrite the\n *   output values\n *\n * @param {Middleware} middleware\n *   Function to wrap.\n * @param {Callback} callback\n *   Callback called with the output of `middleware`.\n * @returns {Run}\n *   Wrapped middleware.\n */\nexport function wrap(middleware, callback) {\n  /** @type {boolean} */\n  let called\n\n  return wrapped\n\n  /**\n   * Call `middleware`.\n   * @this {any}\n   * @param {Array<any>} parameters\n   * @returns {void}\n   */\n  function wrapped(...parameters) {\n    const fnExpectsCallback = middleware.length > parameters.length\n    /** @type {any} */\n    let result\n\n    if (fnExpectsCallback) {\n      parameters.push(done)\n    }\n\n    try {\n      result = middleware.apply(this, parameters)\n    } catch (error) {\n      const exception = /** @type {Error} */ (error)\n\n      // Well, this is quite the pickle.\n      // `middleware` received a callback and called it synchronously, but that\n      // threw an error.\n      // The only thing left to do is to throw the thing instead.\n      if (fnExpectsCallback && called) {\n        throw exception\n      }\n\n      return done(exception)\n    }\n\n    if (!fnExpectsCallback) {\n      if (result && result.then && typeof result.then === 'function') {\n        result.then(then, done)\n      } else if (result instanceof Error) {\n        done(result)\n      } else {\n        then(result)\n      }\n    }\n  }\n\n  /**\n   * Call `callback`, only once.\n   *\n   * @type {Callback}\n   */\n  function done(error, ...output) {\n    if (!called) {\n      called = true\n      callback(error, ...output)\n    }\n  }\n\n  /**\n   * Call `done` with one value.\n   *\n   * @param {any} [value]\n   */\n  function then(value) {\n    done(null, value)\n  }\n}\n"], "mappings": "AAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,MAAMA,CAAA,EAAG;EACvB;EACA,MAAMC,GAAG,GAAG,EAAE;EACd;EACA,MAAMC,QAAQ,GAAG;IAACC,GAAG;IAAEC;EAAG,CAAC;EAE3B,OAAOF,QAAQ;;EAEf;EACA,SAASC,GAAGA,CAAC,GAAGE,MAAM,EAAE;IACtB,IAAIC,eAAe,GAAG,CAAC,CAAC;IACxB;IACA,MAAMC,QAAQ,GAAGF,MAAM,CAACG,GAAG,CAAC,CAAC;IAE7B,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAAE;MAClC,MAAM,IAAIE,SAAS,CAAC,0CAA0C,GAAGF,QAAQ,CAAC;IAC5E;IAEAG,IAAI,CAAC,IAAI,EAAE,GAAGL,MAAM,CAAC;;IAErB;AACJ;AACA;AACA;AACA;AACA;IACI,SAASK,IAAIA,CAACC,KAAK,EAAE,GAAGC,MAAM,EAAE;MAC9B,MAAMC,EAAE,GAAGZ,GAAG,CAAC,EAAEK,eAAe,CAAC;MACjC,IAAIQ,KAAK,GAAG,CAAC,CAAC;MAEd,IAAIH,KAAK,EAAE;QACTJ,QAAQ,CAACI,KAAK,CAAC;QACf;MACF;;MAEA;MACA,OAAO,EAAEG,KAAK,GAAGT,MAAM,CAACU,MAAM,EAAE;QAC9B,IAAIH,MAAM,CAACE,KAAK,CAAC,KAAK,IAAI,IAAIF,MAAM,CAACE,KAAK,CAAC,KAAKE,SAAS,EAAE;UACzDJ,MAAM,CAACE,KAAK,CAAC,GAAGT,MAAM,CAACS,KAAK,CAAC;QAC/B;MACF;;MAEA;MACAT,MAAM,GAAGO,MAAM;;MAEf;MACA,IAAIC,EAAE,EAAE;QACNI,IAAI,CAACJ,EAAE,EAAEH,IAAI,CAAC,CAAC,GAAGE,MAAM,CAAC;MAC3B,CAAC,MAAM;QACLL,QAAQ,CAAC,IAAI,EAAE,GAAGK,MAAM,CAAC;MAC3B;IACF;EACF;;EAEA;EACA,SAASR,GAAGA,CAACc,UAAU,EAAE;IACvB,IAAI,OAAOA,UAAU,KAAK,UAAU,EAAE;MACpC,MAAM,IAAIT,SAAS,CACjB,8CAA8C,GAAGS,UACnD,CAAC;IACH;IAEAjB,GAAG,CAACkB,IAAI,CAACD,UAAU,CAAC;IACpB,OAAOhB,QAAQ;EACjB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASe,IAAIA,CAACG,UAAU,EAAEb,QAAQ,EAAE;EACzC;EACA,IAAIc,MAAM;EAEV,OAAOC,OAAO;;EAEd;AACF;AACA;AACA;AACA;AACA;EACE,SAASA,OAAOA,CAAC,GAAGC,UAAU,EAAE;IAC9B,MAAMC,iBAAiB,GAAGJ,UAAU,CAACL,MAAM,GAAGQ,UAAU,CAACR,MAAM;IAC/D;IACA,IAAIU,MAAM;IAEV,IAAID,iBAAiB,EAAE;MACrBD,UAAU,CAACJ,IAAI,CAACO,IAAI,CAAC;IACvB;IAEA,IAAI;MACFD,MAAM,GAAGL,UAAU,CAACO,KAAK,CAAC,IAAI,EAAEJ,UAAU,CAAC;IAC7C,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACd,MAAMiB,SAAS,GAAG,oBAAsBjB,KAAM;;MAE9C;MACA;MACA;MACA;MACA,IAAIa,iBAAiB,IAAIH,MAAM,EAAE;QAC/B,MAAMO,SAAS;MACjB;MAEA,OAAOF,IAAI,CAACE,SAAS,CAAC;IACxB;IAEA,IAAI,CAACJ,iBAAiB,EAAE;MACtB,IAAIC,MAAM,IAAIA,MAAM,CAACI,IAAI,IAAI,OAAOJ,MAAM,CAACI,IAAI,KAAK,UAAU,EAAE;QAC9DJ,MAAM,CAACI,IAAI,CAACA,IAAI,EAAEH,IAAI,CAAC;MACzB,CAAC,MAAM,IAAID,MAAM,YAAYK,KAAK,EAAE;QAClCJ,IAAI,CAACD,MAAM,CAAC;MACd,CAAC,MAAM;QACLI,IAAI,CAACJ,MAAM,CAAC;MACd;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASC,IAAIA,CAACf,KAAK,EAAE,GAAGC,MAAM,EAAE;IAC9B,IAAI,CAACS,MAAM,EAAE;MACXA,MAAM,GAAG,IAAI;MACbd,QAAQ,CAACI,KAAK,EAAE,GAAGC,MAAM,CAAC;IAC5B;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASiB,IAAIA,CAACE,KAAK,EAAE;IACnBL,IAAI,CAAC,IAAI,EAAEK,KAAK,CAAC;EACnB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}