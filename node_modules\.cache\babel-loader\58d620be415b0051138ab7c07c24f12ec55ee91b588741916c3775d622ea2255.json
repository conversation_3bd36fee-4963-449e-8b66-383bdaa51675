{"ast": null, "code": "const protocols = ['http', 'https', 'mailto', 'tel'];\n\n/**\n * @param {string} uri\n * @returns {string}\n */\nexport function uriTransformer(uri) {\n  const url = (uri || '').trim();\n  const first = url.charAt(0);\n  if (first === '#' || first === '/') {\n    return url;\n  }\n  const colon = url.indexOf(':');\n  if (colon === -1) {\n    return url;\n  }\n  let index = -1;\n  while (++index < protocols.length) {\n    const protocol = protocols[index];\n    if (colon === protocol.length && url.slice(0, protocol.length).toLowerCase() === protocol) {\n      return url;\n    }\n  }\n  index = url.indexOf('?');\n  if (index !== -1 && colon > index) {\n    return url;\n  }\n  index = url.indexOf('#');\n  if (index !== -1 && colon > index) {\n    return url;\n  }\n\n  // eslint-disable-next-line no-script-url\n  return 'javascript:void(0)';\n}", "map": {"version": 3, "names": ["protocols", "uriTransformer", "uri", "url", "trim", "first", "char<PERSON>t", "colon", "indexOf", "index", "length", "protocol", "slice", "toLowerCase"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/react-markdown/lib/uri-transformer.js"], "sourcesContent": ["const protocols = ['http', 'https', 'mailto', 'tel']\n\n/**\n * @param {string} uri\n * @returns {string}\n */\nexport function uriTransformer(uri) {\n  const url = (uri || '').trim()\n  const first = url.charAt(0)\n\n  if (first === '#' || first === '/') {\n    return url\n  }\n\n  const colon = url.indexOf(':')\n  if (colon === -1) {\n    return url\n  }\n\n  let index = -1\n\n  while (++index < protocols.length) {\n    const protocol = protocols[index]\n\n    if (\n      colon === protocol.length &&\n      url.slice(0, protocol.length).toLowerCase() === protocol\n    ) {\n      return url\n    }\n  }\n\n  index = url.indexOf('?')\n  if (index !== -1 && colon > index) {\n    return url\n  }\n\n  index = url.indexOf('#')\n  if (index !== -1 && colon > index) {\n    return url\n  }\n\n  // eslint-disable-next-line no-script-url\n  return 'javascript:void(0)'\n}\n"], "mappings": "AAAA,MAAMA,SAAS,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;;AAEpD;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,GAAG,EAAE;EAClC,MAAMC,GAAG,GAAG,CAACD,GAAG,IAAI,EAAE,EAAEE,IAAI,CAAC,CAAC;EAC9B,MAAMC,KAAK,GAAGF,GAAG,CAACG,MAAM,CAAC,CAAC,CAAC;EAE3B,IAAID,KAAK,KAAK,GAAG,IAAIA,KAAK,KAAK,GAAG,EAAE;IAClC,OAAOF,GAAG;EACZ;EAEA,MAAMI,KAAK,GAAGJ,GAAG,CAACK,OAAO,CAAC,GAAG,CAAC;EAC9B,IAAID,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB,OAAOJ,GAAG;EACZ;EAEA,IAAIM,KAAK,GAAG,CAAC,CAAC;EAEd,OAAO,EAAEA,KAAK,GAAGT,SAAS,CAACU,MAAM,EAAE;IACjC,MAAMC,QAAQ,GAAGX,SAAS,CAACS,KAAK,CAAC;IAEjC,IACEF,KAAK,KAAKI,QAAQ,CAACD,MAAM,IACzBP,GAAG,CAACS,KAAK,CAAC,CAAC,EAAED,QAAQ,CAACD,MAAM,CAAC,CAACG,WAAW,CAAC,CAAC,KAAKF,QAAQ,EACxD;MACA,OAAOR,GAAG;IACZ;EACF;EAEAM,KAAK,GAAGN,GAAG,CAACK,OAAO,CAAC,GAAG,CAAC;EACxB,IAAIC,KAAK,KAAK,CAAC,CAAC,IAAIF,KAAK,GAAGE,KAAK,EAAE;IACjC,OAAON,GAAG;EACZ;EAEAM,KAAK,GAAGN,GAAG,CAACK,OAAO,CAAC,GAAG,CAAC;EACxB,IAAIC,KAAK,KAAK,CAAC,CAAC,IAAIF,KAAK,GAAGE,KAAK,EAAE;IACjC,OAAON,GAAG;EACZ;;EAEA;EACA,OAAO,oBAAoB;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}