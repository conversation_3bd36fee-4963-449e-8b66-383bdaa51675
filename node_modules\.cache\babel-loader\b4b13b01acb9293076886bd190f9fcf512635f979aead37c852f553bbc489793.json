{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoice-extracting-agent\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport ChatInterface from './components/ChatInterface';\nimport FileUpload from './components/FileUpload';\nimport Header from './components/Header';\nimport BackgroundAnimation from './components/BackgroundAnimation';\nimport InvoiceDisplay from './components/InvoiceDisplay';\nimport { ChatProvider } from './context/ChatContext';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [conversationId, setConversationId] = useState(null);\n  const [invoiceData, setInvoiceData] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const handleFileUpload = async file => {\n    setIsLoading(true);\n    try {\n      const formData = new FormData();\n      formData.append('pdf', file);\n      const response = await fetch('/api/upload-pdf', {\n        method: 'POST',\n        body: formData\n      });\n      if (!response.ok) {\n        throw new Error('Failed to upload file');\n      }\n      const result = await response.json();\n      setConversationId(result.conversationId);\n      setInvoiceData(result.data);\n    } catch (error) {\n      console.error('Upload error:', error);\n      // Handle error (show toast, etc.)\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(ChatProvider, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-800 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(BackgroundAnimation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10 flex flex-col h-screen\",\n        children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"flex-1 flex flex-col lg:flex-row gap-6 p-6 max-w-7xl mx-auto w-full\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"lg:w-1/3 space-y-6\",\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            children: [/*#__PURE__*/_jsxDEV(FileUpload, {\n              onFileUpload: handleFileUpload,\n              isLoading: isLoading\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n              children: invoiceData && /*#__PURE__*/_jsxDEV(InvoiceDisplay, {\n                data: invoiceData,\n                conversationId: conversationId\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"lg:w-2/3 flex flex-col\",\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.2\n            },\n            children: /*#__PURE__*/_jsxDEV(ChatInterface, {\n              conversationId: conversationId,\n              invoiceData: invoiceData\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"AgwNQf3x6+3CEqokZXul2mOuy6Y=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "AnimatePresence", "ChatInterface", "FileUpload", "Header", "BackgroundAnimation", "InvoiceDisplay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "_s", "conversationId", "setConversationId", "invoiceData", "setInvoiceData", "isLoading", "setIsLoading", "handleFileUpload", "file", "formData", "FormData", "append", "response", "fetch", "method", "body", "ok", "Error", "result", "json", "data", "error", "console", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "x", "animate", "transition", "duration", "onFileUpload", "delay", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport ChatInterface from './components/ChatInterface';\nimport FileUpload from './components/FileUpload';\nimport Header from './components/Header';\nimport BackgroundAnimation from './components/BackgroundAnimation';\nimport InvoiceDisplay from './components/InvoiceDisplay';\nimport { ChatProvider } from './context/ChatContext';\nimport './App.css';\n\nfunction App() {\n  const [conversationId, setConversationId] = useState(null);\n  const [invoiceData, setInvoiceData] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n\n  const handleFileUpload = async (file) => {\n    setIsLoading(true);\n    try {\n      const formData = new FormData();\n      formData.append('pdf', file);\n\n      const response = await fetch('/api/upload-pdf', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to upload file');\n      }\n\n      const result = await response.json();\n      setConversationId(result.conversationId);\n      setInvoiceData(result.data);\n    } catch (error) {\n      console.error('Upload error:', error);\n      // Handle error (show toast, etc.)\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <ChatProvider>\n      <div className=\"App min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-800 relative overflow-hidden\">\n        <BackgroundAnimation />\n        \n        <div className=\"relative z-10 flex flex-col h-screen\">\n          <Header />\n          \n          <main className=\"flex-1 flex flex-col lg:flex-row gap-6 p-6 max-w-7xl mx-auto w-full\">\n            {/* Left Panel - File Upload & Invoice Display */}\n            <motion.div \n              className=\"lg:w-1/3 space-y-6\"\n              initial={{ opacity: 0, x: -50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6 }}\n            >\n              <FileUpload \n                onFileUpload={handleFileUpload} \n                isLoading={isLoading}\n              />\n              \n              <AnimatePresence>\n                {invoiceData && (\n                  <InvoiceDisplay \n                    data={invoiceData} \n                    conversationId={conversationId}\n                  />\n                )}\n              </AnimatePresence>\n            </motion.div>\n\n            {/* Right Panel - Chat Interface */}\n            <motion.div \n              className=\"lg:w-2/3 flex flex-col\"\n              initial={{ opacity: 0, x: 50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n            >\n              <ChatInterface \n                conversationId={conversationId}\n                invoiceData={invoiceData}\n              />\n            </motion.div>\n          </main>\n        </div>\n      </div>\n    </ChatProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,cAAc,MAAM,6BAA6B;AACxD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMoB,gBAAgB,GAAG,MAAOC,IAAI,IAAK;IACvCF,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF,MAAMG,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,KAAK,EAAEH,IAAI,CAAC;MAE5B,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,iBAAiB,EAAE;QAC9CC,MAAM,EAAE,MAAM;QACdC,IAAI,EAAEN;MACR,CAAC,CAAC;MAEF,IAAI,CAACG,QAAQ,CAACI,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;MAC1C;MAEA,MAAMC,MAAM,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MACpCjB,iBAAiB,CAACgB,MAAM,CAACjB,cAAc,CAAC;MACxCG,cAAc,CAACc,MAAM,CAACE,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC;IACF,CAAC,SAAS;MACRf,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACER,OAAA,CAACF,YAAY;IAAA2B,QAAA,eACXzB,OAAA;MAAK0B,SAAS,EAAC,oGAAoG;MAAAD,QAAA,gBACjHzB,OAAA,CAACJ,mBAAmB;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEvB9B,OAAA;QAAK0B,SAAS,EAAC,sCAAsC;QAAAD,QAAA,gBACnDzB,OAAA,CAACL,MAAM;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEV9B,OAAA;UAAM0B,SAAS,EAAC,qEAAqE;UAAAD,QAAA,gBAEnFzB,OAAA,CAACT,MAAM,CAACwC,GAAG;YACTL,SAAS,EAAC,oBAAoB;YAC9BM,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAZ,QAAA,gBAE9BzB,OAAA,CAACN,UAAU;cACT4C,YAAY,EAAE7B,gBAAiB;cAC/BF,SAAS,EAAEA;YAAU;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eAEF9B,OAAA,CAACR,eAAe;cAAAiC,QAAA,EACbpB,WAAW,iBACVL,OAAA,CAACH,cAAc;gBACbyB,IAAI,EAAEjB,WAAY;gBAClBF,cAAc,EAAEA;cAAe;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACc,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGb9B,OAAA,CAACT,MAAM,CAACwC,GAAG;YACTL,SAAS,EAAC,wBAAwB;YAClCM,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEE,KAAK,EAAE;YAAI,CAAE;YAAAd,QAAA,eAE1CzB,OAAA,CAACP,aAAa;cACZU,cAAc,EAAEA,cAAe;cAC/BE,WAAW,EAAEA;YAAY;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM,CAAC;AAEnB;AAAC5B,EAAA,CA/EQD,GAAG;AAAAuC,EAAA,GAAHvC,GAAG;AAiFZ,eAAeA,GAAG;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}