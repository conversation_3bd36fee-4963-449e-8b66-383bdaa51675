{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').ContainerState} ContainerState\n * @typedef {import('micromark-util-types').InitialConstruct} InitialConstruct\n * @typedef {import('micromark-util-types').Initializer} Initializer\n * @typedef {import('micromark-util-types').Point} Point\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n/**\n * @typedef {[Construct, ContainerState]} StackItem\n */\n\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding } from 'micromark-util-character';\nimport { splice } from 'micromark-util-chunked';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { constants } from 'micromark-util-symbol/constants.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\n\n/** @type {InitialConstruct} */\nexport const document = {\n  tokenize: initializeDocument\n};\n\n/** @type {Construct} */\nconst containerConstruct = {\n  tokenize: tokenizeContainer\n};\n\n/**\n * @this {TokenizeContext}\n * @type {Initializer}\n */\nfunction initializeDocument(effects) {\n  const self = this;\n  /** @type {Array<StackItem>} */\n  const stack = [];\n  let continued = 0;\n  /** @type {TokenizeContext | undefined} */\n  let childFlow;\n  /** @type {Token | undefined} */\n  let childToken;\n  /** @type {number} */\n  let lineStartOffset;\n  return start;\n\n  /** @type {State} */\n  function start(code) {\n    // First we iterate through the open blocks, starting with the root\n    // document, and descending through last children down to the last open\n    // block.\n    // Each block imposes a condition that the line must satisfy if the block is\n    // to remain open.\n    // For example, a block quote requires a `>` character.\n    // A paragraph requires a non-blank line.\n    // In this phase we may match all or just some of the open blocks.\n    // But we cannot close unmatched blocks yet, because we may have a lazy\n    // continuation line.\n    if (continued < stack.length) {\n      const item = stack[continued];\n      self.containerState = item[1];\n      assert(item[0].continuation, 'expected `continuation` to be defined on container construct');\n      return effects.attempt(item[0].continuation, documentContinue, checkNewContainers)(code);\n    }\n\n    // Done.\n    return checkNewContainers(code);\n  }\n\n  /** @type {State} */\n  function documentContinue(code) {\n    assert(self.containerState, 'expected `containerState` to be defined after continuation');\n    continued++;\n\n    // Note: this field is called `_closeFlow` but it also closes containers.\n    // Perhaps a good idea to rename it but it’s already used in the wild by\n    // extensions.\n    if (self.containerState._closeFlow) {\n      self.containerState._closeFlow = undefined;\n      if (childFlow) {\n        closeFlow();\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when dealing with lazy lines in `writeToChild`.\n      const indexBeforeExits = self.events.length;\n      let indexBeforeFlow = indexBeforeExits;\n      /** @type {Point | undefined} */\n      let point;\n\n      // Find the flow chunk.\n      while (indexBeforeFlow--) {\n        if (self.events[indexBeforeFlow][0] === 'exit' && self.events[indexBeforeFlow][1].type === types.chunkFlow) {\n          point = self.events[indexBeforeFlow][1].end;\n          break;\n        }\n      }\n      assert(point, 'could not find previous flow chunk');\n      exitContainers(continued);\n\n      // Fix positions.\n      let index = indexBeforeExits;\n      while (index < self.events.length) {\n        self.events[index][1].end = Object.assign({}, point);\n        index++;\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      splice(self.events, indexBeforeFlow + 1, 0, self.events.slice(indexBeforeExits));\n\n      // Discard the duplicate exits.\n      self.events.length = index;\n      return checkNewContainers(code);\n    }\n    return start(code);\n  }\n\n  /** @type {State} */\n  function checkNewContainers(code) {\n    // Next, after consuming the continuation markers for existing blocks, we\n    // look for new block starts (e.g. `>` for a block quote).\n    // If we encounter a new block start, we close any blocks unmatched in\n    // step 1 before creating the new block as a child of the last matched\n    // block.\n    if (continued === stack.length) {\n      // No need to `check` whether there’s a container, of `exitContainers`\n      // would be moot.\n      // We can instead immediately `attempt` to parse one.\n      if (!childFlow) {\n        return documentContinued(code);\n      }\n\n      // If we have concrete content, such as block HTML or fenced code,\n      // we can’t have containers “pierce” into them, so we can immediately\n      // start.\n      if (childFlow.currentConstruct && childFlow.currentConstruct.concrete) {\n        return flowStart(code);\n      }\n\n      // If we do have flow, it could still be a blank line,\n      // but we’d be interrupting it w/ a new container if there’s a current\n      // construct.\n      // To do: next major: remove `_gfmTableDynamicInterruptHack` (no longer\n      // needed in micromark-extension-gfm-table@1.0.6).\n      self.interrupt = Boolean(childFlow.currentConstruct && !childFlow._gfmTableDynamicInterruptHack);\n    }\n\n    // Check if there is a new container.\n    self.containerState = {};\n    return effects.check(containerConstruct, thereIsANewContainer, thereIsNoNewContainer)(code);\n  }\n\n  /** @type {State} */\n  function thereIsANewContainer(code) {\n    if (childFlow) closeFlow();\n    exitContainers(continued);\n    return documentContinued(code);\n  }\n\n  /** @type {State} */\n  function thereIsNoNewContainer(code) {\n    self.parser.lazy[self.now().line] = continued !== stack.length;\n    lineStartOffset = self.now().offset;\n    return flowStart(code);\n  }\n\n  /** @type {State} */\n  function documentContinued(code) {\n    // Try new containers.\n    self.containerState = {};\n    return effects.attempt(containerConstruct, containerContinue, flowStart)(code);\n  }\n\n  /** @type {State} */\n  function containerContinue(code) {\n    assert(self.currentConstruct, 'expected `currentConstruct` to be defined on tokenizer');\n    assert(self.containerState, 'expected `containerState` to be defined on tokenizer');\n    continued++;\n    stack.push([self.currentConstruct, self.containerState]);\n    // Try another.\n    return documentContinued(code);\n  }\n\n  /** @type {State} */\n  function flowStart(code) {\n    if (code === codes.eof) {\n      if (childFlow) closeFlow();\n      exitContainers(0);\n      effects.consume(code);\n      return;\n    }\n    childFlow = childFlow || self.parser.flow(self.now());\n    effects.enter(types.chunkFlow, {\n      contentType: constants.contentTypeFlow,\n      previous: childToken,\n      _tokenizer: childFlow\n    });\n    return flowContinue(code);\n  }\n\n  /** @type {State} */\n  function flowContinue(code) {\n    if (code === codes.eof) {\n      writeToChild(effects.exit(types.chunkFlow), true);\n      exitContainers(0);\n      effects.consume(code);\n      return;\n    }\n    if (markdownLineEnding(code)) {\n      effects.consume(code);\n      writeToChild(effects.exit(types.chunkFlow));\n      // Get ready for the next line.\n      continued = 0;\n      self.interrupt = undefined;\n      return start;\n    }\n    effects.consume(code);\n    return flowContinue;\n  }\n\n  /**\n   * @param {Token} token\n   * @param {boolean | undefined} [eof]\n   * @returns {void}\n   */\n  function writeToChild(token, eof) {\n    assert(childFlow, 'expected `childFlow` to be defined when continuing');\n    const stream = self.sliceStream(token);\n    if (eof) stream.push(null);\n    token.previous = childToken;\n    if (childToken) childToken.next = token;\n    childToken = token;\n    childFlow.defineSkip(token.start);\n    childFlow.write(stream);\n\n    // Alright, so we just added a lazy line:\n    //\n    // ```markdown\n    // > a\n    // b.\n    //\n    // Or:\n    //\n    // > ~~~c\n    // d\n    //\n    // Or:\n    //\n    // > | e |\n    // f\n    // ```\n    //\n    // The construct in the second example (fenced code) does not accept lazy\n    // lines, so it marked itself as done at the end of its first line, and\n    // then the content construct parses `d`.\n    // Most constructs in markdown match on the first line: if the first line\n    // forms a construct, a non-lazy line can’t “unmake” it.\n    //\n    // The construct in the third example is potentially a GFM table, and\n    // those are *weird*.\n    // It *could* be a table, from the first line, if the following line\n    // matches a condition.\n    // In this case, that second line is lazy, which “unmakes” the first line\n    // and turns the whole into one content block.\n    //\n    // We’ve now parsed the non-lazy and the lazy line, and can figure out\n    // whether the lazy line started a new flow block.\n    // If it did, we exit the current containers between the two flow blocks.\n    if (self.parser.lazy[token.start.line]) {\n      let index = childFlow.events.length;\n      while (index--) {\n        if (\n        // The token starts before the line ending…\n        childFlow.events[index][1].start.offset < lineStartOffset && (\n        // …and either is not ended yet…\n        !childFlow.events[index][1].end ||\n        // …or ends after it.\n        childFlow.events[index][1].end.offset > lineStartOffset)) {\n          // Exit: there’s still something open, which means it’s a lazy line\n          // part of something.\n          return;\n        }\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when closing flow in `documentContinue`.\n      const indexBeforeExits = self.events.length;\n      let indexBeforeFlow = indexBeforeExits;\n      /** @type {boolean | undefined} */\n      let seen;\n      /** @type {Point | undefined} */\n      let point;\n\n      // Find the previous chunk (the one before the lazy line).\n      while (indexBeforeFlow--) {\n        if (self.events[indexBeforeFlow][0] === 'exit' && self.events[indexBeforeFlow][1].type === types.chunkFlow) {\n          if (seen) {\n            point = self.events[indexBeforeFlow][1].end;\n            break;\n          }\n          seen = true;\n        }\n      }\n      assert(point, 'could not find previous flow chunk');\n      exitContainers(continued);\n\n      // Fix positions.\n      index = indexBeforeExits;\n      while (index < self.events.length) {\n        self.events[index][1].end = Object.assign({}, point);\n        index++;\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      splice(self.events, indexBeforeFlow + 1, 0, self.events.slice(indexBeforeExits));\n\n      // Discard the duplicate exits.\n      self.events.length = index;\n    }\n  }\n\n  /**\n   * @param {number} size\n   * @returns {void}\n   */\n  function exitContainers(size) {\n    let index = stack.length;\n\n    // Exit open containers.\n    while (index-- > size) {\n      const entry = stack[index];\n      self.containerState = entry[1];\n      assert(entry[0].exit, 'expected `exit` to be defined on container construct');\n      entry[0].exit.call(self, effects);\n    }\n    stack.length = size;\n  }\n  function closeFlow() {\n    assert(self.containerState, 'expected `containerState` to be defined when closing flow');\n    assert(childFlow, 'expected `childFlow` to be defined when closing it');\n    childFlow.write([codes.eof]);\n    childToken = undefined;\n    childFlow = undefined;\n    self.containerState._closeFlow = undefined;\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeContainer(effects, ok, nok) {\n  // Always populated by defaults.\n  assert(this.parser.constructs.disable.null, 'expected `disable.null` to be populated');\n  return factorySpace(effects, effects.attempt(this.parser.constructs.document, ok, nok), types.linePrefix, this.parser.constructs.disable.null.includes('codeIndented') ? undefined : constants.tabSize);\n}", "map": {"version": 3, "names": ["factorySpace", "markdownLineEnding", "splice", "codes", "constants", "types", "ok", "assert", "document", "tokenize", "initializeDocument", "containerConstruct", "tokenizeContainer", "effects", "self", "stack", "continued", "childFlow", "childToken", "lineStartOffset", "start", "code", "length", "item", "containerState", "continuation", "attempt", "documentContinue", "checkNewContainers", "_closeFlow", "undefined", "closeFlow", "indexBeforeExits", "events", "indexBeforeFlow", "point", "type", "chunkFlow", "end", "exitContainers", "index", "Object", "assign", "slice", "documentContinued", "currentConstruct", "concrete", "flowStart", "interrupt", "Boolean", "_gfmTableDynamicInterruptHack", "check", "thereIsANewContainer", "thereIsNoNewContainer", "parser", "lazy", "now", "line", "offset", "containerContinue", "push", "eof", "consume", "flow", "enter", "contentType", "contentTypeFlow", "previous", "_tokenizer", "flowContinue", "writeToChild", "exit", "token", "stream", "sliceStream", "next", "defineSkip", "write", "seen", "size", "entry", "call", "nok", "constructs", "disable", "null", "linePrefix", "includes", "tabSize"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/micromark/dev/lib/initialize/document.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').ContainerState} ContainerState\n * @typedef {import('micromark-util-types').InitialConstruct} InitialConstruct\n * @typedef {import('micromark-util-types').Initializer} Initializer\n * @typedef {import('micromark-util-types').Point} Point\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n/**\n * @typedef {[Construct, ContainerState]} StackItem\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {splice} from 'micromark-util-chunked'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/** @type {InitialConstruct} */\nexport const document = {tokenize: initializeDocument}\n\n/** @type {Construct} */\nconst containerConstruct = {tokenize: tokenizeContainer}\n\n/**\n * @this {TokenizeContext}\n * @type {Initializer}\n */\nfunction initializeDocument(effects) {\n  const self = this\n  /** @type {Array<StackItem>} */\n  const stack = []\n  let continued = 0\n  /** @type {TokenizeContext | undefined} */\n  let childFlow\n  /** @type {Token | undefined} */\n  let childToken\n  /** @type {number} */\n  let lineStartOffset\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    // First we iterate through the open blocks, starting with the root\n    // document, and descending through last children down to the last open\n    // block.\n    // Each block imposes a condition that the line must satisfy if the block is\n    // to remain open.\n    // For example, a block quote requires a `>` character.\n    // A paragraph requires a non-blank line.\n    // In this phase we may match all or just some of the open blocks.\n    // But we cannot close unmatched blocks yet, because we may have a lazy\n    // continuation line.\n    if (continued < stack.length) {\n      const item = stack[continued]\n      self.containerState = item[1]\n      assert(\n        item[0].continuation,\n        'expected `continuation` to be defined on container construct'\n      )\n      return effects.attempt(\n        item[0].continuation,\n        documentContinue,\n        checkNewContainers\n      )(code)\n    }\n\n    // Done.\n    return checkNewContainers(code)\n  }\n\n  /** @type {State} */\n  function documentContinue(code) {\n    assert(\n      self.containerState,\n      'expected `containerState` to be defined after continuation'\n    )\n\n    continued++\n\n    // Note: this field is called `_closeFlow` but it also closes containers.\n    // Perhaps a good idea to rename it but it’s already used in the wild by\n    // extensions.\n    if (self.containerState._closeFlow) {\n      self.containerState._closeFlow = undefined\n\n      if (childFlow) {\n        closeFlow()\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when dealing with lazy lines in `writeToChild`.\n      const indexBeforeExits = self.events.length\n      let indexBeforeFlow = indexBeforeExits\n      /** @type {Point | undefined} */\n      let point\n\n      // Find the flow chunk.\n      while (indexBeforeFlow--) {\n        if (\n          self.events[indexBeforeFlow][0] === 'exit' &&\n          self.events[indexBeforeFlow][1].type === types.chunkFlow\n        ) {\n          point = self.events[indexBeforeFlow][1].end\n          break\n        }\n      }\n\n      assert(point, 'could not find previous flow chunk')\n\n      exitContainers(continued)\n\n      // Fix positions.\n      let index = indexBeforeExits\n\n      while (index < self.events.length) {\n        self.events[index][1].end = Object.assign({}, point)\n        index++\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      splice(\n        self.events,\n        indexBeforeFlow + 1,\n        0,\n        self.events.slice(indexBeforeExits)\n      )\n\n      // Discard the duplicate exits.\n      self.events.length = index\n\n      return checkNewContainers(code)\n    }\n\n    return start(code)\n  }\n\n  /** @type {State} */\n  function checkNewContainers(code) {\n    // Next, after consuming the continuation markers for existing blocks, we\n    // look for new block starts (e.g. `>` for a block quote).\n    // If we encounter a new block start, we close any blocks unmatched in\n    // step 1 before creating the new block as a child of the last matched\n    // block.\n    if (continued === stack.length) {\n      // No need to `check` whether there’s a container, of `exitContainers`\n      // would be moot.\n      // We can instead immediately `attempt` to parse one.\n      if (!childFlow) {\n        return documentContinued(code)\n      }\n\n      // If we have concrete content, such as block HTML or fenced code,\n      // we can’t have containers “pierce” into them, so we can immediately\n      // start.\n      if (childFlow.currentConstruct && childFlow.currentConstruct.concrete) {\n        return flowStart(code)\n      }\n\n      // If we do have flow, it could still be a blank line,\n      // but we’d be interrupting it w/ a new container if there’s a current\n      // construct.\n      // To do: next major: remove `_gfmTableDynamicInterruptHack` (no longer\n      // needed in micromark-extension-gfm-table@1.0.6).\n      self.interrupt = Boolean(\n        childFlow.currentConstruct && !childFlow._gfmTableDynamicInterruptHack\n      )\n    }\n\n    // Check if there is a new container.\n    self.containerState = {}\n    return effects.check(\n      containerConstruct,\n      thereIsANewContainer,\n      thereIsNoNewContainer\n    )(code)\n  }\n\n  /** @type {State} */\n  function thereIsANewContainer(code) {\n    if (childFlow) closeFlow()\n    exitContainers(continued)\n    return documentContinued(code)\n  }\n\n  /** @type {State} */\n  function thereIsNoNewContainer(code) {\n    self.parser.lazy[self.now().line] = continued !== stack.length\n    lineStartOffset = self.now().offset\n    return flowStart(code)\n  }\n\n  /** @type {State} */\n  function documentContinued(code) {\n    // Try new containers.\n    self.containerState = {}\n    return effects.attempt(\n      containerConstruct,\n      containerContinue,\n      flowStart\n    )(code)\n  }\n\n  /** @type {State} */\n  function containerContinue(code) {\n    assert(\n      self.currentConstruct,\n      'expected `currentConstruct` to be defined on tokenizer'\n    )\n    assert(\n      self.containerState,\n      'expected `containerState` to be defined on tokenizer'\n    )\n    continued++\n    stack.push([self.currentConstruct, self.containerState])\n    // Try another.\n    return documentContinued(code)\n  }\n\n  /** @type {State} */\n  function flowStart(code) {\n    if (code === codes.eof) {\n      if (childFlow) closeFlow()\n      exitContainers(0)\n      effects.consume(code)\n      return\n    }\n\n    childFlow = childFlow || self.parser.flow(self.now())\n    effects.enter(types.chunkFlow, {\n      contentType: constants.contentTypeFlow,\n      previous: childToken,\n      _tokenizer: childFlow\n    })\n\n    return flowContinue(code)\n  }\n\n  /** @type {State} */\n  function flowContinue(code) {\n    if (code === codes.eof) {\n      writeToChild(effects.exit(types.chunkFlow), true)\n      exitContainers(0)\n      effects.consume(code)\n      return\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.consume(code)\n      writeToChild(effects.exit(types.chunkFlow))\n      // Get ready for the next line.\n      continued = 0\n      self.interrupt = undefined\n      return start\n    }\n\n    effects.consume(code)\n    return flowContinue\n  }\n\n  /**\n   * @param {Token} token\n   * @param {boolean | undefined} [eof]\n   * @returns {void}\n   */\n  function writeToChild(token, eof) {\n    assert(childFlow, 'expected `childFlow` to be defined when continuing')\n    const stream = self.sliceStream(token)\n    if (eof) stream.push(null)\n    token.previous = childToken\n    if (childToken) childToken.next = token\n    childToken = token\n    childFlow.defineSkip(token.start)\n    childFlow.write(stream)\n\n    // Alright, so we just added a lazy line:\n    //\n    // ```markdown\n    // > a\n    // b.\n    //\n    // Or:\n    //\n    // > ~~~c\n    // d\n    //\n    // Or:\n    //\n    // > | e |\n    // f\n    // ```\n    //\n    // The construct in the second example (fenced code) does not accept lazy\n    // lines, so it marked itself as done at the end of its first line, and\n    // then the content construct parses `d`.\n    // Most constructs in markdown match on the first line: if the first line\n    // forms a construct, a non-lazy line can’t “unmake” it.\n    //\n    // The construct in the third example is potentially a GFM table, and\n    // those are *weird*.\n    // It *could* be a table, from the first line, if the following line\n    // matches a condition.\n    // In this case, that second line is lazy, which “unmakes” the first line\n    // and turns the whole into one content block.\n    //\n    // We’ve now parsed the non-lazy and the lazy line, and can figure out\n    // whether the lazy line started a new flow block.\n    // If it did, we exit the current containers between the two flow blocks.\n    if (self.parser.lazy[token.start.line]) {\n      let index = childFlow.events.length\n\n      while (index--) {\n        if (\n          // The token starts before the line ending…\n          childFlow.events[index][1].start.offset < lineStartOffset &&\n          // …and either is not ended yet…\n          (!childFlow.events[index][1].end ||\n            // …or ends after it.\n            childFlow.events[index][1].end.offset > lineStartOffset)\n        ) {\n          // Exit: there’s still something open, which means it’s a lazy line\n          // part of something.\n          return\n        }\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when closing flow in `documentContinue`.\n      const indexBeforeExits = self.events.length\n      let indexBeforeFlow = indexBeforeExits\n      /** @type {boolean | undefined} */\n      let seen\n      /** @type {Point | undefined} */\n      let point\n\n      // Find the previous chunk (the one before the lazy line).\n      while (indexBeforeFlow--) {\n        if (\n          self.events[indexBeforeFlow][0] === 'exit' &&\n          self.events[indexBeforeFlow][1].type === types.chunkFlow\n        ) {\n          if (seen) {\n            point = self.events[indexBeforeFlow][1].end\n            break\n          }\n\n          seen = true\n        }\n      }\n\n      assert(point, 'could not find previous flow chunk')\n\n      exitContainers(continued)\n\n      // Fix positions.\n      index = indexBeforeExits\n\n      while (index < self.events.length) {\n        self.events[index][1].end = Object.assign({}, point)\n        index++\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      splice(\n        self.events,\n        indexBeforeFlow + 1,\n        0,\n        self.events.slice(indexBeforeExits)\n      )\n\n      // Discard the duplicate exits.\n      self.events.length = index\n    }\n  }\n\n  /**\n   * @param {number} size\n   * @returns {void}\n   */\n  function exitContainers(size) {\n    let index = stack.length\n\n    // Exit open containers.\n    while (index-- > size) {\n      const entry = stack[index]\n      self.containerState = entry[1]\n      assert(\n        entry[0].exit,\n        'expected `exit` to be defined on container construct'\n      )\n      entry[0].exit.call(self, effects)\n    }\n\n    stack.length = size\n  }\n\n  function closeFlow() {\n    assert(\n      self.containerState,\n      'expected `containerState` to be defined when closing flow'\n    )\n    assert(childFlow, 'expected `childFlow` to be defined when closing it')\n    childFlow.write([codes.eof])\n    childToken = undefined\n    childFlow = undefined\n    self.containerState._closeFlow = undefined\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeContainer(effects, ok, nok) {\n  // Always populated by defaults.\n  assert(\n    this.parser.constructs.disable.null,\n    'expected `disable.null` to be populated'\n  )\n  return factorySpace(\n    effects,\n    effects.attempt(this.parser.constructs.document, ok, nok),\n    types.linePrefix,\n    this.parser.constructs.disable.null.includes('codeIndented')\n      ? undefined\n      : constants.tabSize\n  )\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,yBAAyB;AACpD,SAAQC,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,MAAM,QAAO,wBAAwB;AAC7C,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,SAAS,QAAO,oCAAoC;AAC5D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;;AAEvC;AACA,OAAO,MAAMC,QAAQ,GAAG;EAACC,QAAQ,EAAEC;AAAkB,CAAC;;AAEtD;AACA,MAAMC,kBAAkB,GAAG;EAACF,QAAQ,EAAEG;AAAiB,CAAC;;AAExD;AACA;AACA;AACA;AACA,SAASF,kBAAkBA,CAACG,OAAO,EAAE;EACnC,MAAMC,IAAI,GAAG,IAAI;EACjB;EACA,MAAMC,KAAK,GAAG,EAAE;EAChB,IAAIC,SAAS,GAAG,CAAC;EACjB;EACA,IAAIC,SAAS;EACb;EACA,IAAIC,UAAU;EACd;EACA,IAAIC,eAAe;EAEnB,OAAOC,KAAK;;EAEZ;EACA,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIL,SAAS,GAAGD,KAAK,CAACO,MAAM,EAAE;MAC5B,MAAMC,IAAI,GAAGR,KAAK,CAACC,SAAS,CAAC;MAC7BF,IAAI,CAACU,cAAc,GAAGD,IAAI,CAAC,CAAC,CAAC;MAC7BhB,MAAM,CACJgB,IAAI,CAAC,CAAC,CAAC,CAACE,YAAY,EACpB,8DACF,CAAC;MACD,OAAOZ,OAAO,CAACa,OAAO,CACpBH,IAAI,CAAC,CAAC,CAAC,CAACE,YAAY,EACpBE,gBAAgB,EAChBC,kBACF,CAAC,CAACP,IAAI,CAAC;IACT;;IAEA;IACA,OAAOO,kBAAkB,CAACP,IAAI,CAAC;EACjC;;EAEA;EACA,SAASM,gBAAgBA,CAACN,IAAI,EAAE;IAC9Bd,MAAM,CACJO,IAAI,CAACU,cAAc,EACnB,4DACF,CAAC;IAEDR,SAAS,EAAE;;IAEX;IACA;IACA;IACA,IAAIF,IAAI,CAACU,cAAc,CAACK,UAAU,EAAE;MAClCf,IAAI,CAACU,cAAc,CAACK,UAAU,GAAGC,SAAS;MAE1C,IAAIb,SAAS,EAAE;QACbc,SAAS,CAAC,CAAC;MACb;;MAEA;MACA;MACA,MAAMC,gBAAgB,GAAGlB,IAAI,CAACmB,MAAM,CAACX,MAAM;MAC3C,IAAIY,eAAe,GAAGF,gBAAgB;MACtC;MACA,IAAIG,KAAK;;MAET;MACA,OAAOD,eAAe,EAAE,EAAE;QACxB,IACEpB,IAAI,CAACmB,MAAM,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,IAC1CpB,IAAI,CAACmB,MAAM,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC,CAACE,IAAI,KAAK/B,KAAK,CAACgC,SAAS,EACxD;UACAF,KAAK,GAAGrB,IAAI,CAACmB,MAAM,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC,CAACI,GAAG;UAC3C;QACF;MACF;MAEA/B,MAAM,CAAC4B,KAAK,EAAE,oCAAoC,CAAC;MAEnDI,cAAc,CAACvB,SAAS,CAAC;;MAEzB;MACA,IAAIwB,KAAK,GAAGR,gBAAgB;MAE5B,OAAOQ,KAAK,GAAG1B,IAAI,CAACmB,MAAM,CAACX,MAAM,EAAE;QACjCR,IAAI,CAACmB,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,CAACF,GAAG,GAAGG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,KAAK,CAAC;QACpDK,KAAK,EAAE;MACT;;MAEA;MACAtC,MAAM,CACJY,IAAI,CAACmB,MAAM,EACXC,eAAe,GAAG,CAAC,EACnB,CAAC,EACDpB,IAAI,CAACmB,MAAM,CAACU,KAAK,CAACX,gBAAgB,CACpC,CAAC;;MAED;MACAlB,IAAI,CAACmB,MAAM,CAACX,MAAM,GAAGkB,KAAK;MAE1B,OAAOZ,kBAAkB,CAACP,IAAI,CAAC;IACjC;IAEA,OAAOD,KAAK,CAACC,IAAI,CAAC;EACpB;;EAEA;EACA,SAASO,kBAAkBA,CAACP,IAAI,EAAE;IAChC;IACA;IACA;IACA;IACA;IACA,IAAIL,SAAS,KAAKD,KAAK,CAACO,MAAM,EAAE;MAC9B;MACA;MACA;MACA,IAAI,CAACL,SAAS,EAAE;QACd,OAAO2B,iBAAiB,CAACvB,IAAI,CAAC;MAChC;;MAEA;MACA;MACA;MACA,IAAIJ,SAAS,CAAC4B,gBAAgB,IAAI5B,SAAS,CAAC4B,gBAAgB,CAACC,QAAQ,EAAE;QACrE,OAAOC,SAAS,CAAC1B,IAAI,CAAC;MACxB;;MAEA;MACA;MACA;MACA;MACA;MACAP,IAAI,CAACkC,SAAS,GAAGC,OAAO,CACtBhC,SAAS,CAAC4B,gBAAgB,IAAI,CAAC5B,SAAS,CAACiC,6BAC3C,CAAC;IACH;;IAEA;IACApC,IAAI,CAACU,cAAc,GAAG,CAAC,CAAC;IACxB,OAAOX,OAAO,CAACsC,KAAK,CAClBxC,kBAAkB,EAClByC,oBAAoB,EACpBC,qBACF,CAAC,CAAChC,IAAI,CAAC;EACT;;EAEA;EACA,SAAS+B,oBAAoBA,CAAC/B,IAAI,EAAE;IAClC,IAAIJ,SAAS,EAAEc,SAAS,CAAC,CAAC;IAC1BQ,cAAc,CAACvB,SAAS,CAAC;IACzB,OAAO4B,iBAAiB,CAACvB,IAAI,CAAC;EAChC;;EAEA;EACA,SAASgC,qBAAqBA,CAAChC,IAAI,EAAE;IACnCP,IAAI,CAACwC,MAAM,CAACC,IAAI,CAACzC,IAAI,CAAC0C,GAAG,CAAC,CAAC,CAACC,IAAI,CAAC,GAAGzC,SAAS,KAAKD,KAAK,CAACO,MAAM;IAC9DH,eAAe,GAAGL,IAAI,CAAC0C,GAAG,CAAC,CAAC,CAACE,MAAM;IACnC,OAAOX,SAAS,CAAC1B,IAAI,CAAC;EACxB;;EAEA;EACA,SAASuB,iBAAiBA,CAACvB,IAAI,EAAE;IAC/B;IACAP,IAAI,CAACU,cAAc,GAAG,CAAC,CAAC;IACxB,OAAOX,OAAO,CAACa,OAAO,CACpBf,kBAAkB,EAClBgD,iBAAiB,EACjBZ,SACF,CAAC,CAAC1B,IAAI,CAAC;EACT;;EAEA;EACA,SAASsC,iBAAiBA,CAACtC,IAAI,EAAE;IAC/Bd,MAAM,CACJO,IAAI,CAAC+B,gBAAgB,EACrB,wDACF,CAAC;IACDtC,MAAM,CACJO,IAAI,CAACU,cAAc,EACnB,sDACF,CAAC;IACDR,SAAS,EAAE;IACXD,KAAK,CAAC6C,IAAI,CAAC,CAAC9C,IAAI,CAAC+B,gBAAgB,EAAE/B,IAAI,CAACU,cAAc,CAAC,CAAC;IACxD;IACA,OAAOoB,iBAAiB,CAACvB,IAAI,CAAC;EAChC;;EAEA;EACA,SAAS0B,SAASA,CAAC1B,IAAI,EAAE;IACvB,IAAIA,IAAI,KAAKlB,KAAK,CAAC0D,GAAG,EAAE;MACtB,IAAI5C,SAAS,EAAEc,SAAS,CAAC,CAAC;MAC1BQ,cAAc,CAAC,CAAC,CAAC;MACjB1B,OAAO,CAACiD,OAAO,CAACzC,IAAI,CAAC;MACrB;IACF;IAEAJ,SAAS,GAAGA,SAAS,IAAIH,IAAI,CAACwC,MAAM,CAACS,IAAI,CAACjD,IAAI,CAAC0C,GAAG,CAAC,CAAC,CAAC;IACrD3C,OAAO,CAACmD,KAAK,CAAC3D,KAAK,CAACgC,SAAS,EAAE;MAC7B4B,WAAW,EAAE7D,SAAS,CAAC8D,eAAe;MACtCC,QAAQ,EAAEjD,UAAU;MACpBkD,UAAU,EAAEnD;IACd,CAAC,CAAC;IAEF,OAAOoD,YAAY,CAAChD,IAAI,CAAC;EAC3B;;EAEA;EACA,SAASgD,YAAYA,CAAChD,IAAI,EAAE;IAC1B,IAAIA,IAAI,KAAKlB,KAAK,CAAC0D,GAAG,EAAE;MACtBS,YAAY,CAACzD,OAAO,CAAC0D,IAAI,CAAClE,KAAK,CAACgC,SAAS,CAAC,EAAE,IAAI,CAAC;MACjDE,cAAc,CAAC,CAAC,CAAC;MACjB1B,OAAO,CAACiD,OAAO,CAACzC,IAAI,CAAC;MACrB;IACF;IAEA,IAAIpB,kBAAkB,CAACoB,IAAI,CAAC,EAAE;MAC5BR,OAAO,CAACiD,OAAO,CAACzC,IAAI,CAAC;MACrBiD,YAAY,CAACzD,OAAO,CAAC0D,IAAI,CAAClE,KAAK,CAACgC,SAAS,CAAC,CAAC;MAC3C;MACArB,SAAS,GAAG,CAAC;MACbF,IAAI,CAACkC,SAAS,GAAGlB,SAAS;MAC1B,OAAOV,KAAK;IACd;IAEAP,OAAO,CAACiD,OAAO,CAACzC,IAAI,CAAC;IACrB,OAAOgD,YAAY;EACrB;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASC,YAAYA,CAACE,KAAK,EAAEX,GAAG,EAAE;IAChCtD,MAAM,CAACU,SAAS,EAAE,oDAAoD,CAAC;IACvE,MAAMwD,MAAM,GAAG3D,IAAI,CAAC4D,WAAW,CAACF,KAAK,CAAC;IACtC,IAAIX,GAAG,EAAEY,MAAM,CAACb,IAAI,CAAC,IAAI,CAAC;IAC1BY,KAAK,CAACL,QAAQ,GAAGjD,UAAU;IAC3B,IAAIA,UAAU,EAAEA,UAAU,CAACyD,IAAI,GAAGH,KAAK;IACvCtD,UAAU,GAAGsD,KAAK;IAClBvD,SAAS,CAAC2D,UAAU,CAACJ,KAAK,CAACpD,KAAK,CAAC;IACjCH,SAAS,CAAC4D,KAAK,CAACJ,MAAM,CAAC;;IAEvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI3D,IAAI,CAACwC,MAAM,CAACC,IAAI,CAACiB,KAAK,CAACpD,KAAK,CAACqC,IAAI,CAAC,EAAE;MACtC,IAAIjB,KAAK,GAAGvB,SAAS,CAACgB,MAAM,CAACX,MAAM;MAEnC,OAAOkB,KAAK,EAAE,EAAE;QACd;QACE;QACAvB,SAAS,CAACgB,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,CAACpB,KAAK,CAACsC,MAAM,GAAGvC,eAAe;QACzD;QACC,CAACF,SAAS,CAACgB,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,CAACF,GAAG;QAC9B;QACArB,SAAS,CAACgB,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,CAACF,GAAG,CAACoB,MAAM,GAAGvC,eAAe,CAAC,EAC1D;UACA;UACA;UACA;QACF;MACF;;MAEA;MACA;MACA,MAAMa,gBAAgB,GAAGlB,IAAI,CAACmB,MAAM,CAACX,MAAM;MAC3C,IAAIY,eAAe,GAAGF,gBAAgB;MACtC;MACA,IAAI8C,IAAI;MACR;MACA,IAAI3C,KAAK;;MAET;MACA,OAAOD,eAAe,EAAE,EAAE;QACxB,IACEpB,IAAI,CAACmB,MAAM,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,IAC1CpB,IAAI,CAACmB,MAAM,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC,CAACE,IAAI,KAAK/B,KAAK,CAACgC,SAAS,EACxD;UACA,IAAIyC,IAAI,EAAE;YACR3C,KAAK,GAAGrB,IAAI,CAACmB,MAAM,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC,CAACI,GAAG;YAC3C;UACF;UAEAwC,IAAI,GAAG,IAAI;QACb;MACF;MAEAvE,MAAM,CAAC4B,KAAK,EAAE,oCAAoC,CAAC;MAEnDI,cAAc,CAACvB,SAAS,CAAC;;MAEzB;MACAwB,KAAK,GAAGR,gBAAgB;MAExB,OAAOQ,KAAK,GAAG1B,IAAI,CAACmB,MAAM,CAACX,MAAM,EAAE;QACjCR,IAAI,CAACmB,MAAM,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,CAACF,GAAG,GAAGG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,KAAK,CAAC;QACpDK,KAAK,EAAE;MACT;;MAEA;MACAtC,MAAM,CACJY,IAAI,CAACmB,MAAM,EACXC,eAAe,GAAG,CAAC,EACnB,CAAC,EACDpB,IAAI,CAACmB,MAAM,CAACU,KAAK,CAACX,gBAAgB,CACpC,CAAC;;MAED;MACAlB,IAAI,CAACmB,MAAM,CAACX,MAAM,GAAGkB,KAAK;IAC5B;EACF;;EAEA;AACF;AACA;AACA;EACE,SAASD,cAAcA,CAACwC,IAAI,EAAE;IAC5B,IAAIvC,KAAK,GAAGzB,KAAK,CAACO,MAAM;;IAExB;IACA,OAAOkB,KAAK,EAAE,GAAGuC,IAAI,EAAE;MACrB,MAAMC,KAAK,GAAGjE,KAAK,CAACyB,KAAK,CAAC;MAC1B1B,IAAI,CAACU,cAAc,GAAGwD,KAAK,CAAC,CAAC,CAAC;MAC9BzE,MAAM,CACJyE,KAAK,CAAC,CAAC,CAAC,CAACT,IAAI,EACb,sDACF,CAAC;MACDS,KAAK,CAAC,CAAC,CAAC,CAACT,IAAI,CAACU,IAAI,CAACnE,IAAI,EAAED,OAAO,CAAC;IACnC;IAEAE,KAAK,CAACO,MAAM,GAAGyD,IAAI;EACrB;EAEA,SAAShD,SAASA,CAAA,EAAG;IACnBxB,MAAM,CACJO,IAAI,CAACU,cAAc,EACnB,2DACF,CAAC;IACDjB,MAAM,CAACU,SAAS,EAAE,oDAAoD,CAAC;IACvEA,SAAS,CAAC4D,KAAK,CAAC,CAAC1E,KAAK,CAAC0D,GAAG,CAAC,CAAC;IAC5B3C,UAAU,GAAGY,SAAS;IACtBb,SAAS,GAAGa,SAAS;IACrBhB,IAAI,CAACU,cAAc,CAACK,UAAU,GAAGC,SAAS;EAC5C;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASlB,iBAAiBA,CAACC,OAAO,EAAEP,EAAE,EAAE4E,GAAG,EAAE;EAC3C;EACA3E,MAAM,CACJ,IAAI,CAAC+C,MAAM,CAAC6B,UAAU,CAACC,OAAO,CAACC,IAAI,EACnC,yCACF,CAAC;EACD,OAAOrF,YAAY,CACjBa,OAAO,EACPA,OAAO,CAACa,OAAO,CAAC,IAAI,CAAC4B,MAAM,CAAC6B,UAAU,CAAC3E,QAAQ,EAAEF,EAAE,EAAE4E,GAAG,CAAC,EACzD7E,KAAK,CAACiF,UAAU,EAChB,IAAI,CAAChC,MAAM,CAAC6B,UAAU,CAACC,OAAO,CAACC,IAAI,CAACE,QAAQ,CAAC,cAAc,CAAC,GACxDzD,SAAS,GACT1B,SAAS,CAACoF,OAChB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}