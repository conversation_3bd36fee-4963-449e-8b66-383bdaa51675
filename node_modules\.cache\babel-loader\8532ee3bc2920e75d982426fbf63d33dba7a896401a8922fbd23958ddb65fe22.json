{"ast": null, "code": "class Queue {\n  constructor() {\n    this.order = [];\n    this.scheduled = new Set();\n  }\n  add(process) {\n    if (!this.scheduled.has(process)) {\n      this.scheduled.add(process);\n      this.order.push(process);\n      return true;\n    }\n  }\n  remove(process) {\n    const index = this.order.indexOf(process);\n    if (index !== -1) {\n      this.order.splice(index, 1);\n      this.scheduled.delete(process);\n    }\n  }\n  clear() {\n    this.order.length = 0;\n    this.scheduled.clear();\n  }\n}\nfunction createRenderStep(runNextFrame) {\n  /**\n   * We create and reuse two queues, one to queue jobs for the current frame\n   * and one for the next. We reuse to avoid triggering GC after x frames.\n   */\n  let thisFrame = new Queue();\n  let nextFrame = new Queue();\n  let numToRun = 0;\n  /**\n   * Track whether we're currently processing jobs in this step. This way\n   * we can decide whether to schedule new jobs for this frame or next.\n   */\n  let isProcessing = false;\n  let flushNextFrame = false;\n  /**\n   * A set of processes which were marked keepAlive when scheduled.\n   */\n  const toKeepAlive = new WeakSet();\n  const step = {\n    /**\n     * Schedule a process to run on the next frame.\n     */\n    schedule: (callback, keepAlive = false, immediate = false) => {\n      const addToCurrentFrame = immediate && isProcessing;\n      const queue = addToCurrentFrame ? thisFrame : nextFrame;\n      if (keepAlive) toKeepAlive.add(callback);\n      if (queue.add(callback) && addToCurrentFrame && isProcessing) {\n        // If we're adding it to the currently running queue, update its measured size\n        numToRun = thisFrame.order.length;\n      }\n      return callback;\n    },\n    /**\n     * Cancel the provided callback from running on the next frame.\n     */\n    cancel: callback => {\n      nextFrame.remove(callback);\n      toKeepAlive.delete(callback);\n    },\n    /**\n     * Execute all schedule callbacks.\n     */\n    process: frameData => {\n      /**\n       * If we're already processing we've probably been triggered by a flushSync\n       * inside an existing process. Instead of executing, mark flushNextFrame\n       * as true and ensure we flush the following frame at the end of this one.\n       */\n      if (isProcessing) {\n        flushNextFrame = true;\n        return;\n      }\n      isProcessing = true;\n      [thisFrame, nextFrame] = [nextFrame, thisFrame];\n      // Clear the next frame queue\n      nextFrame.clear();\n      // Execute this frame\n      numToRun = thisFrame.order.length;\n      if (numToRun) {\n        for (let i = 0; i < numToRun; i++) {\n          const callback = thisFrame.order[i];\n          callback(frameData);\n          if (toKeepAlive.has(callback)) {\n            step.schedule(callback);\n            runNextFrame();\n          }\n        }\n      }\n      isProcessing = false;\n      if (flushNextFrame) {\n        flushNextFrame = false;\n        step.process(frameData);\n      }\n    }\n  };\n  return step;\n}\nexport { createRenderStep };", "map": {"version": 3, "names": ["Queue", "constructor", "order", "scheduled", "Set", "add", "process", "has", "push", "remove", "index", "indexOf", "splice", "delete", "clear", "length", "createRenderStep", "runNextFrame", "thisFrame", "next<PERSON><PERSON><PERSON>", "numToRun", "isProcessing", "flushNextFrame", "toKeepAlive", "WeakSet", "step", "schedule", "callback", "keepAlive", "immediate", "addToCurrentFrame", "queue", "cancel", "frameData", "i"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/framer-motion/dist/es/frameloop/render-step.mjs"], "sourcesContent": ["class Queue {\n    constructor() {\n        this.order = [];\n        this.scheduled = new Set();\n    }\n    add(process) {\n        if (!this.scheduled.has(process)) {\n            this.scheduled.add(process);\n            this.order.push(process);\n            return true;\n        }\n    }\n    remove(process) {\n        const index = this.order.indexOf(process);\n        if (index !== -1) {\n            this.order.splice(index, 1);\n            this.scheduled.delete(process);\n        }\n    }\n    clear() {\n        this.order.length = 0;\n        this.scheduled.clear();\n    }\n}\nfunction createRenderStep(runNextFrame) {\n    /**\n     * We create and reuse two queues, one to queue jobs for the current frame\n     * and one for the next. We reuse to avoid triggering GC after x frames.\n     */\n    let thisFrame = new Queue();\n    let nextFrame = new Queue();\n    let numToRun = 0;\n    /**\n     * Track whether we're currently processing jobs in this step. This way\n     * we can decide whether to schedule new jobs for this frame or next.\n     */\n    let isProcessing = false;\n    let flushNextFrame = false;\n    /**\n     * A set of processes which were marked keepAlive when scheduled.\n     */\n    const toKeepAlive = new WeakSet();\n    const step = {\n        /**\n         * Schedule a process to run on the next frame.\n         */\n        schedule: (callback, keepAlive = false, immediate = false) => {\n            const addToCurrentFrame = immediate && isProcessing;\n            const queue = addToCurrentFrame ? thisFrame : nextFrame;\n            if (keepAlive)\n                toKeepAlive.add(callback);\n            if (queue.add(callback) && addToCurrentFrame && isProcessing) {\n                // If we're adding it to the currently running queue, update its measured size\n                numToRun = thisFrame.order.length;\n            }\n            return callback;\n        },\n        /**\n         * Cancel the provided callback from running on the next frame.\n         */\n        cancel: (callback) => {\n            nextFrame.remove(callback);\n            toKeepAlive.delete(callback);\n        },\n        /**\n         * Execute all schedule callbacks.\n         */\n        process: (frameData) => {\n            /**\n             * If we're already processing we've probably been triggered by a flushSync\n             * inside an existing process. Instead of executing, mark flushNextFrame\n             * as true and ensure we flush the following frame at the end of this one.\n             */\n            if (isProcessing) {\n                flushNextFrame = true;\n                return;\n            }\n            isProcessing = true;\n            [thisFrame, nextFrame] = [nextFrame, thisFrame];\n            // Clear the next frame queue\n            nextFrame.clear();\n            // Execute this frame\n            numToRun = thisFrame.order.length;\n            if (numToRun) {\n                for (let i = 0; i < numToRun; i++) {\n                    const callback = thisFrame.order[i];\n                    callback(frameData);\n                    if (toKeepAlive.has(callback)) {\n                        step.schedule(callback);\n                        runNextFrame();\n                    }\n                }\n            }\n            isProcessing = false;\n            if (flushNextFrame) {\n                flushNextFrame = false;\n                step.process(frameData);\n            }\n        },\n    };\n    return step;\n}\n\nexport { createRenderStep };\n"], "mappings": "AAAA,MAAMA,KAAK,CAAC;EACRC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC9B;EACAC,GAAGA,CAACC,OAAO,EAAE;IACT,IAAI,CAAC,IAAI,CAACH,SAAS,CAACI,GAAG,CAACD,OAAO,CAAC,EAAE;MAC9B,IAAI,CAACH,SAAS,CAACE,GAAG,CAACC,OAAO,CAAC;MAC3B,IAAI,CAACJ,KAAK,CAACM,IAAI,CAACF,OAAO,CAAC;MACxB,OAAO,IAAI;IACf;EACJ;EACAG,MAAMA,CAACH,OAAO,EAAE;IACZ,MAAMI,KAAK,GAAG,IAAI,CAACR,KAAK,CAACS,OAAO,CAACL,OAAO,CAAC;IACzC,IAAII,KAAK,KAAK,CAAC,CAAC,EAAE;MACd,IAAI,CAACR,KAAK,CAACU,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC3B,IAAI,CAACP,SAAS,CAACU,MAAM,CAACP,OAAO,CAAC;IAClC;EACJ;EACAQ,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACZ,KAAK,CAACa,MAAM,GAAG,CAAC;IACrB,IAAI,CAACZ,SAAS,CAACW,KAAK,CAAC,CAAC;EAC1B;AACJ;AACA,SAASE,gBAAgBA,CAACC,YAAY,EAAE;EACpC;AACJ;AACA;AACA;EACI,IAAIC,SAAS,GAAG,IAAIlB,KAAK,CAAC,CAAC;EAC3B,IAAImB,SAAS,GAAG,IAAInB,KAAK,CAAC,CAAC;EAC3B,IAAIoB,QAAQ,GAAG,CAAC;EAChB;AACJ;AACA;AACA;EACI,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,cAAc,GAAG,KAAK;EAC1B;AACJ;AACA;EACI,MAAMC,WAAW,GAAG,IAAIC,OAAO,CAAC,CAAC;EACjC,MAAMC,IAAI,GAAG;IACT;AACR;AACA;IACQC,QAAQ,EAAEA,CAACC,QAAQ,EAAEC,SAAS,GAAG,KAAK,EAAEC,SAAS,GAAG,KAAK,KAAK;MAC1D,MAAMC,iBAAiB,GAAGD,SAAS,IAAIR,YAAY;MACnD,MAAMU,KAAK,GAAGD,iBAAiB,GAAGZ,SAAS,GAAGC,SAAS;MACvD,IAAIS,SAAS,EACTL,WAAW,CAAClB,GAAG,CAACsB,QAAQ,CAAC;MAC7B,IAAII,KAAK,CAAC1B,GAAG,CAACsB,QAAQ,CAAC,IAAIG,iBAAiB,IAAIT,YAAY,EAAE;QAC1D;QACAD,QAAQ,GAAGF,SAAS,CAAChB,KAAK,CAACa,MAAM;MACrC;MACA,OAAOY,QAAQ;IACnB,CAAC;IACD;AACR;AACA;IACQK,MAAM,EAAGL,QAAQ,IAAK;MAClBR,SAAS,CAACV,MAAM,CAACkB,QAAQ,CAAC;MAC1BJ,WAAW,CAACV,MAAM,CAACc,QAAQ,CAAC;IAChC,CAAC;IACD;AACR;AACA;IACQrB,OAAO,EAAG2B,SAAS,IAAK;MACpB;AACZ;AACA;AACA;AACA;MACY,IAAIZ,YAAY,EAAE;QACdC,cAAc,GAAG,IAAI;QACrB;MACJ;MACAD,YAAY,GAAG,IAAI;MACnB,CAACH,SAAS,EAAEC,SAAS,CAAC,GAAG,CAACA,SAAS,EAAED,SAAS,CAAC;MAC/C;MACAC,SAAS,CAACL,KAAK,CAAC,CAAC;MACjB;MACAM,QAAQ,GAAGF,SAAS,CAAChB,KAAK,CAACa,MAAM;MACjC,IAAIK,QAAQ,EAAE;QACV,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,QAAQ,EAAEc,CAAC,EAAE,EAAE;UAC/B,MAAMP,QAAQ,GAAGT,SAAS,CAAChB,KAAK,CAACgC,CAAC,CAAC;UACnCP,QAAQ,CAACM,SAAS,CAAC;UACnB,IAAIV,WAAW,CAAChB,GAAG,CAACoB,QAAQ,CAAC,EAAE;YAC3BF,IAAI,CAACC,QAAQ,CAACC,QAAQ,CAAC;YACvBV,YAAY,CAAC,CAAC;UAClB;QACJ;MACJ;MACAI,YAAY,GAAG,KAAK;MACpB,IAAIC,cAAc,EAAE;QAChBA,cAAc,GAAG,KAAK;QACtBG,IAAI,CAACnB,OAAO,CAAC2B,SAAS,CAAC;MAC3B;IACJ;EACJ,CAAC;EACD,OAAOR,IAAI;AACf;AAEA,SAAST,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}