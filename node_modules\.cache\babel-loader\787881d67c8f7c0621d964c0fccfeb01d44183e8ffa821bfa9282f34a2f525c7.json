{"ast": null, "code": "import * as React from 'react';\nimport { useState, useRef, useEffect } from 'react';\nimport { LazyContext } from '../../context/LazyContext.mjs';\nimport { loadFeatures } from '../../motion/features/load-features.mjs';\n\n/**\n * Used in conjunction with the `m` component to reduce bundle size.\n *\n * `m` is a version of the `motion` component that only loads functionality\n * critical for the initial render.\n *\n * `LazyMotion` can then be used to either synchronously or asynchronously\n * load animation and gesture support.\n *\n * ```jsx\n * // Synchronous loading\n * import { LazyMotion, m, domAnimation } from \"framer-motion\"\n *\n * function App() {\n *   return (\n *     <LazyMotion features={domAnimation}>\n *       <m.div animate={{ scale: 2 }} />\n *     </LazyMotion>\n *   )\n * }\n *\n * // Asynchronous loading\n * import { LazyMotion, m } from \"framer-motion\"\n *\n * function App() {\n *   return (\n *     <LazyMotion features={() => import('./path/to/domAnimation')}>\n *       <m.div animate={{ scale: 2 }} />\n *     </LazyMotion>\n *   )\n * }\n * ```\n *\n * @public\n */\nfunction LazyMotion({\n  children,\n  features,\n  strict = false\n}) {\n  const [, setIsLoaded] = useState(!isLazyBundle(features));\n  const loadedRenderer = useRef(undefined);\n  /**\n   * If this is a synchronous load, load features immediately\n   */\n  if (!isLazyBundle(features)) {\n    const {\n      renderer,\n      ...loadedFeatures\n    } = features;\n    loadedRenderer.current = renderer;\n    loadFeatures(loadedFeatures);\n  }\n  useEffect(() => {\n    if (isLazyBundle(features)) {\n      features().then(({\n        renderer,\n        ...loadedFeatures\n      }) => {\n        loadFeatures(loadedFeatures);\n        loadedRenderer.current = renderer;\n        setIsLoaded(true);\n      });\n    }\n  }, []);\n  return React.createElement(LazyContext.Provider, {\n    value: {\n      renderer: loadedRenderer.current,\n      strict\n    }\n  }, children);\n}\nfunction isLazyBundle(features) {\n  return typeof features === \"function\";\n}\nexport { LazyMotion };", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "LazyContext", "loadFeatures", "LazyMotion", "children", "features", "strict", "setIsLoaded", "isLazyBundle", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "renderer", "loadedFeatures", "current", "then", "createElement", "Provider", "value"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/framer-motion/dist/es/components/LazyMotion/index.mjs"], "sourcesContent": ["import * as React from 'react';\nimport { useState, useRef, useEffect } from 'react';\nimport { LazyContext } from '../../context/LazyContext.mjs';\nimport { loadFeatures } from '../../motion/features/load-features.mjs';\n\n/**\n * Used in conjunction with the `m` component to reduce bundle size.\n *\n * `m` is a version of the `motion` component that only loads functionality\n * critical for the initial render.\n *\n * `LazyMotion` can then be used to either synchronously or asynchronously\n * load animation and gesture support.\n *\n * ```jsx\n * // Synchronous loading\n * import { LazyMotion, m, domAnimation } from \"framer-motion\"\n *\n * function App() {\n *   return (\n *     <LazyMotion features={domAnimation}>\n *       <m.div animate={{ scale: 2 }} />\n *     </LazyMotion>\n *   )\n * }\n *\n * // Asynchronous loading\n * import { LazyMotion, m } from \"framer-motion\"\n *\n * function App() {\n *   return (\n *     <LazyMotion features={() => import('./path/to/domAnimation')}>\n *       <m.div animate={{ scale: 2 }} />\n *     </LazyMotion>\n *   )\n * }\n * ```\n *\n * @public\n */\nfunction LazyMotion({ children, features, strict = false }) {\n    const [, setIsLoaded] = useState(!isLazyBundle(features));\n    const loadedRenderer = useRef(undefined);\n    /**\n     * If this is a synchronous load, load features immediately\n     */\n    if (!isLazyBundle(features)) {\n        const { renderer, ...loadedFeatures } = features;\n        loadedRenderer.current = renderer;\n        loadFeatures(loadedFeatures);\n    }\n    useEffect(() => {\n        if (isLazyBundle(features)) {\n            features().then(({ renderer, ...loadedFeatures }) => {\n                loadFeatures(loadedFeatures);\n                loadedRenderer.current = renderer;\n                setIsLoaded(true);\n            });\n        }\n    }, []);\n    return (React.createElement(LazyContext.Provider, { value: { renderer: loadedRenderer.current, strict } }, children));\n}\nfunction isLazyBundle(features) {\n    return typeof features === \"function\";\n}\n\nexport { LazyMotion };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACnD,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,YAAY,QAAQ,yCAAyC;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAAC;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,MAAM,GAAG;AAAM,CAAC,EAAE;EACxD,MAAM,GAAGC,WAAW,CAAC,GAAGT,QAAQ,CAAC,CAACU,YAAY,CAACH,QAAQ,CAAC,CAAC;EACzD,MAAMI,cAAc,GAAGV,MAAM,CAACW,SAAS,CAAC;EACxC;AACJ;AACA;EACI,IAAI,CAACF,YAAY,CAACH,QAAQ,CAAC,EAAE;IACzB,MAAM;MAAEM,QAAQ;MAAE,GAAGC;IAAe,CAAC,GAAGP,QAAQ;IAChDI,cAAc,CAACI,OAAO,GAAGF,QAAQ;IACjCT,YAAY,CAACU,cAAc,CAAC;EAChC;EACAZ,SAAS,CAAC,MAAM;IACZ,IAAIQ,YAAY,CAACH,QAAQ,CAAC,EAAE;MACxBA,QAAQ,CAAC,CAAC,CAACS,IAAI,CAAC,CAAC;QAAEH,QAAQ;QAAE,GAAGC;MAAe,CAAC,KAAK;QACjDV,YAAY,CAACU,cAAc,CAAC;QAC5BH,cAAc,CAACI,OAAO,GAAGF,QAAQ;QACjCJ,WAAW,CAAC,IAAI,CAAC;MACrB,CAAC,CAAC;IACN;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,OAAQV,KAAK,CAACkB,aAAa,CAACd,WAAW,CAACe,QAAQ,EAAE;IAAEC,KAAK,EAAE;MAAEN,QAAQ,EAAEF,cAAc,CAACI,OAAO;MAAEP;IAAO;EAAE,CAAC,EAAEF,QAAQ,CAAC;AACxH;AACA,SAASI,YAAYA,CAACH,QAAQ,EAAE;EAC5B,OAAO,OAAOA,QAAQ,KAAK,UAAU;AACzC;AAEA,SAASF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}