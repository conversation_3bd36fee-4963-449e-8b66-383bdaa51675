{"ast": null, "code": "function Diff() {}\nDiff.prototype = {\n  diff: function diff(oldString, newString) {\n    var _options$timeout;\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var callback = options.callback;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    this.options = options;\n    var self = this;\n    function done(value) {\n      if (callback) {\n        setTimeout(function () {\n          callback(undefined, value);\n        }, 0);\n        return true;\n      } else {\n        return value;\n      }\n    } // Allow subclasses to massage the input prior to running\n\n    oldString = this.castInput(oldString);\n    newString = this.castInput(newString);\n    oldString = this.removeEmpty(this.tokenize(oldString));\n    newString = this.removeEmpty(this.tokenize(newString));\n    var newLen = newString.length,\n      oldLen = oldString.length;\n    var editLength = 1;\n    var maxEditLength = newLen + oldLen;\n    if (options.maxEditLength) {\n      maxEditLength = Math.min(maxEditLength, options.maxEditLength);\n    }\n    var maxExecutionTime = (_options$timeout = options.timeout) !== null && _options$timeout !== void 0 ? _options$timeout : Infinity;\n    var abortAfterTimestamp = Date.now() + maxExecutionTime;\n    var bestPath = [{\n      oldPos: -1,\n      lastComponent: undefined\n    }]; // Seed editLength = 0, i.e. the content starts with the same values\n\n    var newPos = this.extractCommon(bestPath[0], newString, oldString, 0);\n    if (bestPath[0].oldPos + 1 >= oldLen && newPos + 1 >= newLen) {\n      // Identity per the equality and tokenizer\n      return done([{\n        value: this.join(newString),\n        count: newString.length\n      }]);\n    } // Once we hit the right edge of the edit graph on some diagonal k, we can\n    // definitely reach the end of the edit graph in no more than k edits, so\n    // there's no point in considering any moves to diagonal k+1 any more (from\n    // which we're guaranteed to need at least k+1 more edits).\n    // Similarly, once we've reached the bottom of the edit graph, there's no\n    // point considering moves to lower diagonals.\n    // We record this fact by setting minDiagonalToConsider and\n    // maxDiagonalToConsider to some finite value once we've hit the edge of\n    // the edit graph.\n    // This optimization is not faithful to the original algorithm presented in\n    // Myers's paper, which instead pointlessly extends D-paths off the end of\n    // the edit graph - see page 7 of Myers's paper which notes this point\n    // explicitly and illustrates it with a diagram. This has major performance\n    // implications for some common scenarios. For instance, to compute a diff\n    // where the new text simply appends d characters on the end of the\n    // original text of length n, the true Myers algorithm will take O(n+d^2)\n    // time while this optimization needs only O(n+d) time.\n\n    var minDiagonalToConsider = -Infinity,\n      maxDiagonalToConsider = Infinity; // Main worker method. checks all permutations of a given edit length for acceptance.\n\n    function execEditLength() {\n      for (var diagonalPath = Math.max(minDiagonalToConsider, -editLength); diagonalPath <= Math.min(maxDiagonalToConsider, editLength); diagonalPath += 2) {\n        var basePath = void 0;\n        var removePath = bestPath[diagonalPath - 1],\n          addPath = bestPath[diagonalPath + 1];\n        if (removePath) {\n          // No one else is going to attempt to use this value, clear it\n          bestPath[diagonalPath - 1] = undefined;\n        }\n        var canAdd = false;\n        if (addPath) {\n          // what newPos will be after we do an insertion:\n          var addPathNewPos = addPath.oldPos - diagonalPath;\n          canAdd = addPath && 0 <= addPathNewPos && addPathNewPos < newLen;\n        }\n        var canRemove = removePath && removePath.oldPos + 1 < oldLen;\n        if (!canAdd && !canRemove) {\n          // If this path is a terminal then prune\n          bestPath[diagonalPath] = undefined;\n          continue;\n        } // Select the diagonal that we want to branch from. We select the prior\n        // path whose position in the old string is the farthest from the origin\n        // and does not pass the bounds of the diff graph\n        // TODO: Remove the `+ 1` here to make behavior match Myers algorithm\n        //       and prefer to order removals before insertions.\n\n        if (!canRemove || canAdd && removePath.oldPos + 1 < addPath.oldPos) {\n          basePath = self.addToPath(addPath, true, undefined, 0);\n        } else {\n          basePath = self.addToPath(removePath, undefined, true, 1);\n        }\n        newPos = self.extractCommon(basePath, newString, oldString, diagonalPath);\n        if (basePath.oldPos + 1 >= oldLen && newPos + 1 >= newLen) {\n          // If we have hit the end of both strings, then we are done\n          return done(buildValues(self, basePath.lastComponent, newString, oldString, self.useLongestToken));\n        } else {\n          bestPath[diagonalPath] = basePath;\n          if (basePath.oldPos + 1 >= oldLen) {\n            maxDiagonalToConsider = Math.min(maxDiagonalToConsider, diagonalPath - 1);\n          }\n          if (newPos + 1 >= newLen) {\n            minDiagonalToConsider = Math.max(minDiagonalToConsider, diagonalPath + 1);\n          }\n        }\n      }\n      editLength++;\n    } // Performs the length of edit iteration. Is a bit fugly as this has to support the\n    // sync and async mode which is never fun. Loops over execEditLength until a value\n    // is produced, or until the edit length exceeds options.maxEditLength (if given),\n    // in which case it will return undefined.\n\n    if (callback) {\n      (function exec() {\n        setTimeout(function () {\n          if (editLength > maxEditLength || Date.now() > abortAfterTimestamp) {\n            return callback();\n          }\n          if (!execEditLength()) {\n            exec();\n          }\n        }, 0);\n      })();\n    } else {\n      while (editLength <= maxEditLength && Date.now() <= abortAfterTimestamp) {\n        var ret = execEditLength();\n        if (ret) {\n          return ret;\n        }\n      }\n    }\n  },\n  addToPath: function addToPath(path, added, removed, oldPosInc) {\n    var last = path.lastComponent;\n    if (last && last.added === added && last.removed === removed) {\n      return {\n        oldPos: path.oldPos + oldPosInc,\n        lastComponent: {\n          count: last.count + 1,\n          added: added,\n          removed: removed,\n          previousComponent: last.previousComponent\n        }\n      };\n    } else {\n      return {\n        oldPos: path.oldPos + oldPosInc,\n        lastComponent: {\n          count: 1,\n          added: added,\n          removed: removed,\n          previousComponent: last\n        }\n      };\n    }\n  },\n  extractCommon: function extractCommon(basePath, newString, oldString, diagonalPath) {\n    var newLen = newString.length,\n      oldLen = oldString.length,\n      oldPos = basePath.oldPos,\n      newPos = oldPos - diagonalPath,\n      commonCount = 0;\n    while (newPos + 1 < newLen && oldPos + 1 < oldLen && this.equals(newString[newPos + 1], oldString[oldPos + 1])) {\n      newPos++;\n      oldPos++;\n      commonCount++;\n    }\n    if (commonCount) {\n      basePath.lastComponent = {\n        count: commonCount,\n        previousComponent: basePath.lastComponent\n      };\n    }\n    basePath.oldPos = oldPos;\n    return newPos;\n  },\n  equals: function equals(left, right) {\n    if (this.options.comparator) {\n      return this.options.comparator(left, right);\n    } else {\n      return left === right || this.options.ignoreCase && left.toLowerCase() === right.toLowerCase();\n    }\n  },\n  removeEmpty: function removeEmpty(array) {\n    var ret = [];\n    for (var i = 0; i < array.length; i++) {\n      if (array[i]) {\n        ret.push(array[i]);\n      }\n    }\n    return ret;\n  },\n  castInput: function castInput(value) {\n    return value;\n  },\n  tokenize: function tokenize(value) {\n    return value.split('');\n  },\n  join: function join(chars) {\n    return chars.join('');\n  }\n};\nfunction buildValues(diff, lastComponent, newString, oldString, useLongestToken) {\n  // First we convert our linked list of components in reverse order to an\n  // array in the right order:\n  var components = [];\n  var nextComponent;\n  while (lastComponent) {\n    components.push(lastComponent);\n    nextComponent = lastComponent.previousComponent;\n    delete lastComponent.previousComponent;\n    lastComponent = nextComponent;\n  }\n  components.reverse();\n  var componentPos = 0,\n    componentLen = components.length,\n    newPos = 0,\n    oldPos = 0;\n  for (; componentPos < componentLen; componentPos++) {\n    var component = components[componentPos];\n    if (!component.removed) {\n      if (!component.added && useLongestToken) {\n        var value = newString.slice(newPos, newPos + component.count);\n        value = value.map(function (value, i) {\n          var oldValue = oldString[oldPos + i];\n          return oldValue.length > value.length ? oldValue : value;\n        });\n        component.value = diff.join(value);\n      } else {\n        component.value = diff.join(newString.slice(newPos, newPos + component.count));\n      }\n      newPos += component.count; // Common case\n\n      if (!component.added) {\n        oldPos += component.count;\n      }\n    } else {\n      component.value = diff.join(oldString.slice(oldPos, oldPos + component.count));\n      oldPos += component.count; // Reverse add and remove so removes are output first to match common convention\n      // The diffing algorithm is tied to add then remove output and this is the simplest\n      // route to get the desired output with minimal overhead.\n\n      if (componentPos && components[componentPos - 1].added) {\n        var tmp = components[componentPos - 1];\n        components[componentPos - 1] = components[componentPos];\n        components[componentPos] = tmp;\n      }\n    }\n  } // Special case handle for when one terminal is ignored (i.e. whitespace).\n  // For this case we merge the terminal into the prior string and drop the change.\n  // This is only available for string mode.\n\n  var finalComponent = components[componentLen - 1];\n  if (componentLen > 1 && typeof finalComponent.value === 'string' && (finalComponent.added || finalComponent.removed) && diff.equals('', finalComponent.value)) {\n    components[componentLen - 2].value += finalComponent.value;\n    components.pop();\n  }\n  return components;\n}\nvar characterDiff = new Diff();\nfunction diffChars(oldStr, newStr, options) {\n  return characterDiff.diff(oldStr, newStr, options);\n}\nfunction generateOptions(options, defaults) {\n  if (typeof options === 'function') {\n    defaults.callback = options;\n  } else if (options) {\n    for (var name in options) {\n      /* istanbul ignore else */\n      if (options.hasOwnProperty(name)) {\n        defaults[name] = options[name];\n      }\n    }\n  }\n  return defaults;\n}\n\n//\n// Ranges and exceptions:\n// Latin-1 Supplement, 0080–00FF\n//  - U+00D7  × Multiplication sign\n//  - U+00F7  ÷ Division sign\n// Latin Extended-A, 0100–017F\n// Latin Extended-B, 0180–024F\n// IPA Extensions, 0250–02AF\n// Spacing Modifier Letters, 02B0–02FF\n//  - U+02C7  ˇ &#711;  Caron\n//  - U+02D8  ˘ &#728;  Breve\n//  - U+02D9  ˙ &#729;  Dot Above\n//  - U+02DA  ˚ &#730;  Ring Above\n//  - U+02DB  ˛ &#731;  Ogonek\n//  - U+02DC  ˜ &#732;  Small Tilde\n//  - U+02DD  ˝ &#733;  Double Acute Accent\n// Latin Extended Additional, 1E00–1EFF\n\nvar extendedWordChars = /^[A-Za-z\\xC0-\\u02C6\\u02C8-\\u02D7\\u02DE-\\u02FF\\u1E00-\\u1EFF]+$/;\nvar reWhitespace = /\\S/;\nvar wordDiff = new Diff();\nwordDiff.equals = function (left, right) {\n  if (this.options.ignoreCase) {\n    left = left.toLowerCase();\n    right = right.toLowerCase();\n  }\n  return left === right || this.options.ignoreWhitespace && !reWhitespace.test(left) && !reWhitespace.test(right);\n};\nwordDiff.tokenize = function (value) {\n  // All whitespace symbols except newline group into one token, each newline - in separate token\n  var tokens = value.split(/([^\\S\\r\\n]+|[()[\\]{}'\"\\r\\n]|\\b)/); // Join the boundary splits that we do not consider to be boundaries. This is primarily the extended Latin character set.\n\n  for (var i = 0; i < tokens.length - 1; i++) {\n    // If we have an empty string in the next field and we have only word chars before and after, merge\n    if (!tokens[i + 1] && tokens[i + 2] && extendedWordChars.test(tokens[i]) && extendedWordChars.test(tokens[i + 2])) {\n      tokens[i] += tokens[i + 2];\n      tokens.splice(i + 1, 2);\n      i--;\n    }\n  }\n  return tokens;\n};\nfunction diffWords(oldStr, newStr, options) {\n  options = generateOptions(options, {\n    ignoreWhitespace: true\n  });\n  return wordDiff.diff(oldStr, newStr, options);\n}\nfunction diffWordsWithSpace(oldStr, newStr, options) {\n  return wordDiff.diff(oldStr, newStr, options);\n}\nvar lineDiff = new Diff();\nlineDiff.tokenize = function (value) {\n  if (this.options.stripTrailingCr) {\n    // remove one \\r before \\n to match GNU diff's --strip-trailing-cr behavior\n    value = value.replace(/\\r\\n/g, '\\n');\n  }\n  var retLines = [],\n    linesAndNewlines = value.split(/(\\n|\\r\\n)/); // Ignore the final empty token that occurs if the string ends with a new line\n\n  if (!linesAndNewlines[linesAndNewlines.length - 1]) {\n    linesAndNewlines.pop();\n  } // Merge the content and line separators into single tokens\n\n  for (var i = 0; i < linesAndNewlines.length; i++) {\n    var line = linesAndNewlines[i];\n    if (i % 2 && !this.options.newlineIsToken) {\n      retLines[retLines.length - 1] += line;\n    } else {\n      if (this.options.ignoreWhitespace) {\n        line = line.trim();\n      }\n      retLines.push(line);\n    }\n  }\n  return retLines;\n};\nfunction diffLines(oldStr, newStr, callback) {\n  return lineDiff.diff(oldStr, newStr, callback);\n}\nfunction diffTrimmedLines(oldStr, newStr, callback) {\n  var options = generateOptions(callback, {\n    ignoreWhitespace: true\n  });\n  return lineDiff.diff(oldStr, newStr, options);\n}\nvar sentenceDiff = new Diff();\nsentenceDiff.tokenize = function (value) {\n  return value.split(/(\\S.+?[.!?])(?=\\s+|$)/);\n};\nfunction diffSentences(oldStr, newStr, callback) {\n  return sentenceDiff.diff(oldStr, newStr, callback);\n}\nvar cssDiff = new Diff();\ncssDiff.tokenize = function (value) {\n  return value.split(/([{}:;,]|\\s+)/);\n};\nfunction diffCss(oldStr, newStr, callback) {\n  return cssDiff.diff(oldStr, newStr, callback);\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nvar objectPrototypeToString = Object.prototype.toString;\nvar jsonDiff = new Diff(); // Discriminate between two lines of pretty-printed, serialized JSON where one of them has a\n// dangling comma and the other doesn't. Turns out including the dangling comma yields the nicest output:\n\njsonDiff.useLongestToken = true;\njsonDiff.tokenize = lineDiff.tokenize;\njsonDiff.castInput = function (value) {\n  var _this$options = this.options,\n    undefinedReplacement = _this$options.undefinedReplacement,\n    _this$options$stringi = _this$options.stringifyReplacer,\n    stringifyReplacer = _this$options$stringi === void 0 ? function (k, v) {\n      return typeof v === 'undefined' ? undefinedReplacement : v;\n    } : _this$options$stringi;\n  return typeof value === 'string' ? value : JSON.stringify(canonicalize(value, null, null, stringifyReplacer), stringifyReplacer, '  ');\n};\njsonDiff.equals = function (left, right) {\n  return Diff.prototype.equals.call(jsonDiff, left.replace(/,([\\r\\n])/g, '$1'), right.replace(/,([\\r\\n])/g, '$1'));\n};\nfunction diffJson(oldObj, newObj, options) {\n  return jsonDiff.diff(oldObj, newObj, options);\n} // This function handles the presence of circular references by bailing out when encountering an\n// object that is already on the \"stack\" of items being processed. Accepts an optional replacer\n\nfunction canonicalize(obj, stack, replacementStack, replacer, key) {\n  stack = stack || [];\n  replacementStack = replacementStack || [];\n  if (replacer) {\n    obj = replacer(key, obj);\n  }\n  var i;\n  for (i = 0; i < stack.length; i += 1) {\n    if (stack[i] === obj) {\n      return replacementStack[i];\n    }\n  }\n  var canonicalizedObj;\n  if ('[object Array]' === objectPrototypeToString.call(obj)) {\n    stack.push(obj);\n    canonicalizedObj = new Array(obj.length);\n    replacementStack.push(canonicalizedObj);\n    for (i = 0; i < obj.length; i += 1) {\n      canonicalizedObj[i] = canonicalize(obj[i], stack, replacementStack, replacer, key);\n    }\n    stack.pop();\n    replacementStack.pop();\n    return canonicalizedObj;\n  }\n  if (obj && obj.toJSON) {\n    obj = obj.toJSON();\n  }\n  if (_typeof(obj) === 'object' && obj !== null) {\n    stack.push(obj);\n    canonicalizedObj = {};\n    replacementStack.push(canonicalizedObj);\n    var sortedKeys = [],\n      _key;\n    for (_key in obj) {\n      /* istanbul ignore else */\n      if (obj.hasOwnProperty(_key)) {\n        sortedKeys.push(_key);\n      }\n    }\n    sortedKeys.sort();\n    for (i = 0; i < sortedKeys.length; i += 1) {\n      _key = sortedKeys[i];\n      canonicalizedObj[_key] = canonicalize(obj[_key], stack, replacementStack, replacer, _key);\n    }\n    stack.pop();\n    replacementStack.pop();\n  } else {\n    canonicalizedObj = obj;\n  }\n  return canonicalizedObj;\n}\nvar arrayDiff = new Diff();\narrayDiff.tokenize = function (value) {\n  return value.slice();\n};\narrayDiff.join = arrayDiff.removeEmpty = function (value) {\n  return value;\n};\nfunction diffArrays(oldArr, newArr, callback) {\n  return arrayDiff.diff(oldArr, newArr, callback);\n}\nfunction parsePatch(uniDiff) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var diffstr = uniDiff.split(/\\r\\n|[\\n\\v\\f\\r\\x85]/),\n    delimiters = uniDiff.match(/\\r\\n|[\\n\\v\\f\\r\\x85]/g) || [],\n    list = [],\n    i = 0;\n  function parseIndex() {\n    var index = {};\n    list.push(index); // Parse diff metadata\n\n    while (i < diffstr.length) {\n      var line = diffstr[i]; // File header found, end parsing diff metadata\n\n      if (/^(\\-\\-\\-|\\+\\+\\+|@@)\\s/.test(line)) {\n        break;\n      } // Diff index\n\n      var header = /^(?:Index:|diff(?: -r \\w+)+)\\s+(.+?)\\s*$/.exec(line);\n      if (header) {\n        index.index = header[1];\n      }\n      i++;\n    } // Parse file headers if they are defined. Unified diff requires them, but\n    // there's no technical issues to have an isolated hunk without file header\n\n    parseFileHeader(index);\n    parseFileHeader(index); // Parse hunks\n\n    index.hunks = [];\n    while (i < diffstr.length) {\n      var _line = diffstr[i];\n      if (/^(Index:|diff|\\-\\-\\-|\\+\\+\\+)\\s/.test(_line)) {\n        break;\n      } else if (/^@@/.test(_line)) {\n        index.hunks.push(parseHunk());\n      } else if (_line && options.strict) {\n        // Ignore unexpected content unless in strict mode\n        throw new Error('Unknown line ' + (i + 1) + ' ' + JSON.stringify(_line));\n      } else {\n        i++;\n      }\n    }\n  } // Parses the --- and +++ headers, if none are found, no lines\n  // are consumed.\n\n  function parseFileHeader(index) {\n    var fileHeader = /^(---|\\+\\+\\+)\\s+(.*)$/.exec(diffstr[i]);\n    if (fileHeader) {\n      var keyPrefix = fileHeader[1] === '---' ? 'old' : 'new';\n      var data = fileHeader[2].split('\\t', 2);\n      var fileName = data[0].replace(/\\\\\\\\/g, '\\\\');\n      if (/^\".*\"$/.test(fileName)) {\n        fileName = fileName.substr(1, fileName.length - 2);\n      }\n      index[keyPrefix + 'FileName'] = fileName;\n      index[keyPrefix + 'Header'] = (data[1] || '').trim();\n      i++;\n    }\n  } // Parses a hunk\n  // This assumes that we are at the start of a hunk.\n\n  function parseHunk() {\n    var chunkHeaderIndex = i,\n      chunkHeaderLine = diffstr[i++],\n      chunkHeader = chunkHeaderLine.split(/@@ -(\\d+)(?:,(\\d+))? \\+(\\d+)(?:,(\\d+))? @@/);\n    var hunk = {\n      oldStart: +chunkHeader[1],\n      oldLines: typeof chunkHeader[2] === 'undefined' ? 1 : +chunkHeader[2],\n      newStart: +chunkHeader[3],\n      newLines: typeof chunkHeader[4] === 'undefined' ? 1 : +chunkHeader[4],\n      lines: [],\n      linedelimiters: []\n    }; // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n\n    if (hunk.oldLines === 0) {\n      hunk.oldStart += 1;\n    }\n    if (hunk.newLines === 0) {\n      hunk.newStart += 1;\n    }\n    var addCount = 0,\n      removeCount = 0;\n    for (; i < diffstr.length; i++) {\n      // Lines starting with '---' could be mistaken for the \"remove line\" operation\n      // But they could be the header for the next file. Therefore prune such cases out.\n      if (diffstr[i].indexOf('--- ') === 0 && i + 2 < diffstr.length && diffstr[i + 1].indexOf('+++ ') === 0 && diffstr[i + 2].indexOf('@@') === 0) {\n        break;\n      }\n      var operation = diffstr[i].length == 0 && i != diffstr.length - 1 ? ' ' : diffstr[i][0];\n      if (operation === '+' || operation === '-' || operation === ' ' || operation === '\\\\') {\n        hunk.lines.push(diffstr[i]);\n        hunk.linedelimiters.push(delimiters[i] || '\\n');\n        if (operation === '+') {\n          addCount++;\n        } else if (operation === '-') {\n          removeCount++;\n        } else if (operation === ' ') {\n          addCount++;\n          removeCount++;\n        }\n      } else {\n        break;\n      }\n    } // Handle the empty block count case\n\n    if (!addCount && hunk.newLines === 1) {\n      hunk.newLines = 0;\n    }\n    if (!removeCount && hunk.oldLines === 1) {\n      hunk.oldLines = 0;\n    } // Perform optional sanity checking\n\n    if (options.strict) {\n      if (addCount !== hunk.newLines) {\n        throw new Error('Added line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n      }\n      if (removeCount !== hunk.oldLines) {\n        throw new Error('Removed line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n      }\n    }\n    return hunk;\n  }\n  while (i < diffstr.length) {\n    parseIndex();\n  }\n  return list;\n}\n\n// Iterator that traverses in the range of [min, max], stepping\n// by distance from a given start position. I.e. for [0, 4], with\n// start of 2, this will iterate 2, 3, 1, 4, 0.\nfunction distanceIterator(start, minLine, maxLine) {\n  var wantForward = true,\n    backwardExhausted = false,\n    forwardExhausted = false,\n    localOffset = 1;\n  return function iterator() {\n    if (wantForward && !forwardExhausted) {\n      if (backwardExhausted) {\n        localOffset++;\n      } else {\n        wantForward = false;\n      } // Check if trying to fit beyond text length, and if not, check it fits\n      // after offset location (or desired location on first iteration)\n\n      if (start + localOffset <= maxLine) {\n        return localOffset;\n      }\n      forwardExhausted = true;\n    }\n    if (!backwardExhausted) {\n      if (!forwardExhausted) {\n        wantForward = true;\n      } // Check if trying to fit before text beginning, and if not, check it fits\n      // before offset location\n\n      if (minLine <= start - localOffset) {\n        return -localOffset++;\n      }\n      backwardExhausted = true;\n      return iterator();\n    } // We tried to fit hunk before text beginning and beyond text length, then\n    // hunk can't fit on the text. Return undefined\n  };\n}\nfunction applyPatch(source, uniDiff) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n  if (Array.isArray(uniDiff)) {\n    if (uniDiff.length > 1) {\n      throw new Error('applyPatch only works with a single input.');\n    }\n    uniDiff = uniDiff[0];\n  } // Apply the diff to the input\n\n  var lines = source.split(/\\r\\n|[\\n\\v\\f\\r\\x85]/),\n    delimiters = source.match(/\\r\\n|[\\n\\v\\f\\r\\x85]/g) || [],\n    hunks = uniDiff.hunks,\n    compareLine = options.compareLine || function (lineNumber, line, operation, patchContent) {\n      return line === patchContent;\n    },\n    errorCount = 0,\n    fuzzFactor = options.fuzzFactor || 0,\n    minLine = 0,\n    offset = 0,\n    removeEOFNL,\n    addEOFNL;\n  /**\n   * Checks if the hunk exactly fits on the provided location\n   */\n\n  function hunkFits(hunk, toPos) {\n    for (var j = 0; j < hunk.lines.length; j++) {\n      var line = hunk.lines[j],\n        operation = line.length > 0 ? line[0] : ' ',\n        content = line.length > 0 ? line.substr(1) : line;\n      if (operation === ' ' || operation === '-') {\n        // Context sanity check\n        if (!compareLine(toPos + 1, lines[toPos], operation, content)) {\n          errorCount++;\n          if (errorCount > fuzzFactor) {\n            return false;\n          }\n        }\n        toPos++;\n      }\n    }\n    return true;\n  } // Search best fit offsets for each hunk based on the previous ones\n\n  for (var i = 0; i < hunks.length; i++) {\n    var hunk = hunks[i],\n      maxLine = lines.length - hunk.oldLines,\n      localOffset = 0,\n      toPos = offset + hunk.oldStart - 1;\n    var iterator = distanceIterator(toPos, minLine, maxLine);\n    for (; localOffset !== undefined; localOffset = iterator()) {\n      if (hunkFits(hunk, toPos + localOffset)) {\n        hunk.offset = offset += localOffset;\n        break;\n      }\n    }\n    if (localOffset === undefined) {\n      return false;\n    } // Set lower text limit to end of the current hunk, so next ones don't try\n    // to fit over already patched text\n\n    minLine = hunk.offset + hunk.oldStart + hunk.oldLines;\n  } // Apply patch hunks\n\n  var diffOffset = 0;\n  for (var _i = 0; _i < hunks.length; _i++) {\n    var _hunk = hunks[_i],\n      _toPos = _hunk.oldStart + _hunk.offset + diffOffset - 1;\n    diffOffset += _hunk.newLines - _hunk.oldLines;\n    for (var j = 0; j < _hunk.lines.length; j++) {\n      var line = _hunk.lines[j],\n        operation = line.length > 0 ? line[0] : ' ',\n        content = line.length > 0 ? line.substr(1) : line,\n        delimiter = _hunk.linedelimiters && _hunk.linedelimiters[j] || '\\n';\n      if (operation === ' ') {\n        _toPos++;\n      } else if (operation === '-') {\n        lines.splice(_toPos, 1);\n        delimiters.splice(_toPos, 1);\n        /* istanbul ignore else */\n      } else if (operation === '+') {\n        lines.splice(_toPos, 0, content);\n        delimiters.splice(_toPos, 0, delimiter);\n        _toPos++;\n      } else if (operation === '\\\\') {\n        var previousOperation = _hunk.lines[j - 1] ? _hunk.lines[j - 1][0] : null;\n        if (previousOperation === '+') {\n          removeEOFNL = true;\n        } else if (previousOperation === '-') {\n          addEOFNL = true;\n        }\n      }\n    }\n  } // Handle EOFNL insertion/removal\n\n  if (removeEOFNL) {\n    while (!lines[lines.length - 1]) {\n      lines.pop();\n      delimiters.pop();\n    }\n  } else if (addEOFNL) {\n    lines.push('');\n    delimiters.push('\\n');\n  }\n  for (var _k = 0; _k < lines.length - 1; _k++) {\n    lines[_k] = lines[_k] + delimiters[_k];\n  }\n  return lines.join('');\n} // Wrapper that supports multiple file patches via callbacks.\n\nfunction applyPatches(uniDiff, options) {\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n  var currentIndex = 0;\n  function processIndex() {\n    var index = uniDiff[currentIndex++];\n    if (!index) {\n      return options.complete();\n    }\n    options.loadFile(index, function (err, data) {\n      if (err) {\n        return options.complete(err);\n      }\n      var updatedContent = applyPatch(data, index, options);\n      options.patched(index, updatedContent, function (err) {\n        if (err) {\n          return options.complete(err);\n        }\n        processIndex();\n      });\n    });\n  }\n  processIndex();\n}\nfunction structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  if (!options) {\n    options = {};\n  }\n  if (typeof options.context === 'undefined') {\n    options.context = 4;\n  }\n  var diff = diffLines(oldStr, newStr, options);\n  if (!diff) {\n    return;\n  }\n  diff.push({\n    value: '',\n    lines: []\n  }); // Append an empty value to make cleanup easier\n\n  function contextLines(lines) {\n    return lines.map(function (entry) {\n      return ' ' + entry;\n    });\n  }\n  var hunks = [];\n  var oldRangeStart = 0,\n    newRangeStart = 0,\n    curRange = [],\n    oldLine = 1,\n    newLine = 1;\n  var _loop = function _loop(i) {\n    var current = diff[i],\n      lines = current.lines || current.value.replace(/\\n$/, '').split('\\n');\n    current.lines = lines;\n    if (current.added || current.removed) {\n      var _curRange;\n\n      // If we have previous context, start with that\n      if (!oldRangeStart) {\n        var prev = diff[i - 1];\n        oldRangeStart = oldLine;\n        newRangeStart = newLine;\n        if (prev) {\n          curRange = options.context > 0 ? contextLines(prev.lines.slice(-options.context)) : [];\n          oldRangeStart -= curRange.length;\n          newRangeStart -= curRange.length;\n        }\n      } // Output our changes\n\n      (_curRange = curRange).push.apply(_curRange, _toConsumableArray(lines.map(function (entry) {\n        return (current.added ? '+' : '-') + entry;\n      }))); // Track the updated file position\n\n      if (current.added) {\n        newLine += lines.length;\n      } else {\n        oldLine += lines.length;\n      }\n    } else {\n      // Identical context lines. Track line changes\n      if (oldRangeStart) {\n        // Close out any changes that have been output (or join overlapping)\n        if (lines.length <= options.context * 2 && i < diff.length - 2) {\n          var _curRange2;\n\n          // Overlapping\n          (_curRange2 = curRange).push.apply(_curRange2, _toConsumableArray(contextLines(lines)));\n        } else {\n          var _curRange3;\n\n          // end the range and output\n          var contextSize = Math.min(lines.length, options.context);\n          (_curRange3 = curRange).push.apply(_curRange3, _toConsumableArray(contextLines(lines.slice(0, contextSize))));\n          var hunk = {\n            oldStart: oldRangeStart,\n            oldLines: oldLine - oldRangeStart + contextSize,\n            newStart: newRangeStart,\n            newLines: newLine - newRangeStart + contextSize,\n            lines: curRange\n          };\n          if (i >= diff.length - 2 && lines.length <= options.context) {\n            // EOF is inside this hunk\n            var oldEOFNewline = /\\n$/.test(oldStr);\n            var newEOFNewline = /\\n$/.test(newStr);\n            var noNlBeforeAdds = lines.length == 0 && curRange.length > hunk.oldLines;\n            if (!oldEOFNewline && noNlBeforeAdds && oldStr.length > 0) {\n              // special case: old has no eol and no trailing context; no-nl can end up before adds\n              // however, if the old file is empty, do not output the no-nl line\n              curRange.splice(hunk.oldLines, 0, '\\\\ No newline at end of file');\n            }\n            if (!oldEOFNewline && !noNlBeforeAdds || !newEOFNewline) {\n              curRange.push('\\\\ No newline at end of file');\n            }\n          }\n          hunks.push(hunk);\n          oldRangeStart = 0;\n          newRangeStart = 0;\n          curRange = [];\n        }\n      }\n      oldLine += lines.length;\n      newLine += lines.length;\n    }\n  };\n  for (var i = 0; i < diff.length; i++) {\n    _loop(i);\n  }\n  return {\n    oldFileName: oldFileName,\n    newFileName: newFileName,\n    oldHeader: oldHeader,\n    newHeader: newHeader,\n    hunks: hunks\n  };\n}\nfunction formatPatch(diff) {\n  if (Array.isArray(diff)) {\n    return diff.map(formatPatch).join('\\n');\n  }\n  var ret = [];\n  if (diff.oldFileName == diff.newFileName) {\n    ret.push('Index: ' + diff.oldFileName);\n  }\n  ret.push('===================================================================');\n  ret.push('--- ' + diff.oldFileName + (typeof diff.oldHeader === 'undefined' ? '' : '\\t' + diff.oldHeader));\n  ret.push('+++ ' + diff.newFileName + (typeof diff.newHeader === 'undefined' ? '' : '\\t' + diff.newHeader));\n  for (var i = 0; i < diff.hunks.length; i++) {\n    var hunk = diff.hunks[i]; // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n\n    if (hunk.oldLines === 0) {\n      hunk.oldStart -= 1;\n    }\n    if (hunk.newLines === 0) {\n      hunk.newStart -= 1;\n    }\n    ret.push('@@ -' + hunk.oldStart + ',' + hunk.oldLines + ' +' + hunk.newStart + ',' + hunk.newLines + ' @@');\n    ret.push.apply(ret, hunk.lines);\n  }\n  return ret.join('\\n') + '\\n';\n}\nfunction createTwoFilesPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  return formatPatch(structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options));\n}\nfunction createPatch(fileName, oldStr, newStr, oldHeader, newHeader, options) {\n  return createTwoFilesPatch(fileName, fileName, oldStr, newStr, oldHeader, newHeader, options);\n}\nfunction arrayEqual(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  }\n  return arrayStartsWith(a, b);\n}\nfunction arrayStartsWith(array, start) {\n  if (start.length > array.length) {\n    return false;\n  }\n  for (var i = 0; i < start.length; i++) {\n    if (start[i] !== array[i]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction calcLineCount(hunk) {\n  var _calcOldNewLineCount = calcOldNewLineCount(hunk.lines),\n    oldLines = _calcOldNewLineCount.oldLines,\n    newLines = _calcOldNewLineCount.newLines;\n  if (oldLines !== undefined) {\n    hunk.oldLines = oldLines;\n  } else {\n    delete hunk.oldLines;\n  }\n  if (newLines !== undefined) {\n    hunk.newLines = newLines;\n  } else {\n    delete hunk.newLines;\n  }\n}\nfunction merge(mine, theirs, base) {\n  mine = loadPatch(mine, base);\n  theirs = loadPatch(theirs, base);\n  var ret = {}; // For index we just let it pass through as it doesn't have any necessary meaning.\n  // Leaving sanity checks on this to the API consumer that may know more about the\n  // meaning in their own context.\n\n  if (mine.index || theirs.index) {\n    ret.index = mine.index || theirs.index;\n  }\n  if (mine.newFileName || theirs.newFileName) {\n    if (!fileNameChanged(mine)) {\n      // No header or no change in ours, use theirs (and ours if theirs does not exist)\n      ret.oldFileName = theirs.oldFileName || mine.oldFileName;\n      ret.newFileName = theirs.newFileName || mine.newFileName;\n      ret.oldHeader = theirs.oldHeader || mine.oldHeader;\n      ret.newHeader = theirs.newHeader || mine.newHeader;\n    } else if (!fileNameChanged(theirs)) {\n      // No header or no change in theirs, use ours\n      ret.oldFileName = mine.oldFileName;\n      ret.newFileName = mine.newFileName;\n      ret.oldHeader = mine.oldHeader;\n      ret.newHeader = mine.newHeader;\n    } else {\n      // Both changed... figure it out\n      ret.oldFileName = selectField(ret, mine.oldFileName, theirs.oldFileName);\n      ret.newFileName = selectField(ret, mine.newFileName, theirs.newFileName);\n      ret.oldHeader = selectField(ret, mine.oldHeader, theirs.oldHeader);\n      ret.newHeader = selectField(ret, mine.newHeader, theirs.newHeader);\n    }\n  }\n  ret.hunks = [];\n  var mineIndex = 0,\n    theirsIndex = 0,\n    mineOffset = 0,\n    theirsOffset = 0;\n  while (mineIndex < mine.hunks.length || theirsIndex < theirs.hunks.length) {\n    var mineCurrent = mine.hunks[mineIndex] || {\n        oldStart: Infinity\n      },\n      theirsCurrent = theirs.hunks[theirsIndex] || {\n        oldStart: Infinity\n      };\n    if (hunkBefore(mineCurrent, theirsCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(mineCurrent, mineOffset));\n      mineIndex++;\n      theirsOffset += mineCurrent.newLines - mineCurrent.oldLines;\n    } else if (hunkBefore(theirsCurrent, mineCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(theirsCurrent, theirsOffset));\n      theirsIndex++;\n      mineOffset += theirsCurrent.newLines - theirsCurrent.oldLines;\n    } else {\n      // Overlap, merge as best we can\n      var mergedHunk = {\n        oldStart: Math.min(mineCurrent.oldStart, theirsCurrent.oldStart),\n        oldLines: 0,\n        newStart: Math.min(mineCurrent.newStart + mineOffset, theirsCurrent.oldStart + theirsOffset),\n        newLines: 0,\n        lines: []\n      };\n      mergeLines(mergedHunk, mineCurrent.oldStart, mineCurrent.lines, theirsCurrent.oldStart, theirsCurrent.lines);\n      theirsIndex++;\n      mineIndex++;\n      ret.hunks.push(mergedHunk);\n    }\n  }\n  return ret;\n}\nfunction loadPatch(param, base) {\n  if (typeof param === 'string') {\n    if (/^@@/m.test(param) || /^Index:/m.test(param)) {\n      return parsePatch(param)[0];\n    }\n    if (!base) {\n      throw new Error('Must provide a base reference or pass in a patch');\n    }\n    return structuredPatch(undefined, undefined, base, param);\n  }\n  return param;\n}\nfunction fileNameChanged(patch) {\n  return patch.newFileName && patch.newFileName !== patch.oldFileName;\n}\nfunction selectField(index, mine, theirs) {\n  if (mine === theirs) {\n    return mine;\n  } else {\n    index.conflict = true;\n    return {\n      mine: mine,\n      theirs: theirs\n    };\n  }\n}\nfunction hunkBefore(test, check) {\n  return test.oldStart < check.oldStart && test.oldStart + test.oldLines < check.oldStart;\n}\nfunction cloneHunk(hunk, offset) {\n  return {\n    oldStart: hunk.oldStart,\n    oldLines: hunk.oldLines,\n    newStart: hunk.newStart + offset,\n    newLines: hunk.newLines,\n    lines: hunk.lines\n  };\n}\nfunction mergeLines(hunk, mineOffset, mineLines, theirOffset, theirLines) {\n  // This will generally result in a conflicted hunk, but there are cases where the context\n  // is the only overlap where we can successfully merge the content here.\n  var mine = {\n      offset: mineOffset,\n      lines: mineLines,\n      index: 0\n    },\n    their = {\n      offset: theirOffset,\n      lines: theirLines,\n      index: 0\n    }; // Handle any leading content\n\n  insertLeading(hunk, mine, their);\n  insertLeading(hunk, their, mine); // Now in the overlap content. Scan through and select the best changes from each.\n\n  while (mine.index < mine.lines.length && their.index < their.lines.length) {\n    var mineCurrent = mine.lines[mine.index],\n      theirCurrent = their.lines[their.index];\n    if ((mineCurrent[0] === '-' || mineCurrent[0] === '+') && (theirCurrent[0] === '-' || theirCurrent[0] === '+')) {\n      // Both modified ...\n      mutualChange(hunk, mine, their);\n    } else if (mineCurrent[0] === '+' && theirCurrent[0] === ' ') {\n      var _hunk$lines;\n\n      // Mine inserted\n      (_hunk$lines = hunk.lines).push.apply(_hunk$lines, _toConsumableArray(collectChange(mine)));\n    } else if (theirCurrent[0] === '+' && mineCurrent[0] === ' ') {\n      var _hunk$lines2;\n\n      // Theirs inserted\n      (_hunk$lines2 = hunk.lines).push.apply(_hunk$lines2, _toConsumableArray(collectChange(their)));\n    } else if (mineCurrent[0] === '-' && theirCurrent[0] === ' ') {\n      // Mine removed or edited\n      removal(hunk, mine, their);\n    } else if (theirCurrent[0] === '-' && mineCurrent[0] === ' ') {\n      // Their removed or edited\n      removal(hunk, their, mine, true);\n    } else if (mineCurrent === theirCurrent) {\n      // Context identity\n      hunk.lines.push(mineCurrent);\n      mine.index++;\n      their.index++;\n    } else {\n      // Context mismatch\n      conflict(hunk, collectChange(mine), collectChange(their));\n    }\n  } // Now push anything that may be remaining\n\n  insertTrailing(hunk, mine);\n  insertTrailing(hunk, their);\n  calcLineCount(hunk);\n}\nfunction mutualChange(hunk, mine, their) {\n  var myChanges = collectChange(mine),\n    theirChanges = collectChange(their);\n  if (allRemoves(myChanges) && allRemoves(theirChanges)) {\n    // Special case for remove changes that are supersets of one another\n    if (arrayStartsWith(myChanges, theirChanges) && skipRemoveSuperset(their, myChanges, myChanges.length - theirChanges.length)) {\n      var _hunk$lines3;\n      (_hunk$lines3 = hunk.lines).push.apply(_hunk$lines3, _toConsumableArray(myChanges));\n      return;\n    } else if (arrayStartsWith(theirChanges, myChanges) && skipRemoveSuperset(mine, theirChanges, theirChanges.length - myChanges.length)) {\n      var _hunk$lines4;\n      (_hunk$lines4 = hunk.lines).push.apply(_hunk$lines4, _toConsumableArray(theirChanges));\n      return;\n    }\n  } else if (arrayEqual(myChanges, theirChanges)) {\n    var _hunk$lines5;\n    (_hunk$lines5 = hunk.lines).push.apply(_hunk$lines5, _toConsumableArray(myChanges));\n    return;\n  }\n  conflict(hunk, myChanges, theirChanges);\n}\nfunction removal(hunk, mine, their, swap) {\n  var myChanges = collectChange(mine),\n    theirChanges = collectContext(their, myChanges);\n  if (theirChanges.merged) {\n    var _hunk$lines6;\n    (_hunk$lines6 = hunk.lines).push.apply(_hunk$lines6, _toConsumableArray(theirChanges.merged));\n  } else {\n    conflict(hunk, swap ? theirChanges : myChanges, swap ? myChanges : theirChanges);\n  }\n}\nfunction conflict(hunk, mine, their) {\n  hunk.conflict = true;\n  hunk.lines.push({\n    conflict: true,\n    mine: mine,\n    theirs: their\n  });\n}\nfunction insertLeading(hunk, insert, their) {\n  while (insert.offset < their.offset && insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n    insert.offset++;\n  }\n}\nfunction insertTrailing(hunk, insert) {\n  while (insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n  }\n}\nfunction collectChange(state) {\n  var ret = [],\n    operation = state.lines[state.index][0];\n  while (state.index < state.lines.length) {\n    var line = state.lines[state.index]; // Group additions that are immediately after subtractions and treat them as one \"atomic\" modify change.\n\n    if (operation === '-' && line[0] === '+') {\n      operation = '+';\n    }\n    if (operation === line[0]) {\n      ret.push(line);\n      state.index++;\n    } else {\n      break;\n    }\n  }\n  return ret;\n}\nfunction collectContext(state, matchChanges) {\n  var changes = [],\n    merged = [],\n    matchIndex = 0,\n    contextChanges = false,\n    conflicted = false;\n  while (matchIndex < matchChanges.length && state.index < state.lines.length) {\n    var change = state.lines[state.index],\n      match = matchChanges[matchIndex]; // Once we've hit our add, then we are done\n\n    if (match[0] === '+') {\n      break;\n    }\n    contextChanges = contextChanges || change[0] !== ' ';\n    merged.push(match);\n    matchIndex++; // Consume any additions in the other block as a conflict to attempt\n    // to pull in the remaining context after this\n\n    if (change[0] === '+') {\n      conflicted = true;\n      while (change[0] === '+') {\n        changes.push(change);\n        change = state.lines[++state.index];\n      }\n    }\n    if (match.substr(1) === change.substr(1)) {\n      changes.push(change);\n      state.index++;\n    } else {\n      conflicted = true;\n    }\n  }\n  if ((matchChanges[matchIndex] || '')[0] === '+' && contextChanges) {\n    conflicted = true;\n  }\n  if (conflicted) {\n    return changes;\n  }\n  while (matchIndex < matchChanges.length) {\n    merged.push(matchChanges[matchIndex++]);\n  }\n  return {\n    merged: merged,\n    changes: changes\n  };\n}\nfunction allRemoves(changes) {\n  return changes.reduce(function (prev, change) {\n    return prev && change[0] === '-';\n  }, true);\n}\nfunction skipRemoveSuperset(state, removeChanges, delta) {\n  for (var i = 0; i < delta; i++) {\n    var changeContent = removeChanges[removeChanges.length - delta + i].substr(1);\n    if (state.lines[state.index + i] !== ' ' + changeContent) {\n      return false;\n    }\n  }\n  state.index += delta;\n  return true;\n}\nfunction calcOldNewLineCount(lines) {\n  var oldLines = 0;\n  var newLines = 0;\n  lines.forEach(function (line) {\n    if (typeof line !== 'string') {\n      var myCount = calcOldNewLineCount(line.mine);\n      var theirCount = calcOldNewLineCount(line.theirs);\n      if (oldLines !== undefined) {\n        if (myCount.oldLines === theirCount.oldLines) {\n          oldLines += myCount.oldLines;\n        } else {\n          oldLines = undefined;\n        }\n      }\n      if (newLines !== undefined) {\n        if (myCount.newLines === theirCount.newLines) {\n          newLines += myCount.newLines;\n        } else {\n          newLines = undefined;\n        }\n      }\n    } else {\n      if (newLines !== undefined && (line[0] === '+' || line[0] === ' ')) {\n        newLines++;\n      }\n      if (oldLines !== undefined && (line[0] === '-' || line[0] === ' ')) {\n        oldLines++;\n      }\n    }\n  });\n  return {\n    oldLines: oldLines,\n    newLines: newLines\n  };\n}\nfunction reversePatch(structuredPatch) {\n  if (Array.isArray(structuredPatch)) {\n    return structuredPatch.map(reversePatch).reverse();\n  }\n  return _objectSpread2(_objectSpread2({}, structuredPatch), {}, {\n    oldFileName: structuredPatch.newFileName,\n    oldHeader: structuredPatch.newHeader,\n    newFileName: structuredPatch.oldFileName,\n    newHeader: structuredPatch.oldHeader,\n    hunks: structuredPatch.hunks.map(function (hunk) {\n      return {\n        oldLines: hunk.newLines,\n        oldStart: hunk.newStart,\n        newLines: hunk.oldLines,\n        newStart: hunk.oldStart,\n        linedelimiters: hunk.linedelimiters,\n        lines: hunk.lines.map(function (l) {\n          if (l.startsWith('-')) {\n            return \"+\".concat(l.slice(1));\n          }\n          if (l.startsWith('+')) {\n            return \"-\".concat(l.slice(1));\n          }\n          return l;\n        })\n      };\n    })\n  });\n}\n\n// See: http://code.google.com/p/google-diff-match-patch/wiki/API\nfunction convertChangesToDMP(changes) {\n  var ret = [],\n    change,\n    operation;\n  for (var i = 0; i < changes.length; i++) {\n    change = changes[i];\n    if (change.added) {\n      operation = 1;\n    } else if (change.removed) {\n      operation = -1;\n    } else {\n      operation = 0;\n    }\n    ret.push([operation, change.value]);\n  }\n  return ret;\n}\nfunction convertChangesToXML(changes) {\n  var ret = [];\n  for (var i = 0; i < changes.length; i++) {\n    var change = changes[i];\n    if (change.added) {\n      ret.push('<ins>');\n    } else if (change.removed) {\n      ret.push('<del>');\n    }\n    ret.push(escapeHTML(change.value));\n    if (change.added) {\n      ret.push('</ins>');\n    } else if (change.removed) {\n      ret.push('</del>');\n    }\n  }\n  return ret.join('');\n}\nfunction escapeHTML(s) {\n  var n = s;\n  n = n.replace(/&/g, '&amp;');\n  n = n.replace(/</g, '&lt;');\n  n = n.replace(/>/g, '&gt;');\n  n = n.replace(/\"/g, '&quot;');\n  return n;\n}\nexport { Diff, applyPatch, applyPatches, canonicalize, convertChangesToDMP, convertChangesToXML, createPatch, createTwoFilesPatch, diffArrays, diffChars, diffCss, diffJson, diffLines, diffSentences, diffTrimmedLines, diffWords, diffWordsWithSpace, formatPatch, merge, parsePatch, reversePatch, structuredPatch };", "map": {"version": 3, "names": ["Diff", "prototype", "diff", "oldString", "newString", "_options$timeout", "options", "arguments", "length", "undefined", "callback", "self", "done", "value", "setTimeout", "castInput", "removeEmpty", "tokenize", "newLen", "old<PERSON>en", "edit<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "min", "maxExecutionTime", "timeout", "Infinity", "abortAfterTimestamp", "Date", "now", "bestPath", "oldPos", "lastComponent", "newPos", "extractCommon", "join", "count", "minDiagonalToConsider", "maxDiagonalToConsider", "execEditLength", "diagonalPath", "max", "basePath", "remove<PERSON>ath", "addPath", "canAdd", "addPathNewPos", "canRemove", "addToPath", "buildValues", "useLongestToken", "exec", "ret", "path", "added", "removed", "oldPosInc", "last", "previousComponent", "commonCount", "equals", "left", "right", "comparator", "ignoreCase", "toLowerCase", "array", "i", "push", "split", "chars", "components", "nextComponent", "reverse", "componentPos", "componentLen", "component", "slice", "map", "oldValue", "tmp", "finalComponent", "pop", "characterDiff", "diffChars", "oldStr", "newStr", "generateOptions", "defaults", "name", "hasOwnProperty", "extendedWordChars", "reWhitespace", "wordDiff", "ignoreWhitespace", "test", "tokens", "splice", "diffWords", "diffWordsWithSpace", "lineDiff", "stripTrailingCr", "replace", "retLines", "linesAndNewlines", "line", "newlineIsToken", "trim", "diffLines", "diffTrimmedLines", "sentenceDiff", "diffSentences", "cssDiff", "diffCss", "_typeof", "obj", "Symbol", "iterator", "constructor", "_defineProperty", "key", "Object", "defineProperty", "enumerable", "configurable", "writable", "ownKeys", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "apply", "_objectSpread2", "target", "source", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "Array", "isArray", "_arrayLikeToArray", "iter", "from", "o", "minLen", "n", "toString", "call", "len", "arr2", "TypeError", "objectPrototypeToString", "jsonDiff", "_this$options", "undefinedReplacement", "_this$options$stringi", "stringifyReplacer", "k", "v", "JSON", "stringify", "canonicalize", "diff<PERSON><PERSON>", "oldObj", "newObj", "stack", "replacementStack", "replacer", "canonicalizedObj", "toJSON", "sortedKeys", "_key", "sort", "arrayDiff", "diffArrays", "oldArr", "newArr", "parsePatch", "uniDiff", "diffstr", "delimiters", "match", "list", "parseIndex", "index", "header", "parseFileHeader", "hunks", "_line", "parseHunk", "strict", "Error", "fileHeader", "keyPrefix", "data", "fileName", "substr", "chunkHeaderIndex", "chunkHeaderLine", "chunkHeader", "hunk", "oldStart", "oldLines", "newStart", "newLines", "lines", "linedelimiters", "addCount", "removeCount", "indexOf", "operation", "distanceIterator", "start", "minLine", "maxLine", "wantForward", "backwardExhausted", "forwardExhausted", "localOffset", "applyPatch", "compareLine", "lineNumber", "patchContent", "errorCount", "fuzzFactor", "offset", "removeEOFNL", "addEOFNL", "hunkFits", "toPos", "j", "content", "diffOffset", "_i", "_hunk", "_toPos", "delimiter", "previousOperation", "_k", "applyPatches", "currentIndex", "processIndex", "complete", "loadFile", "err", "updatedContent", "patched", "structuredPatch", "oldFileName", "newFileName", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "context", "contextLines", "entry", "oldRangeStart", "newRangeStart", "cur<PERSON><PERSON><PERSON>", "oldLine", "newLine", "_loop", "current", "_cur<PERSON><PERSON>e", "prev", "_curRange2", "_curRange3", "contextSize", "oldEOFNewline", "newEOFNewline", "noNlBeforeAdds", "formatPatch", "createTwoFilesPatch", "createPatch", "arrayEqual", "a", "b", "arrayStartsWith", "calcLineCount", "_calcOldNewLineCount", "calcOldNewLineCount", "merge", "mine", "theirs", "base", "loadPatch", "fileNameChanged", "selectField", "mineIndex", "theirsIndex", "mineOffset", "theirsOffset", "mineCurrent", "theirsCurrent", "hunkBefore", "cloneHunk", "mergedHunk", "mergeLines", "param", "patch", "conflict", "check", "mineLines", "theirOffset", "theirLines", "their", "insertLeading", "theirCurrent", "mutualChange", "_hunk$lines", "collectChange", "_hunk$lines2", "removal", "insertTrailing", "myChanges", "theirChanges", "allRemoves", "skipRemoveSuperset", "_hunk$lines3", "_hunk$lines4", "_hunk$lines5", "swap", "collectContext", "merged", "_hunk$lines6", "insert", "state", "matchChanges", "changes", "matchIndex", "contextChanges", "conflicted", "change", "reduce", "removeChanges", "delta", "changeContent", "myCount", "theirCount", "reversePatch", "l", "startsWith", "concat", "convertChangesToDMP", "convertChangesToXML", "escapeHTML", "s"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/diff/lib/index.mjs"], "sourcesContent": ["function Diff() {}\nDiff.prototype = {\n  diff: function diff(oldString, newString) {\n    var _options$timeout;\n\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var callback = options.callback;\n\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n\n    this.options = options;\n    var self = this;\n\n    function done(value) {\n      if (callback) {\n        setTimeout(function () {\n          callback(undefined, value);\n        }, 0);\n        return true;\n      } else {\n        return value;\n      }\n    } // Allow subclasses to massage the input prior to running\n\n\n    oldString = this.castInput(oldString);\n    newString = this.castInput(newString);\n    oldString = this.removeEmpty(this.tokenize(oldString));\n    newString = this.removeEmpty(this.tokenize(newString));\n    var newLen = newString.length,\n        oldLen = oldString.length;\n    var editLength = 1;\n    var maxEditLength = newLen + oldLen;\n\n    if (options.maxEditLength) {\n      maxEditLength = Math.min(maxEditLength, options.maxEditLength);\n    }\n\n    var maxExecutionTime = (_options$timeout = options.timeout) !== null && _options$timeout !== void 0 ? _options$timeout : Infinity;\n    var abortAfterTimestamp = Date.now() + maxExecutionTime;\n    var bestPath = [{\n      oldPos: -1,\n      lastComponent: undefined\n    }]; // Seed editLength = 0, i.e. the content starts with the same values\n\n    var newPos = this.extractCommon(bestPath[0], newString, oldString, 0);\n\n    if (bestPath[0].oldPos + 1 >= oldLen && newPos + 1 >= newLen) {\n      // Identity per the equality and tokenizer\n      return done([{\n        value: this.join(newString),\n        count: newString.length\n      }]);\n    } // Once we hit the right edge of the edit graph on some diagonal k, we can\n    // definitely reach the end of the edit graph in no more than k edits, so\n    // there's no point in considering any moves to diagonal k+1 any more (from\n    // which we're guaranteed to need at least k+1 more edits).\n    // Similarly, once we've reached the bottom of the edit graph, there's no\n    // point considering moves to lower diagonals.\n    // We record this fact by setting minDiagonalToConsider and\n    // maxDiagonalToConsider to some finite value once we've hit the edge of\n    // the edit graph.\n    // This optimization is not faithful to the original algorithm presented in\n    // Myers's paper, which instead pointlessly extends D-paths off the end of\n    // the edit graph - see page 7 of Myers's paper which notes this point\n    // explicitly and illustrates it with a diagram. This has major performance\n    // implications for some common scenarios. For instance, to compute a diff\n    // where the new text simply appends d characters on the end of the\n    // original text of length n, the true Myers algorithm will take O(n+d^2)\n    // time while this optimization needs only O(n+d) time.\n\n\n    var minDiagonalToConsider = -Infinity,\n        maxDiagonalToConsider = Infinity; // Main worker method. checks all permutations of a given edit length for acceptance.\n\n    function execEditLength() {\n      for (var diagonalPath = Math.max(minDiagonalToConsider, -editLength); diagonalPath <= Math.min(maxDiagonalToConsider, editLength); diagonalPath += 2) {\n        var basePath = void 0;\n        var removePath = bestPath[diagonalPath - 1],\n            addPath = bestPath[diagonalPath + 1];\n\n        if (removePath) {\n          // No one else is going to attempt to use this value, clear it\n          bestPath[diagonalPath - 1] = undefined;\n        }\n\n        var canAdd = false;\n\n        if (addPath) {\n          // what newPos will be after we do an insertion:\n          var addPathNewPos = addPath.oldPos - diagonalPath;\n          canAdd = addPath && 0 <= addPathNewPos && addPathNewPos < newLen;\n        }\n\n        var canRemove = removePath && removePath.oldPos + 1 < oldLen;\n\n        if (!canAdd && !canRemove) {\n          // If this path is a terminal then prune\n          bestPath[diagonalPath] = undefined;\n          continue;\n        } // Select the diagonal that we want to branch from. We select the prior\n        // path whose position in the old string is the farthest from the origin\n        // and does not pass the bounds of the diff graph\n        // TODO: Remove the `+ 1` here to make behavior match Myers algorithm\n        //       and prefer to order removals before insertions.\n\n\n        if (!canRemove || canAdd && removePath.oldPos + 1 < addPath.oldPos) {\n          basePath = self.addToPath(addPath, true, undefined, 0);\n        } else {\n          basePath = self.addToPath(removePath, undefined, true, 1);\n        }\n\n        newPos = self.extractCommon(basePath, newString, oldString, diagonalPath);\n\n        if (basePath.oldPos + 1 >= oldLen && newPos + 1 >= newLen) {\n          // If we have hit the end of both strings, then we are done\n          return done(buildValues(self, basePath.lastComponent, newString, oldString, self.useLongestToken));\n        } else {\n          bestPath[diagonalPath] = basePath;\n\n          if (basePath.oldPos + 1 >= oldLen) {\n            maxDiagonalToConsider = Math.min(maxDiagonalToConsider, diagonalPath - 1);\n          }\n\n          if (newPos + 1 >= newLen) {\n            minDiagonalToConsider = Math.max(minDiagonalToConsider, diagonalPath + 1);\n          }\n        }\n      }\n\n      editLength++;\n    } // Performs the length of edit iteration. Is a bit fugly as this has to support the\n    // sync and async mode which is never fun. Loops over execEditLength until a value\n    // is produced, or until the edit length exceeds options.maxEditLength (if given),\n    // in which case it will return undefined.\n\n\n    if (callback) {\n      (function exec() {\n        setTimeout(function () {\n          if (editLength > maxEditLength || Date.now() > abortAfterTimestamp) {\n            return callback();\n          }\n\n          if (!execEditLength()) {\n            exec();\n          }\n        }, 0);\n      })();\n    } else {\n      while (editLength <= maxEditLength && Date.now() <= abortAfterTimestamp) {\n        var ret = execEditLength();\n\n        if (ret) {\n          return ret;\n        }\n      }\n    }\n  },\n  addToPath: function addToPath(path, added, removed, oldPosInc) {\n    var last = path.lastComponent;\n\n    if (last && last.added === added && last.removed === removed) {\n      return {\n        oldPos: path.oldPos + oldPosInc,\n        lastComponent: {\n          count: last.count + 1,\n          added: added,\n          removed: removed,\n          previousComponent: last.previousComponent\n        }\n      };\n    } else {\n      return {\n        oldPos: path.oldPos + oldPosInc,\n        lastComponent: {\n          count: 1,\n          added: added,\n          removed: removed,\n          previousComponent: last\n        }\n      };\n    }\n  },\n  extractCommon: function extractCommon(basePath, newString, oldString, diagonalPath) {\n    var newLen = newString.length,\n        oldLen = oldString.length,\n        oldPos = basePath.oldPos,\n        newPos = oldPos - diagonalPath,\n        commonCount = 0;\n\n    while (newPos + 1 < newLen && oldPos + 1 < oldLen && this.equals(newString[newPos + 1], oldString[oldPos + 1])) {\n      newPos++;\n      oldPos++;\n      commonCount++;\n    }\n\n    if (commonCount) {\n      basePath.lastComponent = {\n        count: commonCount,\n        previousComponent: basePath.lastComponent\n      };\n    }\n\n    basePath.oldPos = oldPos;\n    return newPos;\n  },\n  equals: function equals(left, right) {\n    if (this.options.comparator) {\n      return this.options.comparator(left, right);\n    } else {\n      return left === right || this.options.ignoreCase && left.toLowerCase() === right.toLowerCase();\n    }\n  },\n  removeEmpty: function removeEmpty(array) {\n    var ret = [];\n\n    for (var i = 0; i < array.length; i++) {\n      if (array[i]) {\n        ret.push(array[i]);\n      }\n    }\n\n    return ret;\n  },\n  castInput: function castInput(value) {\n    return value;\n  },\n  tokenize: function tokenize(value) {\n    return value.split('');\n  },\n  join: function join(chars) {\n    return chars.join('');\n  }\n};\n\nfunction buildValues(diff, lastComponent, newString, oldString, useLongestToken) {\n  // First we convert our linked list of components in reverse order to an\n  // array in the right order:\n  var components = [];\n  var nextComponent;\n\n  while (lastComponent) {\n    components.push(lastComponent);\n    nextComponent = lastComponent.previousComponent;\n    delete lastComponent.previousComponent;\n    lastComponent = nextComponent;\n  }\n\n  components.reverse();\n  var componentPos = 0,\n      componentLen = components.length,\n      newPos = 0,\n      oldPos = 0;\n\n  for (; componentPos < componentLen; componentPos++) {\n    var component = components[componentPos];\n\n    if (!component.removed) {\n      if (!component.added && useLongestToken) {\n        var value = newString.slice(newPos, newPos + component.count);\n        value = value.map(function (value, i) {\n          var oldValue = oldString[oldPos + i];\n          return oldValue.length > value.length ? oldValue : value;\n        });\n        component.value = diff.join(value);\n      } else {\n        component.value = diff.join(newString.slice(newPos, newPos + component.count));\n      }\n\n      newPos += component.count; // Common case\n\n      if (!component.added) {\n        oldPos += component.count;\n      }\n    } else {\n      component.value = diff.join(oldString.slice(oldPos, oldPos + component.count));\n      oldPos += component.count; // Reverse add and remove so removes are output first to match common convention\n      // The diffing algorithm is tied to add then remove output and this is the simplest\n      // route to get the desired output with minimal overhead.\n\n      if (componentPos && components[componentPos - 1].added) {\n        var tmp = components[componentPos - 1];\n        components[componentPos - 1] = components[componentPos];\n        components[componentPos] = tmp;\n      }\n    }\n  } // Special case handle for when one terminal is ignored (i.e. whitespace).\n  // For this case we merge the terminal into the prior string and drop the change.\n  // This is only available for string mode.\n\n\n  var finalComponent = components[componentLen - 1];\n\n  if (componentLen > 1 && typeof finalComponent.value === 'string' && (finalComponent.added || finalComponent.removed) && diff.equals('', finalComponent.value)) {\n    components[componentLen - 2].value += finalComponent.value;\n    components.pop();\n  }\n\n  return components;\n}\n\nvar characterDiff = new Diff();\nfunction diffChars(oldStr, newStr, options) {\n  return characterDiff.diff(oldStr, newStr, options);\n}\n\nfunction generateOptions(options, defaults) {\n  if (typeof options === 'function') {\n    defaults.callback = options;\n  } else if (options) {\n    for (var name in options) {\n      /* istanbul ignore else */\n      if (options.hasOwnProperty(name)) {\n        defaults[name] = options[name];\n      }\n    }\n  }\n\n  return defaults;\n}\n\n//\n// Ranges and exceptions:\n// Latin-1 Supplement, 0080–00FF\n//  - U+00D7  × Multiplication sign\n//  - U+00F7  ÷ Division sign\n// Latin Extended-A, 0100–017F\n// Latin Extended-B, 0180–024F\n// IPA Extensions, 0250–02AF\n// Spacing Modifier Letters, 02B0–02FF\n//  - U+02C7  ˇ &#711;  Caron\n//  - U+02D8  ˘ &#728;  Breve\n//  - U+02D9  ˙ &#729;  Dot Above\n//  - U+02DA  ˚ &#730;  Ring Above\n//  - U+02DB  ˛ &#731;  Ogonek\n//  - U+02DC  ˜ &#732;  Small Tilde\n//  - U+02DD  ˝ &#733;  Double Acute Accent\n// Latin Extended Additional, 1E00–1EFF\n\nvar extendedWordChars = /^[A-Za-z\\xC0-\\u02C6\\u02C8-\\u02D7\\u02DE-\\u02FF\\u1E00-\\u1EFF]+$/;\nvar reWhitespace = /\\S/;\nvar wordDiff = new Diff();\n\nwordDiff.equals = function (left, right) {\n  if (this.options.ignoreCase) {\n    left = left.toLowerCase();\n    right = right.toLowerCase();\n  }\n\n  return left === right || this.options.ignoreWhitespace && !reWhitespace.test(left) && !reWhitespace.test(right);\n};\n\nwordDiff.tokenize = function (value) {\n  // All whitespace symbols except newline group into one token, each newline - in separate token\n  var tokens = value.split(/([^\\S\\r\\n]+|[()[\\]{}'\"\\r\\n]|\\b)/); // Join the boundary splits that we do not consider to be boundaries. This is primarily the extended Latin character set.\n\n  for (var i = 0; i < tokens.length - 1; i++) {\n    // If we have an empty string in the next field and we have only word chars before and after, merge\n    if (!tokens[i + 1] && tokens[i + 2] && extendedWordChars.test(tokens[i]) && extendedWordChars.test(tokens[i + 2])) {\n      tokens[i] += tokens[i + 2];\n      tokens.splice(i + 1, 2);\n      i--;\n    }\n  }\n\n  return tokens;\n};\n\nfunction diffWords(oldStr, newStr, options) {\n  options = generateOptions(options, {\n    ignoreWhitespace: true\n  });\n  return wordDiff.diff(oldStr, newStr, options);\n}\nfunction diffWordsWithSpace(oldStr, newStr, options) {\n  return wordDiff.diff(oldStr, newStr, options);\n}\n\nvar lineDiff = new Diff();\n\nlineDiff.tokenize = function (value) {\n  if (this.options.stripTrailingCr) {\n    // remove one \\r before \\n to match GNU diff's --strip-trailing-cr behavior\n    value = value.replace(/\\r\\n/g, '\\n');\n  }\n\n  var retLines = [],\n      linesAndNewlines = value.split(/(\\n|\\r\\n)/); // Ignore the final empty token that occurs if the string ends with a new line\n\n  if (!linesAndNewlines[linesAndNewlines.length - 1]) {\n    linesAndNewlines.pop();\n  } // Merge the content and line separators into single tokens\n\n\n  for (var i = 0; i < linesAndNewlines.length; i++) {\n    var line = linesAndNewlines[i];\n\n    if (i % 2 && !this.options.newlineIsToken) {\n      retLines[retLines.length - 1] += line;\n    } else {\n      if (this.options.ignoreWhitespace) {\n        line = line.trim();\n      }\n\n      retLines.push(line);\n    }\n  }\n\n  return retLines;\n};\n\nfunction diffLines(oldStr, newStr, callback) {\n  return lineDiff.diff(oldStr, newStr, callback);\n}\nfunction diffTrimmedLines(oldStr, newStr, callback) {\n  var options = generateOptions(callback, {\n    ignoreWhitespace: true\n  });\n  return lineDiff.diff(oldStr, newStr, options);\n}\n\nvar sentenceDiff = new Diff();\n\nsentenceDiff.tokenize = function (value) {\n  return value.split(/(\\S.+?[.!?])(?=\\s+|$)/);\n};\n\nfunction diffSentences(oldStr, newStr, callback) {\n  return sentenceDiff.diff(oldStr, newStr, callback);\n}\n\nvar cssDiff = new Diff();\n\ncssDiff.tokenize = function (value) {\n  return value.split(/([{}:;,]|\\s+)/);\n};\n\nfunction diffCss(oldStr, newStr, callback) {\n  return cssDiff.diff(oldStr, newStr, callback);\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    });\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\n\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\n\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && Symbol.iterator in Object(iter)) return Array.from(iter);\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar objectPrototypeToString = Object.prototype.toString;\nvar jsonDiff = new Diff(); // Discriminate between two lines of pretty-printed, serialized JSON where one of them has a\n// dangling comma and the other doesn't. Turns out including the dangling comma yields the nicest output:\n\njsonDiff.useLongestToken = true;\njsonDiff.tokenize = lineDiff.tokenize;\n\njsonDiff.castInput = function (value) {\n  var _this$options = this.options,\n      undefinedReplacement = _this$options.undefinedReplacement,\n      _this$options$stringi = _this$options.stringifyReplacer,\n      stringifyReplacer = _this$options$stringi === void 0 ? function (k, v) {\n    return typeof v === 'undefined' ? undefinedReplacement : v;\n  } : _this$options$stringi;\n  return typeof value === 'string' ? value : JSON.stringify(canonicalize(value, null, null, stringifyReplacer), stringifyReplacer, '  ');\n};\n\njsonDiff.equals = function (left, right) {\n  return Diff.prototype.equals.call(jsonDiff, left.replace(/,([\\r\\n])/g, '$1'), right.replace(/,([\\r\\n])/g, '$1'));\n};\n\nfunction diffJson(oldObj, newObj, options) {\n  return jsonDiff.diff(oldObj, newObj, options);\n} // This function handles the presence of circular references by bailing out when encountering an\n// object that is already on the \"stack\" of items being processed. Accepts an optional replacer\n\nfunction canonicalize(obj, stack, replacementStack, replacer, key) {\n  stack = stack || [];\n  replacementStack = replacementStack || [];\n\n  if (replacer) {\n    obj = replacer(key, obj);\n  }\n\n  var i;\n\n  for (i = 0; i < stack.length; i += 1) {\n    if (stack[i] === obj) {\n      return replacementStack[i];\n    }\n  }\n\n  var canonicalizedObj;\n\n  if ('[object Array]' === objectPrototypeToString.call(obj)) {\n    stack.push(obj);\n    canonicalizedObj = new Array(obj.length);\n    replacementStack.push(canonicalizedObj);\n\n    for (i = 0; i < obj.length; i += 1) {\n      canonicalizedObj[i] = canonicalize(obj[i], stack, replacementStack, replacer, key);\n    }\n\n    stack.pop();\n    replacementStack.pop();\n    return canonicalizedObj;\n  }\n\n  if (obj && obj.toJSON) {\n    obj = obj.toJSON();\n  }\n\n  if (_typeof(obj) === 'object' && obj !== null) {\n    stack.push(obj);\n    canonicalizedObj = {};\n    replacementStack.push(canonicalizedObj);\n\n    var sortedKeys = [],\n        _key;\n\n    for (_key in obj) {\n      /* istanbul ignore else */\n      if (obj.hasOwnProperty(_key)) {\n        sortedKeys.push(_key);\n      }\n    }\n\n    sortedKeys.sort();\n\n    for (i = 0; i < sortedKeys.length; i += 1) {\n      _key = sortedKeys[i];\n      canonicalizedObj[_key] = canonicalize(obj[_key], stack, replacementStack, replacer, _key);\n    }\n\n    stack.pop();\n    replacementStack.pop();\n  } else {\n    canonicalizedObj = obj;\n  }\n\n  return canonicalizedObj;\n}\n\nvar arrayDiff = new Diff();\n\narrayDiff.tokenize = function (value) {\n  return value.slice();\n};\n\narrayDiff.join = arrayDiff.removeEmpty = function (value) {\n  return value;\n};\n\nfunction diffArrays(oldArr, newArr, callback) {\n  return arrayDiff.diff(oldArr, newArr, callback);\n}\n\nfunction parsePatch(uniDiff) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var diffstr = uniDiff.split(/\\r\\n|[\\n\\v\\f\\r\\x85]/),\n      delimiters = uniDiff.match(/\\r\\n|[\\n\\v\\f\\r\\x85]/g) || [],\n      list = [],\n      i = 0;\n\n  function parseIndex() {\n    var index = {};\n    list.push(index); // Parse diff metadata\n\n    while (i < diffstr.length) {\n      var line = diffstr[i]; // File header found, end parsing diff metadata\n\n      if (/^(\\-\\-\\-|\\+\\+\\+|@@)\\s/.test(line)) {\n        break;\n      } // Diff index\n\n\n      var header = /^(?:Index:|diff(?: -r \\w+)+)\\s+(.+?)\\s*$/.exec(line);\n\n      if (header) {\n        index.index = header[1];\n      }\n\n      i++;\n    } // Parse file headers if they are defined. Unified diff requires them, but\n    // there's no technical issues to have an isolated hunk without file header\n\n\n    parseFileHeader(index);\n    parseFileHeader(index); // Parse hunks\n\n    index.hunks = [];\n\n    while (i < diffstr.length) {\n      var _line = diffstr[i];\n\n      if (/^(Index:|diff|\\-\\-\\-|\\+\\+\\+)\\s/.test(_line)) {\n        break;\n      } else if (/^@@/.test(_line)) {\n        index.hunks.push(parseHunk());\n      } else if (_line && options.strict) {\n        // Ignore unexpected content unless in strict mode\n        throw new Error('Unknown line ' + (i + 1) + ' ' + JSON.stringify(_line));\n      } else {\n        i++;\n      }\n    }\n  } // Parses the --- and +++ headers, if none are found, no lines\n  // are consumed.\n\n\n  function parseFileHeader(index) {\n    var fileHeader = /^(---|\\+\\+\\+)\\s+(.*)$/.exec(diffstr[i]);\n\n    if (fileHeader) {\n      var keyPrefix = fileHeader[1] === '---' ? 'old' : 'new';\n      var data = fileHeader[2].split('\\t', 2);\n      var fileName = data[0].replace(/\\\\\\\\/g, '\\\\');\n\n      if (/^\".*\"$/.test(fileName)) {\n        fileName = fileName.substr(1, fileName.length - 2);\n      }\n\n      index[keyPrefix + 'FileName'] = fileName;\n      index[keyPrefix + 'Header'] = (data[1] || '').trim();\n      i++;\n    }\n  } // Parses a hunk\n  // This assumes that we are at the start of a hunk.\n\n\n  function parseHunk() {\n    var chunkHeaderIndex = i,\n        chunkHeaderLine = diffstr[i++],\n        chunkHeader = chunkHeaderLine.split(/@@ -(\\d+)(?:,(\\d+))? \\+(\\d+)(?:,(\\d+))? @@/);\n    var hunk = {\n      oldStart: +chunkHeader[1],\n      oldLines: typeof chunkHeader[2] === 'undefined' ? 1 : +chunkHeader[2],\n      newStart: +chunkHeader[3],\n      newLines: typeof chunkHeader[4] === 'undefined' ? 1 : +chunkHeader[4],\n      lines: [],\n      linedelimiters: []\n    }; // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n\n    if (hunk.oldLines === 0) {\n      hunk.oldStart += 1;\n    }\n\n    if (hunk.newLines === 0) {\n      hunk.newStart += 1;\n    }\n\n    var addCount = 0,\n        removeCount = 0;\n\n    for (; i < diffstr.length; i++) {\n      // Lines starting with '---' could be mistaken for the \"remove line\" operation\n      // But they could be the header for the next file. Therefore prune such cases out.\n      if (diffstr[i].indexOf('--- ') === 0 && i + 2 < diffstr.length && diffstr[i + 1].indexOf('+++ ') === 0 && diffstr[i + 2].indexOf('@@') === 0) {\n        break;\n      }\n\n      var operation = diffstr[i].length == 0 && i != diffstr.length - 1 ? ' ' : diffstr[i][0];\n\n      if (operation === '+' || operation === '-' || operation === ' ' || operation === '\\\\') {\n        hunk.lines.push(diffstr[i]);\n        hunk.linedelimiters.push(delimiters[i] || '\\n');\n\n        if (operation === '+') {\n          addCount++;\n        } else if (operation === '-') {\n          removeCount++;\n        } else if (operation === ' ') {\n          addCount++;\n          removeCount++;\n        }\n      } else {\n        break;\n      }\n    } // Handle the empty block count case\n\n\n    if (!addCount && hunk.newLines === 1) {\n      hunk.newLines = 0;\n    }\n\n    if (!removeCount && hunk.oldLines === 1) {\n      hunk.oldLines = 0;\n    } // Perform optional sanity checking\n\n\n    if (options.strict) {\n      if (addCount !== hunk.newLines) {\n        throw new Error('Added line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n      }\n\n      if (removeCount !== hunk.oldLines) {\n        throw new Error('Removed line count did not match for hunk at line ' + (chunkHeaderIndex + 1));\n      }\n    }\n\n    return hunk;\n  }\n\n  while (i < diffstr.length) {\n    parseIndex();\n  }\n\n  return list;\n}\n\n// Iterator that traverses in the range of [min, max], stepping\n// by distance from a given start position. I.e. for [0, 4], with\n// start of 2, this will iterate 2, 3, 1, 4, 0.\nfunction distanceIterator (start, minLine, maxLine) {\n  var wantForward = true,\n      backwardExhausted = false,\n      forwardExhausted = false,\n      localOffset = 1;\n  return function iterator() {\n    if (wantForward && !forwardExhausted) {\n      if (backwardExhausted) {\n        localOffset++;\n      } else {\n        wantForward = false;\n      } // Check if trying to fit beyond text length, and if not, check it fits\n      // after offset location (or desired location on first iteration)\n\n\n      if (start + localOffset <= maxLine) {\n        return localOffset;\n      }\n\n      forwardExhausted = true;\n    }\n\n    if (!backwardExhausted) {\n      if (!forwardExhausted) {\n        wantForward = true;\n      } // Check if trying to fit before text beginning, and if not, check it fits\n      // before offset location\n\n\n      if (minLine <= start - localOffset) {\n        return -localOffset++;\n      }\n\n      backwardExhausted = true;\n      return iterator();\n    } // We tried to fit hunk before text beginning and beyond text length, then\n    // hunk can't fit on the text. Return undefined\n\n  };\n}\n\nfunction applyPatch(source, uniDiff) {\n  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n\n  if (Array.isArray(uniDiff)) {\n    if (uniDiff.length > 1) {\n      throw new Error('applyPatch only works with a single input.');\n    }\n\n    uniDiff = uniDiff[0];\n  } // Apply the diff to the input\n\n\n  var lines = source.split(/\\r\\n|[\\n\\v\\f\\r\\x85]/),\n      delimiters = source.match(/\\r\\n|[\\n\\v\\f\\r\\x85]/g) || [],\n      hunks = uniDiff.hunks,\n      compareLine = options.compareLine || function (lineNumber, line, operation, patchContent) {\n    return line === patchContent;\n  },\n      errorCount = 0,\n      fuzzFactor = options.fuzzFactor || 0,\n      minLine = 0,\n      offset = 0,\n      removeEOFNL,\n      addEOFNL;\n  /**\n   * Checks if the hunk exactly fits on the provided location\n   */\n\n\n  function hunkFits(hunk, toPos) {\n    for (var j = 0; j < hunk.lines.length; j++) {\n      var line = hunk.lines[j],\n          operation = line.length > 0 ? line[0] : ' ',\n          content = line.length > 0 ? line.substr(1) : line;\n\n      if (operation === ' ' || operation === '-') {\n        // Context sanity check\n        if (!compareLine(toPos + 1, lines[toPos], operation, content)) {\n          errorCount++;\n\n          if (errorCount > fuzzFactor) {\n            return false;\n          }\n        }\n\n        toPos++;\n      }\n    }\n\n    return true;\n  } // Search best fit offsets for each hunk based on the previous ones\n\n\n  for (var i = 0; i < hunks.length; i++) {\n    var hunk = hunks[i],\n        maxLine = lines.length - hunk.oldLines,\n        localOffset = 0,\n        toPos = offset + hunk.oldStart - 1;\n    var iterator = distanceIterator(toPos, minLine, maxLine);\n\n    for (; localOffset !== undefined; localOffset = iterator()) {\n      if (hunkFits(hunk, toPos + localOffset)) {\n        hunk.offset = offset += localOffset;\n        break;\n      }\n    }\n\n    if (localOffset === undefined) {\n      return false;\n    } // Set lower text limit to end of the current hunk, so next ones don't try\n    // to fit over already patched text\n\n\n    minLine = hunk.offset + hunk.oldStart + hunk.oldLines;\n  } // Apply patch hunks\n\n\n  var diffOffset = 0;\n\n  for (var _i = 0; _i < hunks.length; _i++) {\n    var _hunk = hunks[_i],\n        _toPos = _hunk.oldStart + _hunk.offset + diffOffset - 1;\n\n    diffOffset += _hunk.newLines - _hunk.oldLines;\n\n    for (var j = 0; j < _hunk.lines.length; j++) {\n      var line = _hunk.lines[j],\n          operation = line.length > 0 ? line[0] : ' ',\n          content = line.length > 0 ? line.substr(1) : line,\n          delimiter = _hunk.linedelimiters && _hunk.linedelimiters[j] || '\\n';\n\n      if (operation === ' ') {\n        _toPos++;\n      } else if (operation === '-') {\n        lines.splice(_toPos, 1);\n        delimiters.splice(_toPos, 1);\n        /* istanbul ignore else */\n      } else if (operation === '+') {\n        lines.splice(_toPos, 0, content);\n        delimiters.splice(_toPos, 0, delimiter);\n        _toPos++;\n      } else if (operation === '\\\\') {\n        var previousOperation = _hunk.lines[j - 1] ? _hunk.lines[j - 1][0] : null;\n\n        if (previousOperation === '+') {\n          removeEOFNL = true;\n        } else if (previousOperation === '-') {\n          addEOFNL = true;\n        }\n      }\n    }\n  } // Handle EOFNL insertion/removal\n\n\n  if (removeEOFNL) {\n    while (!lines[lines.length - 1]) {\n      lines.pop();\n      delimiters.pop();\n    }\n  } else if (addEOFNL) {\n    lines.push('');\n    delimiters.push('\\n');\n  }\n\n  for (var _k = 0; _k < lines.length - 1; _k++) {\n    lines[_k] = lines[_k] + delimiters[_k];\n  }\n\n  return lines.join('');\n} // Wrapper that supports multiple file patches via callbacks.\n\nfunction applyPatches(uniDiff, options) {\n  if (typeof uniDiff === 'string') {\n    uniDiff = parsePatch(uniDiff);\n  }\n\n  var currentIndex = 0;\n\n  function processIndex() {\n    var index = uniDiff[currentIndex++];\n\n    if (!index) {\n      return options.complete();\n    }\n\n    options.loadFile(index, function (err, data) {\n      if (err) {\n        return options.complete(err);\n      }\n\n      var updatedContent = applyPatch(data, index, options);\n      options.patched(index, updatedContent, function (err) {\n        if (err) {\n          return options.complete(err);\n        }\n\n        processIndex();\n      });\n    });\n  }\n\n  processIndex();\n}\n\nfunction structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  if (!options) {\n    options = {};\n  }\n\n  if (typeof options.context === 'undefined') {\n    options.context = 4;\n  }\n\n  var diff = diffLines(oldStr, newStr, options);\n\n  if (!diff) {\n    return;\n  }\n\n  diff.push({\n    value: '',\n    lines: []\n  }); // Append an empty value to make cleanup easier\n\n  function contextLines(lines) {\n    return lines.map(function (entry) {\n      return ' ' + entry;\n    });\n  }\n\n  var hunks = [];\n  var oldRangeStart = 0,\n      newRangeStart = 0,\n      curRange = [],\n      oldLine = 1,\n      newLine = 1;\n\n  var _loop = function _loop(i) {\n    var current = diff[i],\n        lines = current.lines || current.value.replace(/\\n$/, '').split('\\n');\n    current.lines = lines;\n\n    if (current.added || current.removed) {\n      var _curRange;\n\n      // If we have previous context, start with that\n      if (!oldRangeStart) {\n        var prev = diff[i - 1];\n        oldRangeStart = oldLine;\n        newRangeStart = newLine;\n\n        if (prev) {\n          curRange = options.context > 0 ? contextLines(prev.lines.slice(-options.context)) : [];\n          oldRangeStart -= curRange.length;\n          newRangeStart -= curRange.length;\n        }\n      } // Output our changes\n\n\n      (_curRange = curRange).push.apply(_curRange, _toConsumableArray(lines.map(function (entry) {\n        return (current.added ? '+' : '-') + entry;\n      }))); // Track the updated file position\n\n\n      if (current.added) {\n        newLine += lines.length;\n      } else {\n        oldLine += lines.length;\n      }\n    } else {\n      // Identical context lines. Track line changes\n      if (oldRangeStart) {\n        // Close out any changes that have been output (or join overlapping)\n        if (lines.length <= options.context * 2 && i < diff.length - 2) {\n          var _curRange2;\n\n          // Overlapping\n          (_curRange2 = curRange).push.apply(_curRange2, _toConsumableArray(contextLines(lines)));\n        } else {\n          var _curRange3;\n\n          // end the range and output\n          var contextSize = Math.min(lines.length, options.context);\n\n          (_curRange3 = curRange).push.apply(_curRange3, _toConsumableArray(contextLines(lines.slice(0, contextSize))));\n\n          var hunk = {\n            oldStart: oldRangeStart,\n            oldLines: oldLine - oldRangeStart + contextSize,\n            newStart: newRangeStart,\n            newLines: newLine - newRangeStart + contextSize,\n            lines: curRange\n          };\n\n          if (i >= diff.length - 2 && lines.length <= options.context) {\n            // EOF is inside this hunk\n            var oldEOFNewline = /\\n$/.test(oldStr);\n            var newEOFNewline = /\\n$/.test(newStr);\n            var noNlBeforeAdds = lines.length == 0 && curRange.length > hunk.oldLines;\n\n            if (!oldEOFNewline && noNlBeforeAdds && oldStr.length > 0) {\n              // special case: old has no eol and no trailing context; no-nl can end up before adds\n              // however, if the old file is empty, do not output the no-nl line\n              curRange.splice(hunk.oldLines, 0, '\\\\ No newline at end of file');\n            }\n\n            if (!oldEOFNewline && !noNlBeforeAdds || !newEOFNewline) {\n              curRange.push('\\\\ No newline at end of file');\n            }\n          }\n\n          hunks.push(hunk);\n          oldRangeStart = 0;\n          newRangeStart = 0;\n          curRange = [];\n        }\n      }\n\n      oldLine += lines.length;\n      newLine += lines.length;\n    }\n  };\n\n  for (var i = 0; i < diff.length; i++) {\n    _loop(i);\n  }\n\n  return {\n    oldFileName: oldFileName,\n    newFileName: newFileName,\n    oldHeader: oldHeader,\n    newHeader: newHeader,\n    hunks: hunks\n  };\n}\nfunction formatPatch(diff) {\n  if (Array.isArray(diff)) {\n    return diff.map(formatPatch).join('\\n');\n  }\n\n  var ret = [];\n\n  if (diff.oldFileName == diff.newFileName) {\n    ret.push('Index: ' + diff.oldFileName);\n  }\n\n  ret.push('===================================================================');\n  ret.push('--- ' + diff.oldFileName + (typeof diff.oldHeader === 'undefined' ? '' : '\\t' + diff.oldHeader));\n  ret.push('+++ ' + diff.newFileName + (typeof diff.newHeader === 'undefined' ? '' : '\\t' + diff.newHeader));\n\n  for (var i = 0; i < diff.hunks.length; i++) {\n    var hunk = diff.hunks[i]; // Unified Diff Format quirk: If the chunk size is 0,\n    // the first number is one lower than one would expect.\n    // https://www.artima.com/weblogs/viewpost.jsp?thread=164293\n\n    if (hunk.oldLines === 0) {\n      hunk.oldStart -= 1;\n    }\n\n    if (hunk.newLines === 0) {\n      hunk.newStart -= 1;\n    }\n\n    ret.push('@@ -' + hunk.oldStart + ',' + hunk.oldLines + ' +' + hunk.newStart + ',' + hunk.newLines + ' @@');\n    ret.push.apply(ret, hunk.lines);\n  }\n\n  return ret.join('\\n') + '\\n';\n}\nfunction createTwoFilesPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options) {\n  return formatPatch(structuredPatch(oldFileName, newFileName, oldStr, newStr, oldHeader, newHeader, options));\n}\nfunction createPatch(fileName, oldStr, newStr, oldHeader, newHeader, options) {\n  return createTwoFilesPatch(fileName, fileName, oldStr, newStr, oldHeader, newHeader, options);\n}\n\nfunction arrayEqual(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  return arrayStartsWith(a, b);\n}\nfunction arrayStartsWith(array, start) {\n  if (start.length > array.length) {\n    return false;\n  }\n\n  for (var i = 0; i < start.length; i++) {\n    if (start[i] !== array[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction calcLineCount(hunk) {\n  var _calcOldNewLineCount = calcOldNewLineCount(hunk.lines),\n      oldLines = _calcOldNewLineCount.oldLines,\n      newLines = _calcOldNewLineCount.newLines;\n\n  if (oldLines !== undefined) {\n    hunk.oldLines = oldLines;\n  } else {\n    delete hunk.oldLines;\n  }\n\n  if (newLines !== undefined) {\n    hunk.newLines = newLines;\n  } else {\n    delete hunk.newLines;\n  }\n}\nfunction merge(mine, theirs, base) {\n  mine = loadPatch(mine, base);\n  theirs = loadPatch(theirs, base);\n  var ret = {}; // For index we just let it pass through as it doesn't have any necessary meaning.\n  // Leaving sanity checks on this to the API consumer that may know more about the\n  // meaning in their own context.\n\n  if (mine.index || theirs.index) {\n    ret.index = mine.index || theirs.index;\n  }\n\n  if (mine.newFileName || theirs.newFileName) {\n    if (!fileNameChanged(mine)) {\n      // No header or no change in ours, use theirs (and ours if theirs does not exist)\n      ret.oldFileName = theirs.oldFileName || mine.oldFileName;\n      ret.newFileName = theirs.newFileName || mine.newFileName;\n      ret.oldHeader = theirs.oldHeader || mine.oldHeader;\n      ret.newHeader = theirs.newHeader || mine.newHeader;\n    } else if (!fileNameChanged(theirs)) {\n      // No header or no change in theirs, use ours\n      ret.oldFileName = mine.oldFileName;\n      ret.newFileName = mine.newFileName;\n      ret.oldHeader = mine.oldHeader;\n      ret.newHeader = mine.newHeader;\n    } else {\n      // Both changed... figure it out\n      ret.oldFileName = selectField(ret, mine.oldFileName, theirs.oldFileName);\n      ret.newFileName = selectField(ret, mine.newFileName, theirs.newFileName);\n      ret.oldHeader = selectField(ret, mine.oldHeader, theirs.oldHeader);\n      ret.newHeader = selectField(ret, mine.newHeader, theirs.newHeader);\n    }\n  }\n\n  ret.hunks = [];\n  var mineIndex = 0,\n      theirsIndex = 0,\n      mineOffset = 0,\n      theirsOffset = 0;\n\n  while (mineIndex < mine.hunks.length || theirsIndex < theirs.hunks.length) {\n    var mineCurrent = mine.hunks[mineIndex] || {\n      oldStart: Infinity\n    },\n        theirsCurrent = theirs.hunks[theirsIndex] || {\n      oldStart: Infinity\n    };\n\n    if (hunkBefore(mineCurrent, theirsCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(mineCurrent, mineOffset));\n      mineIndex++;\n      theirsOffset += mineCurrent.newLines - mineCurrent.oldLines;\n    } else if (hunkBefore(theirsCurrent, mineCurrent)) {\n      // This patch does not overlap with any of the others, yay.\n      ret.hunks.push(cloneHunk(theirsCurrent, theirsOffset));\n      theirsIndex++;\n      mineOffset += theirsCurrent.newLines - theirsCurrent.oldLines;\n    } else {\n      // Overlap, merge as best we can\n      var mergedHunk = {\n        oldStart: Math.min(mineCurrent.oldStart, theirsCurrent.oldStart),\n        oldLines: 0,\n        newStart: Math.min(mineCurrent.newStart + mineOffset, theirsCurrent.oldStart + theirsOffset),\n        newLines: 0,\n        lines: []\n      };\n      mergeLines(mergedHunk, mineCurrent.oldStart, mineCurrent.lines, theirsCurrent.oldStart, theirsCurrent.lines);\n      theirsIndex++;\n      mineIndex++;\n      ret.hunks.push(mergedHunk);\n    }\n  }\n\n  return ret;\n}\n\nfunction loadPatch(param, base) {\n  if (typeof param === 'string') {\n    if (/^@@/m.test(param) || /^Index:/m.test(param)) {\n      return parsePatch(param)[0];\n    }\n\n    if (!base) {\n      throw new Error('Must provide a base reference or pass in a patch');\n    }\n\n    return structuredPatch(undefined, undefined, base, param);\n  }\n\n  return param;\n}\n\nfunction fileNameChanged(patch) {\n  return patch.newFileName && patch.newFileName !== patch.oldFileName;\n}\n\nfunction selectField(index, mine, theirs) {\n  if (mine === theirs) {\n    return mine;\n  } else {\n    index.conflict = true;\n    return {\n      mine: mine,\n      theirs: theirs\n    };\n  }\n}\n\nfunction hunkBefore(test, check) {\n  return test.oldStart < check.oldStart && test.oldStart + test.oldLines < check.oldStart;\n}\n\nfunction cloneHunk(hunk, offset) {\n  return {\n    oldStart: hunk.oldStart,\n    oldLines: hunk.oldLines,\n    newStart: hunk.newStart + offset,\n    newLines: hunk.newLines,\n    lines: hunk.lines\n  };\n}\n\nfunction mergeLines(hunk, mineOffset, mineLines, theirOffset, theirLines) {\n  // This will generally result in a conflicted hunk, but there are cases where the context\n  // is the only overlap where we can successfully merge the content here.\n  var mine = {\n    offset: mineOffset,\n    lines: mineLines,\n    index: 0\n  },\n      their = {\n    offset: theirOffset,\n    lines: theirLines,\n    index: 0\n  }; // Handle any leading content\n\n  insertLeading(hunk, mine, their);\n  insertLeading(hunk, their, mine); // Now in the overlap content. Scan through and select the best changes from each.\n\n  while (mine.index < mine.lines.length && their.index < their.lines.length) {\n    var mineCurrent = mine.lines[mine.index],\n        theirCurrent = their.lines[their.index];\n\n    if ((mineCurrent[0] === '-' || mineCurrent[0] === '+') && (theirCurrent[0] === '-' || theirCurrent[0] === '+')) {\n      // Both modified ...\n      mutualChange(hunk, mine, their);\n    } else if (mineCurrent[0] === '+' && theirCurrent[0] === ' ') {\n      var _hunk$lines;\n\n      // Mine inserted\n      (_hunk$lines = hunk.lines).push.apply(_hunk$lines, _toConsumableArray(collectChange(mine)));\n    } else if (theirCurrent[0] === '+' && mineCurrent[0] === ' ') {\n      var _hunk$lines2;\n\n      // Theirs inserted\n      (_hunk$lines2 = hunk.lines).push.apply(_hunk$lines2, _toConsumableArray(collectChange(their)));\n    } else if (mineCurrent[0] === '-' && theirCurrent[0] === ' ') {\n      // Mine removed or edited\n      removal(hunk, mine, their);\n    } else if (theirCurrent[0] === '-' && mineCurrent[0] === ' ') {\n      // Their removed or edited\n      removal(hunk, their, mine, true);\n    } else if (mineCurrent === theirCurrent) {\n      // Context identity\n      hunk.lines.push(mineCurrent);\n      mine.index++;\n      their.index++;\n    } else {\n      // Context mismatch\n      conflict(hunk, collectChange(mine), collectChange(their));\n    }\n  } // Now push anything that may be remaining\n\n\n  insertTrailing(hunk, mine);\n  insertTrailing(hunk, their);\n  calcLineCount(hunk);\n}\n\nfunction mutualChange(hunk, mine, their) {\n  var myChanges = collectChange(mine),\n      theirChanges = collectChange(their);\n\n  if (allRemoves(myChanges) && allRemoves(theirChanges)) {\n    // Special case for remove changes that are supersets of one another\n    if (arrayStartsWith(myChanges, theirChanges) && skipRemoveSuperset(their, myChanges, myChanges.length - theirChanges.length)) {\n      var _hunk$lines3;\n\n      (_hunk$lines3 = hunk.lines).push.apply(_hunk$lines3, _toConsumableArray(myChanges));\n\n      return;\n    } else if (arrayStartsWith(theirChanges, myChanges) && skipRemoveSuperset(mine, theirChanges, theirChanges.length - myChanges.length)) {\n      var _hunk$lines4;\n\n      (_hunk$lines4 = hunk.lines).push.apply(_hunk$lines4, _toConsumableArray(theirChanges));\n\n      return;\n    }\n  } else if (arrayEqual(myChanges, theirChanges)) {\n    var _hunk$lines5;\n\n    (_hunk$lines5 = hunk.lines).push.apply(_hunk$lines5, _toConsumableArray(myChanges));\n\n    return;\n  }\n\n  conflict(hunk, myChanges, theirChanges);\n}\n\nfunction removal(hunk, mine, their, swap) {\n  var myChanges = collectChange(mine),\n      theirChanges = collectContext(their, myChanges);\n\n  if (theirChanges.merged) {\n    var _hunk$lines6;\n\n    (_hunk$lines6 = hunk.lines).push.apply(_hunk$lines6, _toConsumableArray(theirChanges.merged));\n  } else {\n    conflict(hunk, swap ? theirChanges : myChanges, swap ? myChanges : theirChanges);\n  }\n}\n\nfunction conflict(hunk, mine, their) {\n  hunk.conflict = true;\n  hunk.lines.push({\n    conflict: true,\n    mine: mine,\n    theirs: their\n  });\n}\n\nfunction insertLeading(hunk, insert, their) {\n  while (insert.offset < their.offset && insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n    insert.offset++;\n  }\n}\n\nfunction insertTrailing(hunk, insert) {\n  while (insert.index < insert.lines.length) {\n    var line = insert.lines[insert.index++];\n    hunk.lines.push(line);\n  }\n}\n\nfunction collectChange(state) {\n  var ret = [],\n      operation = state.lines[state.index][0];\n\n  while (state.index < state.lines.length) {\n    var line = state.lines[state.index]; // Group additions that are immediately after subtractions and treat them as one \"atomic\" modify change.\n\n    if (operation === '-' && line[0] === '+') {\n      operation = '+';\n    }\n\n    if (operation === line[0]) {\n      ret.push(line);\n      state.index++;\n    } else {\n      break;\n    }\n  }\n\n  return ret;\n}\n\nfunction collectContext(state, matchChanges) {\n  var changes = [],\n      merged = [],\n      matchIndex = 0,\n      contextChanges = false,\n      conflicted = false;\n\n  while (matchIndex < matchChanges.length && state.index < state.lines.length) {\n    var change = state.lines[state.index],\n        match = matchChanges[matchIndex]; // Once we've hit our add, then we are done\n\n    if (match[0] === '+') {\n      break;\n    }\n\n    contextChanges = contextChanges || change[0] !== ' ';\n    merged.push(match);\n    matchIndex++; // Consume any additions in the other block as a conflict to attempt\n    // to pull in the remaining context after this\n\n    if (change[0] === '+') {\n      conflicted = true;\n\n      while (change[0] === '+') {\n        changes.push(change);\n        change = state.lines[++state.index];\n      }\n    }\n\n    if (match.substr(1) === change.substr(1)) {\n      changes.push(change);\n      state.index++;\n    } else {\n      conflicted = true;\n    }\n  }\n\n  if ((matchChanges[matchIndex] || '')[0] === '+' && contextChanges) {\n    conflicted = true;\n  }\n\n  if (conflicted) {\n    return changes;\n  }\n\n  while (matchIndex < matchChanges.length) {\n    merged.push(matchChanges[matchIndex++]);\n  }\n\n  return {\n    merged: merged,\n    changes: changes\n  };\n}\n\nfunction allRemoves(changes) {\n  return changes.reduce(function (prev, change) {\n    return prev && change[0] === '-';\n  }, true);\n}\n\nfunction skipRemoveSuperset(state, removeChanges, delta) {\n  for (var i = 0; i < delta; i++) {\n    var changeContent = removeChanges[removeChanges.length - delta + i].substr(1);\n\n    if (state.lines[state.index + i] !== ' ' + changeContent) {\n      return false;\n    }\n  }\n\n  state.index += delta;\n  return true;\n}\n\nfunction calcOldNewLineCount(lines) {\n  var oldLines = 0;\n  var newLines = 0;\n  lines.forEach(function (line) {\n    if (typeof line !== 'string') {\n      var myCount = calcOldNewLineCount(line.mine);\n      var theirCount = calcOldNewLineCount(line.theirs);\n\n      if (oldLines !== undefined) {\n        if (myCount.oldLines === theirCount.oldLines) {\n          oldLines += myCount.oldLines;\n        } else {\n          oldLines = undefined;\n        }\n      }\n\n      if (newLines !== undefined) {\n        if (myCount.newLines === theirCount.newLines) {\n          newLines += myCount.newLines;\n        } else {\n          newLines = undefined;\n        }\n      }\n    } else {\n      if (newLines !== undefined && (line[0] === '+' || line[0] === ' ')) {\n        newLines++;\n      }\n\n      if (oldLines !== undefined && (line[0] === '-' || line[0] === ' ')) {\n        oldLines++;\n      }\n    }\n  });\n  return {\n    oldLines: oldLines,\n    newLines: newLines\n  };\n}\n\nfunction reversePatch(structuredPatch) {\n  if (Array.isArray(structuredPatch)) {\n    return structuredPatch.map(reversePatch).reverse();\n  }\n\n  return _objectSpread2(_objectSpread2({}, structuredPatch), {}, {\n    oldFileName: structuredPatch.newFileName,\n    oldHeader: structuredPatch.newHeader,\n    newFileName: structuredPatch.oldFileName,\n    newHeader: structuredPatch.oldHeader,\n    hunks: structuredPatch.hunks.map(function (hunk) {\n      return {\n        oldLines: hunk.newLines,\n        oldStart: hunk.newStart,\n        newLines: hunk.oldLines,\n        newStart: hunk.oldStart,\n        linedelimiters: hunk.linedelimiters,\n        lines: hunk.lines.map(function (l) {\n          if (l.startsWith('-')) {\n            return \"+\".concat(l.slice(1));\n          }\n\n          if (l.startsWith('+')) {\n            return \"-\".concat(l.slice(1));\n          }\n\n          return l;\n        })\n      };\n    })\n  });\n}\n\n// See: http://code.google.com/p/google-diff-match-patch/wiki/API\nfunction convertChangesToDMP(changes) {\n  var ret = [],\n      change,\n      operation;\n\n  for (var i = 0; i < changes.length; i++) {\n    change = changes[i];\n\n    if (change.added) {\n      operation = 1;\n    } else if (change.removed) {\n      operation = -1;\n    } else {\n      operation = 0;\n    }\n\n    ret.push([operation, change.value]);\n  }\n\n  return ret;\n}\n\nfunction convertChangesToXML(changes) {\n  var ret = [];\n\n  for (var i = 0; i < changes.length; i++) {\n    var change = changes[i];\n\n    if (change.added) {\n      ret.push('<ins>');\n    } else if (change.removed) {\n      ret.push('<del>');\n    }\n\n    ret.push(escapeHTML(change.value));\n\n    if (change.added) {\n      ret.push('</ins>');\n    } else if (change.removed) {\n      ret.push('</del>');\n    }\n  }\n\n  return ret.join('');\n}\n\nfunction escapeHTML(s) {\n  var n = s;\n  n = n.replace(/&/g, '&amp;');\n  n = n.replace(/</g, '&lt;');\n  n = n.replace(/>/g, '&gt;');\n  n = n.replace(/\"/g, '&quot;');\n  return n;\n}\n\nexport { Diff, applyPatch, applyPatches, canonicalize, convertChangesToDMP, convertChangesToXML, createPatch, createTwoFilesPatch, diffArrays, diffChars, diffCss, diffJson, diffLines, diffSentences, diffTrimmedLines, diffWords, diffWordsWithSpace, formatPatch, merge, parsePatch, reversePatch, structuredPatch };\n"], "mappings": "AAAA,SAASA,IAAIA,CAAA,EAAG,CAAC;AACjBA,IAAI,CAACC,SAAS,GAAG;EACfC,IAAI,EAAE,SAASA,IAAIA,CAACC,SAAS,EAAEC,SAAS,EAAE;IACxC,IAAIC,gBAAgB;IAEpB,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACpF,IAAIG,QAAQ,GAAGJ,OAAO,CAACI,QAAQ;IAE/B,IAAI,OAAOJ,OAAO,KAAK,UAAU,EAAE;MACjCI,QAAQ,GAAGJ,OAAO;MAClBA,OAAO,GAAG,CAAC,CAAC;IACd;IAEA,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAIK,IAAI,GAAG,IAAI;IAEf,SAASC,IAAIA,CAACC,KAAK,EAAE;MACnB,IAAIH,QAAQ,EAAE;QACZI,UAAU,CAAC,YAAY;UACrBJ,QAAQ,CAACD,SAAS,EAAEI,KAAK,CAAC;QAC5B,CAAC,EAAE,CAAC,CAAC;QACL,OAAO,IAAI;MACb,CAAC,MAAM;QACL,OAAOA,KAAK;MACd;IACF,CAAC,CAAC;;IAGFV,SAAS,GAAG,IAAI,CAACY,SAAS,CAACZ,SAAS,CAAC;IACrCC,SAAS,GAAG,IAAI,CAACW,SAAS,CAACX,SAAS,CAAC;IACrCD,SAAS,GAAG,IAAI,CAACa,WAAW,CAAC,IAAI,CAACC,QAAQ,CAACd,SAAS,CAAC,CAAC;IACtDC,SAAS,GAAG,IAAI,CAACY,WAAW,CAAC,IAAI,CAACC,QAAQ,CAACb,SAAS,CAAC,CAAC;IACtD,IAAIc,MAAM,GAAGd,SAAS,CAACI,MAAM;MACzBW,MAAM,GAAGhB,SAAS,CAACK,MAAM;IAC7B,IAAIY,UAAU,GAAG,CAAC;IAClB,IAAIC,aAAa,GAAGH,MAAM,GAAGC,MAAM;IAEnC,IAAIb,OAAO,CAACe,aAAa,EAAE;MACzBA,aAAa,GAAGC,IAAI,CAACC,GAAG,CAACF,aAAa,EAAEf,OAAO,CAACe,aAAa,CAAC;IAChE;IAEA,IAAIG,gBAAgB,GAAG,CAACnB,gBAAgB,GAAGC,OAAO,CAACmB,OAAO,MAAM,IAAI,IAAIpB,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGqB,QAAQ;IACjI,IAAIC,mBAAmB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGL,gBAAgB;IACvD,IAAIM,QAAQ,GAAG,CAAC;MACdC,MAAM,EAAE,CAAC,CAAC;MACVC,aAAa,EAAEvB;IACjB,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAIwB,MAAM,GAAG,IAAI,CAACC,aAAa,CAACJ,QAAQ,CAAC,CAAC,CAAC,EAAE1B,SAAS,EAAED,SAAS,EAAE,CAAC,CAAC;IAErE,IAAI2B,QAAQ,CAAC,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,IAAIZ,MAAM,IAAIc,MAAM,GAAG,CAAC,IAAIf,MAAM,EAAE;MAC5D;MACA,OAAON,IAAI,CAAC,CAAC;QACXC,KAAK,EAAE,IAAI,CAACsB,IAAI,CAAC/B,SAAS,CAAC;QAC3BgC,KAAK,EAAEhC,SAAS,CAACI;MACnB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAGA,IAAI6B,qBAAqB,GAAG,CAACX,QAAQ;MACjCY,qBAAqB,GAAGZ,QAAQ,CAAC,CAAC;;IAEtC,SAASa,cAAcA,CAAA,EAAG;MACxB,KAAK,IAAIC,YAAY,GAAGlB,IAAI,CAACmB,GAAG,CAACJ,qBAAqB,EAAE,CAACjB,UAAU,CAAC,EAAEoB,YAAY,IAAIlB,IAAI,CAACC,GAAG,CAACe,qBAAqB,EAAElB,UAAU,CAAC,EAAEoB,YAAY,IAAI,CAAC,EAAE;QACpJ,IAAIE,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAIC,UAAU,GAAGb,QAAQ,CAACU,YAAY,GAAG,CAAC,CAAC;UACvCI,OAAO,GAAGd,QAAQ,CAACU,YAAY,GAAG,CAAC,CAAC;QAExC,IAAIG,UAAU,EAAE;UACd;UACAb,QAAQ,CAACU,YAAY,GAAG,CAAC,CAAC,GAAG/B,SAAS;QACxC;QAEA,IAAIoC,MAAM,GAAG,KAAK;QAElB,IAAID,OAAO,EAAE;UACX;UACA,IAAIE,aAAa,GAAGF,OAAO,CAACb,MAAM,GAAGS,YAAY;UACjDK,MAAM,GAAGD,OAAO,IAAI,CAAC,IAAIE,aAAa,IAAIA,aAAa,GAAG5B,MAAM;QAClE;QAEA,IAAI6B,SAAS,GAAGJ,UAAU,IAAIA,UAAU,CAACZ,MAAM,GAAG,CAAC,GAAGZ,MAAM;QAE5D,IAAI,CAAC0B,MAAM,IAAI,CAACE,SAAS,EAAE;UACzB;UACAjB,QAAQ,CAACU,YAAY,CAAC,GAAG/B,SAAS;UAClC;QACF,CAAC,CAAC;QACF;QACA;QACA;QACA;;QAGA,IAAI,CAACsC,SAAS,IAAIF,MAAM,IAAIF,UAAU,CAACZ,MAAM,GAAG,CAAC,GAAGa,OAAO,CAACb,MAAM,EAAE;UAClEW,QAAQ,GAAG/B,IAAI,CAACqC,SAAS,CAACJ,OAAO,EAAE,IAAI,EAAEnC,SAAS,EAAE,CAAC,CAAC;QACxD,CAAC,MAAM;UACLiC,QAAQ,GAAG/B,IAAI,CAACqC,SAAS,CAACL,UAAU,EAAElC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC3D;QAEAwB,MAAM,GAAGtB,IAAI,CAACuB,aAAa,CAACQ,QAAQ,EAAEtC,SAAS,EAAED,SAAS,EAAEqC,YAAY,CAAC;QAEzE,IAAIE,QAAQ,CAACX,MAAM,GAAG,CAAC,IAAIZ,MAAM,IAAIc,MAAM,GAAG,CAAC,IAAIf,MAAM,EAAE;UACzD;UACA,OAAON,IAAI,CAACqC,WAAW,CAACtC,IAAI,EAAE+B,QAAQ,CAACV,aAAa,EAAE5B,SAAS,EAAED,SAAS,EAAEQ,IAAI,CAACuC,eAAe,CAAC,CAAC;QACpG,CAAC,MAAM;UACLpB,QAAQ,CAACU,YAAY,CAAC,GAAGE,QAAQ;UAEjC,IAAIA,QAAQ,CAACX,MAAM,GAAG,CAAC,IAAIZ,MAAM,EAAE;YACjCmB,qBAAqB,GAAGhB,IAAI,CAACC,GAAG,CAACe,qBAAqB,EAAEE,YAAY,GAAG,CAAC,CAAC;UAC3E;UAEA,IAAIP,MAAM,GAAG,CAAC,IAAIf,MAAM,EAAE;YACxBmB,qBAAqB,GAAGf,IAAI,CAACmB,GAAG,CAACJ,qBAAqB,EAAEG,YAAY,GAAG,CAAC,CAAC;UAC3E;QACF;MACF;MAEApB,UAAU,EAAE;IACd,CAAC,CAAC;IACF;IACA;IACA;;IAGA,IAAIV,QAAQ,EAAE;MACZ,CAAC,SAASyC,IAAIA,CAAA,EAAG;QACfrC,UAAU,CAAC,YAAY;UACrB,IAAIM,UAAU,GAAGC,aAAa,IAAIO,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,mBAAmB,EAAE;YAClE,OAAOjB,QAAQ,CAAC,CAAC;UACnB;UAEA,IAAI,CAAC6B,cAAc,CAAC,CAAC,EAAE;YACrBY,IAAI,CAAC,CAAC;UACR;QACF,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,EAAE,CAAC;IACN,CAAC,MAAM;MACL,OAAO/B,UAAU,IAAIC,aAAa,IAAIO,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIF,mBAAmB,EAAE;QACvE,IAAIyB,GAAG,GAAGb,cAAc,CAAC,CAAC;QAE1B,IAAIa,GAAG,EAAE;UACP,OAAOA,GAAG;QACZ;MACF;IACF;EACF,CAAC;EACDJ,SAAS,EAAE,SAASA,SAASA,CAACK,IAAI,EAAEC,KAAK,EAAEC,OAAO,EAAEC,SAAS,EAAE;IAC7D,IAAIC,IAAI,GAAGJ,IAAI,CAACrB,aAAa;IAE7B,IAAIyB,IAAI,IAAIA,IAAI,CAACH,KAAK,KAAKA,KAAK,IAAIG,IAAI,CAACF,OAAO,KAAKA,OAAO,EAAE;MAC5D,OAAO;QACLxB,MAAM,EAAEsB,IAAI,CAACtB,MAAM,GAAGyB,SAAS;QAC/BxB,aAAa,EAAE;UACbI,KAAK,EAAEqB,IAAI,CAACrB,KAAK,GAAG,CAAC;UACrBkB,KAAK,EAAEA,KAAK;UACZC,OAAO,EAAEA,OAAO;UAChBG,iBAAiB,EAAED,IAAI,CAACC;QAC1B;MACF,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACL3B,MAAM,EAAEsB,IAAI,CAACtB,MAAM,GAAGyB,SAAS;QAC/BxB,aAAa,EAAE;UACbI,KAAK,EAAE,CAAC;UACRkB,KAAK,EAAEA,KAAK;UACZC,OAAO,EAAEA,OAAO;UAChBG,iBAAiB,EAAED;QACrB;MACF,CAAC;IACH;EACF,CAAC;EACDvB,aAAa,EAAE,SAASA,aAAaA,CAACQ,QAAQ,EAAEtC,SAAS,EAAED,SAAS,EAAEqC,YAAY,EAAE;IAClF,IAAItB,MAAM,GAAGd,SAAS,CAACI,MAAM;MACzBW,MAAM,GAAGhB,SAAS,CAACK,MAAM;MACzBuB,MAAM,GAAGW,QAAQ,CAACX,MAAM;MACxBE,MAAM,GAAGF,MAAM,GAAGS,YAAY;MAC9BmB,WAAW,GAAG,CAAC;IAEnB,OAAO1B,MAAM,GAAG,CAAC,GAAGf,MAAM,IAAIa,MAAM,GAAG,CAAC,GAAGZ,MAAM,IAAI,IAAI,CAACyC,MAAM,CAACxD,SAAS,CAAC6B,MAAM,GAAG,CAAC,CAAC,EAAE9B,SAAS,CAAC4B,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;MAC9GE,MAAM,EAAE;MACRF,MAAM,EAAE;MACR4B,WAAW,EAAE;IACf;IAEA,IAAIA,WAAW,EAAE;MACfjB,QAAQ,CAACV,aAAa,GAAG;QACvBI,KAAK,EAAEuB,WAAW;QAClBD,iBAAiB,EAAEhB,QAAQ,CAACV;MAC9B,CAAC;IACH;IAEAU,QAAQ,CAACX,MAAM,GAAGA,MAAM;IACxB,OAAOE,MAAM;EACf,CAAC;EACD2B,MAAM,EAAE,SAASA,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACnC,IAAI,IAAI,CAACxD,OAAO,CAACyD,UAAU,EAAE;MAC3B,OAAO,IAAI,CAACzD,OAAO,CAACyD,UAAU,CAACF,IAAI,EAAEC,KAAK,CAAC;IAC7C,CAAC,MAAM;MACL,OAAOD,IAAI,KAAKC,KAAK,IAAI,IAAI,CAACxD,OAAO,CAAC0D,UAAU,IAAIH,IAAI,CAACI,WAAW,CAAC,CAAC,KAAKH,KAAK,CAACG,WAAW,CAAC,CAAC;IAChG;EACF,CAAC;EACDjD,WAAW,EAAE,SAASA,WAAWA,CAACkD,KAAK,EAAE;IACvC,IAAId,GAAG,GAAG,EAAE;IAEZ,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAAC1D,MAAM,EAAE2D,CAAC,EAAE,EAAE;MACrC,IAAID,KAAK,CAACC,CAAC,CAAC,EAAE;QACZf,GAAG,CAACgB,IAAI,CAACF,KAAK,CAACC,CAAC,CAAC,CAAC;MACpB;IACF;IAEA,OAAOf,GAAG;EACZ,CAAC;EACDrC,SAAS,EAAE,SAASA,SAASA,CAACF,KAAK,EAAE;IACnC,OAAOA,KAAK;EACd,CAAC;EACDI,QAAQ,EAAE,SAASA,QAAQA,CAACJ,KAAK,EAAE;IACjC,OAAOA,KAAK,CAACwD,KAAK,CAAC,EAAE,CAAC;EACxB,CAAC;EACDlC,IAAI,EAAE,SAASA,IAAIA,CAACmC,KAAK,EAAE;IACzB,OAAOA,KAAK,CAACnC,IAAI,CAAC,EAAE,CAAC;EACvB;AACF,CAAC;AAED,SAASc,WAAWA,CAAC/C,IAAI,EAAE8B,aAAa,EAAE5B,SAAS,EAAED,SAAS,EAAE+C,eAAe,EAAE;EAC/E;EACA;EACA,IAAIqB,UAAU,GAAG,EAAE;EACnB,IAAIC,aAAa;EAEjB,OAAOxC,aAAa,EAAE;IACpBuC,UAAU,CAACH,IAAI,CAACpC,aAAa,CAAC;IAC9BwC,aAAa,GAAGxC,aAAa,CAAC0B,iBAAiB;IAC/C,OAAO1B,aAAa,CAAC0B,iBAAiB;IACtC1B,aAAa,GAAGwC,aAAa;EAC/B;EAEAD,UAAU,CAACE,OAAO,CAAC,CAAC;EACpB,IAAIC,YAAY,GAAG,CAAC;IAChBC,YAAY,GAAGJ,UAAU,CAAC/D,MAAM;IAChCyB,MAAM,GAAG,CAAC;IACVF,MAAM,GAAG,CAAC;EAEd,OAAO2C,YAAY,GAAGC,YAAY,EAAED,YAAY,EAAE,EAAE;IAClD,IAAIE,SAAS,GAAGL,UAAU,CAACG,YAAY,CAAC;IAExC,IAAI,CAACE,SAAS,CAACrB,OAAO,EAAE;MACtB,IAAI,CAACqB,SAAS,CAACtB,KAAK,IAAIJ,eAAe,EAAE;QACvC,IAAIrC,KAAK,GAAGT,SAAS,CAACyE,KAAK,CAAC5C,MAAM,EAAEA,MAAM,GAAG2C,SAAS,CAACxC,KAAK,CAAC;QAC7DvB,KAAK,GAAGA,KAAK,CAACiE,GAAG,CAAC,UAAUjE,KAAK,EAAEsD,CAAC,EAAE;UACpC,IAAIY,QAAQ,GAAG5E,SAAS,CAAC4B,MAAM,GAAGoC,CAAC,CAAC;UACpC,OAAOY,QAAQ,CAACvE,MAAM,GAAGK,KAAK,CAACL,MAAM,GAAGuE,QAAQ,GAAGlE,KAAK;QAC1D,CAAC,CAAC;QACF+D,SAAS,CAAC/D,KAAK,GAAGX,IAAI,CAACiC,IAAI,CAACtB,KAAK,CAAC;MACpC,CAAC,MAAM;QACL+D,SAAS,CAAC/D,KAAK,GAAGX,IAAI,CAACiC,IAAI,CAAC/B,SAAS,CAACyE,KAAK,CAAC5C,MAAM,EAAEA,MAAM,GAAG2C,SAAS,CAACxC,KAAK,CAAC,CAAC;MAChF;MAEAH,MAAM,IAAI2C,SAAS,CAACxC,KAAK,CAAC,CAAC;;MAE3B,IAAI,CAACwC,SAAS,CAACtB,KAAK,EAAE;QACpBvB,MAAM,IAAI6C,SAAS,CAACxC,KAAK;MAC3B;IACF,CAAC,MAAM;MACLwC,SAAS,CAAC/D,KAAK,GAAGX,IAAI,CAACiC,IAAI,CAAChC,SAAS,CAAC0E,KAAK,CAAC9C,MAAM,EAAEA,MAAM,GAAG6C,SAAS,CAACxC,KAAK,CAAC,CAAC;MAC9EL,MAAM,IAAI6C,SAAS,CAACxC,KAAK,CAAC,CAAC;MAC3B;MACA;;MAEA,IAAIsC,YAAY,IAAIH,UAAU,CAACG,YAAY,GAAG,CAAC,CAAC,CAACpB,KAAK,EAAE;QACtD,IAAI0B,GAAG,GAAGT,UAAU,CAACG,YAAY,GAAG,CAAC,CAAC;QACtCH,UAAU,CAACG,YAAY,GAAG,CAAC,CAAC,GAAGH,UAAU,CAACG,YAAY,CAAC;QACvDH,UAAU,CAACG,YAAY,CAAC,GAAGM,GAAG;MAChC;IACF;EACF,CAAC,CAAC;EACF;EACA;;EAGA,IAAIC,cAAc,GAAGV,UAAU,CAACI,YAAY,GAAG,CAAC,CAAC;EAEjD,IAAIA,YAAY,GAAG,CAAC,IAAI,OAAOM,cAAc,CAACpE,KAAK,KAAK,QAAQ,KAAKoE,cAAc,CAAC3B,KAAK,IAAI2B,cAAc,CAAC1B,OAAO,CAAC,IAAIrD,IAAI,CAAC0D,MAAM,CAAC,EAAE,EAAEqB,cAAc,CAACpE,KAAK,CAAC,EAAE;IAC7J0D,UAAU,CAACI,YAAY,GAAG,CAAC,CAAC,CAAC9D,KAAK,IAAIoE,cAAc,CAACpE,KAAK;IAC1D0D,UAAU,CAACW,GAAG,CAAC,CAAC;EAClB;EAEA,OAAOX,UAAU;AACnB;AAEA,IAAIY,aAAa,GAAG,IAAInF,IAAI,CAAC,CAAC;AAC9B,SAASoF,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAEhF,OAAO,EAAE;EAC1C,OAAO6E,aAAa,CAACjF,IAAI,CAACmF,MAAM,EAAEC,MAAM,EAAEhF,OAAO,CAAC;AACpD;AAEA,SAASiF,eAAeA,CAACjF,OAAO,EAAEkF,QAAQ,EAAE;EAC1C,IAAI,OAAOlF,OAAO,KAAK,UAAU,EAAE;IACjCkF,QAAQ,CAAC9E,QAAQ,GAAGJ,OAAO;EAC7B,CAAC,MAAM,IAAIA,OAAO,EAAE;IAClB,KAAK,IAAImF,IAAI,IAAInF,OAAO,EAAE;MACxB;MACA,IAAIA,OAAO,CAACoF,cAAc,CAACD,IAAI,CAAC,EAAE;QAChCD,QAAQ,CAACC,IAAI,CAAC,GAAGnF,OAAO,CAACmF,IAAI,CAAC;MAChC;IACF;EACF;EAEA,OAAOD,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIG,iBAAiB,GAAG,+DAA+D;AACvF,IAAIC,YAAY,GAAG,IAAI;AACvB,IAAIC,QAAQ,GAAG,IAAI7F,IAAI,CAAC,CAAC;AAEzB6F,QAAQ,CAACjC,MAAM,GAAG,UAAUC,IAAI,EAAEC,KAAK,EAAE;EACvC,IAAI,IAAI,CAACxD,OAAO,CAAC0D,UAAU,EAAE;IAC3BH,IAAI,GAAGA,IAAI,CAACI,WAAW,CAAC,CAAC;IACzBH,KAAK,GAAGA,KAAK,CAACG,WAAW,CAAC,CAAC;EAC7B;EAEA,OAAOJ,IAAI,KAAKC,KAAK,IAAI,IAAI,CAACxD,OAAO,CAACwF,gBAAgB,IAAI,CAACF,YAAY,CAACG,IAAI,CAAClC,IAAI,CAAC,IAAI,CAAC+B,YAAY,CAACG,IAAI,CAACjC,KAAK,CAAC;AACjH,CAAC;AAED+B,QAAQ,CAAC5E,QAAQ,GAAG,UAAUJ,KAAK,EAAE;EACnC;EACA,IAAImF,MAAM,GAAGnF,KAAK,CAACwD,KAAK,CAAC,iCAAiC,CAAC,CAAC,CAAC;;EAE7D,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6B,MAAM,CAACxF,MAAM,GAAG,CAAC,EAAE2D,CAAC,EAAE,EAAE;IAC1C;IACA,IAAI,CAAC6B,MAAM,CAAC7B,CAAC,GAAG,CAAC,CAAC,IAAI6B,MAAM,CAAC7B,CAAC,GAAG,CAAC,CAAC,IAAIwB,iBAAiB,CAACI,IAAI,CAACC,MAAM,CAAC7B,CAAC,CAAC,CAAC,IAAIwB,iBAAiB,CAACI,IAAI,CAACC,MAAM,CAAC7B,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;MACjH6B,MAAM,CAAC7B,CAAC,CAAC,IAAI6B,MAAM,CAAC7B,CAAC,GAAG,CAAC,CAAC;MAC1B6B,MAAM,CAACC,MAAM,CAAC9B,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;MACvBA,CAAC,EAAE;IACL;EACF;EAEA,OAAO6B,MAAM;AACf,CAAC;AAED,SAASE,SAASA,CAACb,MAAM,EAAEC,MAAM,EAAEhF,OAAO,EAAE;EAC1CA,OAAO,GAAGiF,eAAe,CAACjF,OAAO,EAAE;IACjCwF,gBAAgB,EAAE;EACpB,CAAC,CAAC;EACF,OAAOD,QAAQ,CAAC3F,IAAI,CAACmF,MAAM,EAAEC,MAAM,EAAEhF,OAAO,CAAC;AAC/C;AACA,SAAS6F,kBAAkBA,CAACd,MAAM,EAAEC,MAAM,EAAEhF,OAAO,EAAE;EACnD,OAAOuF,QAAQ,CAAC3F,IAAI,CAACmF,MAAM,EAAEC,MAAM,EAAEhF,OAAO,CAAC;AAC/C;AAEA,IAAI8F,QAAQ,GAAG,IAAIpG,IAAI,CAAC,CAAC;AAEzBoG,QAAQ,CAACnF,QAAQ,GAAG,UAAUJ,KAAK,EAAE;EACnC,IAAI,IAAI,CAACP,OAAO,CAAC+F,eAAe,EAAE;IAChC;IACAxF,KAAK,GAAGA,KAAK,CAACyF,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;EACtC;EAEA,IAAIC,QAAQ,GAAG,EAAE;IACbC,gBAAgB,GAAG3F,KAAK,CAACwD,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;;EAEjD,IAAI,CAACmC,gBAAgB,CAACA,gBAAgB,CAAChG,MAAM,GAAG,CAAC,CAAC,EAAE;IAClDgG,gBAAgB,CAACtB,GAAG,CAAC,CAAC;EACxB,CAAC,CAAC;;EAGF,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqC,gBAAgB,CAAChG,MAAM,EAAE2D,CAAC,EAAE,EAAE;IAChD,IAAIsC,IAAI,GAAGD,gBAAgB,CAACrC,CAAC,CAAC;IAE9B,IAAIA,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC7D,OAAO,CAACoG,cAAc,EAAE;MACzCH,QAAQ,CAACA,QAAQ,CAAC/F,MAAM,GAAG,CAAC,CAAC,IAAIiG,IAAI;IACvC,CAAC,MAAM;MACL,IAAI,IAAI,CAACnG,OAAO,CAACwF,gBAAgB,EAAE;QACjCW,IAAI,GAAGA,IAAI,CAACE,IAAI,CAAC,CAAC;MACpB;MAEAJ,QAAQ,CAACnC,IAAI,CAACqC,IAAI,CAAC;IACrB;EACF;EAEA,OAAOF,QAAQ;AACjB,CAAC;AAED,SAASK,SAASA,CAACvB,MAAM,EAAEC,MAAM,EAAE5E,QAAQ,EAAE;EAC3C,OAAO0F,QAAQ,CAAClG,IAAI,CAACmF,MAAM,EAAEC,MAAM,EAAE5E,QAAQ,CAAC;AAChD;AACA,SAASmG,gBAAgBA,CAACxB,MAAM,EAAEC,MAAM,EAAE5E,QAAQ,EAAE;EAClD,IAAIJ,OAAO,GAAGiF,eAAe,CAAC7E,QAAQ,EAAE;IACtCoF,gBAAgB,EAAE;EACpB,CAAC,CAAC;EACF,OAAOM,QAAQ,CAAClG,IAAI,CAACmF,MAAM,EAAEC,MAAM,EAAEhF,OAAO,CAAC;AAC/C;AAEA,IAAIwG,YAAY,GAAG,IAAI9G,IAAI,CAAC,CAAC;AAE7B8G,YAAY,CAAC7F,QAAQ,GAAG,UAAUJ,KAAK,EAAE;EACvC,OAAOA,KAAK,CAACwD,KAAK,CAAC,uBAAuB,CAAC;AAC7C,CAAC;AAED,SAAS0C,aAAaA,CAAC1B,MAAM,EAAEC,MAAM,EAAE5E,QAAQ,EAAE;EAC/C,OAAOoG,YAAY,CAAC5G,IAAI,CAACmF,MAAM,EAAEC,MAAM,EAAE5E,QAAQ,CAAC;AACpD;AAEA,IAAIsG,OAAO,GAAG,IAAIhH,IAAI,CAAC,CAAC;AAExBgH,OAAO,CAAC/F,QAAQ,GAAG,UAAUJ,KAAK,EAAE;EAClC,OAAOA,KAAK,CAACwD,KAAK,CAAC,eAAe,CAAC;AACrC,CAAC;AAED,SAAS4C,OAAOA,CAAC5B,MAAM,EAAEC,MAAM,EAAE5E,QAAQ,EAAE;EACzC,OAAOsG,OAAO,CAAC9G,IAAI,CAACmF,MAAM,EAAEC,MAAM,EAAE5E,QAAQ,CAAC;AAC/C;AAEA,SAASwG,OAAOA,CAACC,GAAG,EAAE;EACpB,yBAAyB;;EAEzB,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACvEH,OAAO,GAAG,SAAAA,CAAUC,GAAG,EAAE;MACvB,OAAO,OAAOA,GAAG;IACnB,CAAC;EACH,CAAC,MAAM;IACLD,OAAO,GAAG,SAAAA,CAAUC,GAAG,EAAE;MACvB,OAAOA,GAAG,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACnH,SAAS,GAAG,QAAQ,GAAG,OAAOkH,GAAG;IAC9H,CAAC;EACH;EAEA,OAAOD,OAAO,CAACC,GAAG,CAAC;AACrB;AAEA,SAASI,eAAeA,CAACJ,GAAG,EAAEK,GAAG,EAAE3G,KAAK,EAAE;EACxC,IAAI2G,GAAG,IAAIL,GAAG,EAAE;IACdM,MAAM,CAACC,cAAc,CAACP,GAAG,EAAEK,GAAG,EAAE;MAC9B3G,KAAK,EAAEA,KAAK;MACZ8G,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLV,GAAG,CAACK,GAAG,CAAC,GAAG3G,KAAK;EAClB;EAEA,OAAOsG,GAAG;AACZ;AAEA,SAASW,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EACvC,IAAIC,IAAI,GAAGR,MAAM,CAACQ,IAAI,CAACF,MAAM,CAAC;EAE9B,IAAIN,MAAM,CAACS,qBAAqB,EAAE;IAChC,IAAIC,OAAO,GAAGV,MAAM,CAACS,qBAAqB,CAACH,MAAM,CAAC;IAClD,IAAIC,cAAc,EAAEG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;MAC1D,OAAOZ,MAAM,CAACa,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAACV,UAAU;IAChE,CAAC,CAAC;IACFM,IAAI,CAAC7D,IAAI,CAACmE,KAAK,CAACN,IAAI,EAAEE,OAAO,CAAC;EAChC;EAEA,OAAOF,IAAI;AACb;AAEA,SAASO,cAAcA,CAACC,MAAM,EAAE;EAC9B,KAAK,IAAItE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5D,SAAS,CAACC,MAAM,EAAE2D,CAAC,EAAE,EAAE;IACzC,IAAIuE,MAAM,GAAGnI,SAAS,CAAC4D,CAAC,CAAC,IAAI,IAAI,GAAG5D,SAAS,CAAC4D,CAAC,CAAC,GAAG,CAAC,CAAC;IAErD,IAAIA,CAAC,GAAG,CAAC,EAAE;MACT2D,OAAO,CAACL,MAAM,CAACiB,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUnB,GAAG,EAAE;QACnDD,eAAe,CAACkB,MAAM,EAAEjB,GAAG,EAAEkB,MAAM,CAAClB,GAAG,CAAC,CAAC;MAC3C,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIC,MAAM,CAACmB,yBAAyB,EAAE;MAC3CnB,MAAM,CAACoB,gBAAgB,CAACJ,MAAM,EAAEhB,MAAM,CAACmB,yBAAyB,CAACF,MAAM,CAAC,CAAC;IAC3E,CAAC,MAAM;MACLZ,OAAO,CAACL,MAAM,CAACiB,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUnB,GAAG,EAAE;QAC7CC,MAAM,CAACC,cAAc,CAACe,MAAM,EAAEjB,GAAG,EAAEC,MAAM,CAACa,wBAAwB,CAACI,MAAM,EAAElB,GAAG,CAAC,CAAC;MAClF,CAAC,CAAC;IACJ;EACF;EAEA,OAAOiB,MAAM;AACf;AAEA,SAASK,kBAAkBA,CAACC,GAAG,EAAE;EAC/B,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,CAAC,CAAC;AACrH;AAEA,SAASH,kBAAkBA,CAACD,GAAG,EAAE;EAC/B,IAAIK,KAAK,CAACC,OAAO,CAACN,GAAG,CAAC,EAAE,OAAOO,iBAAiB,CAACP,GAAG,CAAC;AACvD;AAEA,SAASE,gBAAgBA,CAACM,IAAI,EAAE;EAC9B,IAAI,OAAOnC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,IAAII,MAAM,CAAC8B,IAAI,CAAC,EAAE,OAAOH,KAAK,CAACI,IAAI,CAACD,IAAI,CAAC;AAC/F;AAEA,SAASL,2BAA2BA,CAACO,CAAC,EAAEC,MAAM,EAAE;EAC9C,IAAI,CAACD,CAAC,EAAE;EACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOH,iBAAiB,CAACG,CAAC,EAAEC,MAAM,CAAC;EAC9D,IAAIC,CAAC,GAAGlC,MAAM,CAACxH,SAAS,CAAC2J,QAAQ,CAACC,IAAI,CAACJ,CAAC,CAAC,CAAC5E,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,IAAI8E,CAAC,KAAK,QAAQ,IAAIF,CAAC,CAACnC,WAAW,EAAEqC,CAAC,GAAGF,CAAC,CAACnC,WAAW,CAAC7B,IAAI;EAC3D,IAAIkE,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOP,KAAK,CAACI,IAAI,CAACC,CAAC,CAAC;EACpD,IAAIE,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAAC5D,IAAI,CAAC4D,CAAC,CAAC,EAAE,OAAOL,iBAAiB,CAACG,CAAC,EAAEC,MAAM,CAAC;AAClH;AAEA,SAASJ,iBAAiBA,CAACP,GAAG,EAAEe,GAAG,EAAE;EACnC,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGf,GAAG,CAACvI,MAAM,EAAEsJ,GAAG,GAAGf,GAAG,CAACvI,MAAM;EAErD,KAAK,IAAI2D,CAAC,GAAG,CAAC,EAAE4F,IAAI,GAAG,IAAIX,KAAK,CAACU,GAAG,CAAC,EAAE3F,CAAC,GAAG2F,GAAG,EAAE3F,CAAC,EAAE,EAAE4F,IAAI,CAAC5F,CAAC,CAAC,GAAG4E,GAAG,CAAC5E,CAAC,CAAC;EAErE,OAAO4F,IAAI;AACb;AAEA,SAASZ,kBAAkBA,CAAA,EAAG;EAC5B,MAAM,IAAIa,SAAS,CAAC,sIAAsI,CAAC;AAC7J;AAEA,IAAIC,uBAAuB,GAAGxC,MAAM,CAACxH,SAAS,CAAC2J,QAAQ;AACvD,IAAIM,QAAQ,GAAG,IAAIlK,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3B;;AAEAkK,QAAQ,CAAChH,eAAe,GAAG,IAAI;AAC/BgH,QAAQ,CAACjJ,QAAQ,GAAGmF,QAAQ,CAACnF,QAAQ;AAErCiJ,QAAQ,CAACnJ,SAAS,GAAG,UAAUF,KAAK,EAAE;EACpC,IAAIsJ,aAAa,GAAG,IAAI,CAAC7J,OAAO;IAC5B8J,oBAAoB,GAAGD,aAAa,CAACC,oBAAoB;IACzDC,qBAAqB,GAAGF,aAAa,CAACG,iBAAiB;IACvDA,iBAAiB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,UAAUE,CAAC,EAAEC,CAAC,EAAE;MACzE,OAAO,OAAOA,CAAC,KAAK,WAAW,GAAGJ,oBAAoB,GAAGI,CAAC;IAC5D,CAAC,GAAGH,qBAAqB;EACzB,OAAO,OAAOxJ,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG4J,IAAI,CAACC,SAAS,CAACC,YAAY,CAAC9J,KAAK,EAAE,IAAI,EAAE,IAAI,EAAEyJ,iBAAiB,CAAC,EAAEA,iBAAiB,EAAE,IAAI,CAAC;AACxI,CAAC;AAEDJ,QAAQ,CAACtG,MAAM,GAAG,UAAUC,IAAI,EAAEC,KAAK,EAAE;EACvC,OAAO9D,IAAI,CAACC,SAAS,CAAC2D,MAAM,CAACiG,IAAI,CAACK,QAAQ,EAAErG,IAAI,CAACyC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,EAAExC,KAAK,CAACwC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AAClH,CAAC;AAED,SAASsE,QAAQA,CAACC,MAAM,EAAEC,MAAM,EAAExK,OAAO,EAAE;EACzC,OAAO4J,QAAQ,CAAChK,IAAI,CAAC2K,MAAM,EAAEC,MAAM,EAAExK,OAAO,CAAC;AAC/C,CAAC,CAAC;AACF;;AAEA,SAASqK,YAAYA,CAACxD,GAAG,EAAE4D,KAAK,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEzD,GAAG,EAAE;EACjEuD,KAAK,GAAGA,KAAK,IAAI,EAAE;EACnBC,gBAAgB,GAAGA,gBAAgB,IAAI,EAAE;EAEzC,IAAIC,QAAQ,EAAE;IACZ9D,GAAG,GAAG8D,QAAQ,CAACzD,GAAG,EAAEL,GAAG,CAAC;EAC1B;EAEA,IAAIhD,CAAC;EAEL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4G,KAAK,CAACvK,MAAM,EAAE2D,CAAC,IAAI,CAAC,EAAE;IACpC,IAAI4G,KAAK,CAAC5G,CAAC,CAAC,KAAKgD,GAAG,EAAE;MACpB,OAAO6D,gBAAgB,CAAC7G,CAAC,CAAC;IAC5B;EACF;EAEA,IAAI+G,gBAAgB;EAEpB,IAAI,gBAAgB,KAAKjB,uBAAuB,CAACJ,IAAI,CAAC1C,GAAG,CAAC,EAAE;IAC1D4D,KAAK,CAAC3G,IAAI,CAAC+C,GAAG,CAAC;IACf+D,gBAAgB,GAAG,IAAI9B,KAAK,CAACjC,GAAG,CAAC3G,MAAM,CAAC;IACxCwK,gBAAgB,CAAC5G,IAAI,CAAC8G,gBAAgB,CAAC;IAEvC,KAAK/G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgD,GAAG,CAAC3G,MAAM,EAAE2D,CAAC,IAAI,CAAC,EAAE;MAClC+G,gBAAgB,CAAC/G,CAAC,CAAC,GAAGwG,YAAY,CAACxD,GAAG,CAAChD,CAAC,CAAC,EAAE4G,KAAK,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEzD,GAAG,CAAC;IACpF;IAEAuD,KAAK,CAAC7F,GAAG,CAAC,CAAC;IACX8F,gBAAgB,CAAC9F,GAAG,CAAC,CAAC;IACtB,OAAOgG,gBAAgB;EACzB;EAEA,IAAI/D,GAAG,IAAIA,GAAG,CAACgE,MAAM,EAAE;IACrBhE,GAAG,GAAGA,GAAG,CAACgE,MAAM,CAAC,CAAC;EACpB;EAEA,IAAIjE,OAAO,CAACC,GAAG,CAAC,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;IAC7C4D,KAAK,CAAC3G,IAAI,CAAC+C,GAAG,CAAC;IACf+D,gBAAgB,GAAG,CAAC,CAAC;IACrBF,gBAAgB,CAAC5G,IAAI,CAAC8G,gBAAgB,CAAC;IAEvC,IAAIE,UAAU,GAAG,EAAE;MACfC,IAAI;IAER,KAAKA,IAAI,IAAIlE,GAAG,EAAE;MAChB;MACA,IAAIA,GAAG,CAACzB,cAAc,CAAC2F,IAAI,CAAC,EAAE;QAC5BD,UAAU,CAAChH,IAAI,CAACiH,IAAI,CAAC;MACvB;IACF;IAEAD,UAAU,CAACE,IAAI,CAAC,CAAC;IAEjB,KAAKnH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiH,UAAU,CAAC5K,MAAM,EAAE2D,CAAC,IAAI,CAAC,EAAE;MACzCkH,IAAI,GAAGD,UAAU,CAACjH,CAAC,CAAC;MACpB+G,gBAAgB,CAACG,IAAI,CAAC,GAAGV,YAAY,CAACxD,GAAG,CAACkE,IAAI,CAAC,EAAEN,KAAK,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEI,IAAI,CAAC;IAC3F;IAEAN,KAAK,CAAC7F,GAAG,CAAC,CAAC;IACX8F,gBAAgB,CAAC9F,GAAG,CAAC,CAAC;EACxB,CAAC,MAAM;IACLgG,gBAAgB,GAAG/D,GAAG;EACxB;EAEA,OAAO+D,gBAAgB;AACzB;AAEA,IAAIK,SAAS,GAAG,IAAIvL,IAAI,CAAC,CAAC;AAE1BuL,SAAS,CAACtK,QAAQ,GAAG,UAAUJ,KAAK,EAAE;EACpC,OAAOA,KAAK,CAACgE,KAAK,CAAC,CAAC;AACtB,CAAC;AAED0G,SAAS,CAACpJ,IAAI,GAAGoJ,SAAS,CAACvK,WAAW,GAAG,UAAUH,KAAK,EAAE;EACxD,OAAOA,KAAK;AACd,CAAC;AAED,SAAS2K,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAEhL,QAAQ,EAAE;EAC5C,OAAO6K,SAAS,CAACrL,IAAI,CAACuL,MAAM,EAAEC,MAAM,EAAEhL,QAAQ,CAAC;AACjD;AAEA,SAASiL,UAAUA,CAACC,OAAO,EAAE;EAC3B,IAAItL,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpF,IAAIsL,OAAO,GAAGD,OAAO,CAACvH,KAAK,CAAC,qBAAqB,CAAC;IAC9CyH,UAAU,GAAGF,OAAO,CAACG,KAAK,CAAC,sBAAsB,CAAC,IAAI,EAAE;IACxDC,IAAI,GAAG,EAAE;IACT7H,CAAC,GAAG,CAAC;EAET,SAAS8H,UAAUA,CAAA,EAAG;IACpB,IAAIC,KAAK,GAAG,CAAC,CAAC;IACdF,IAAI,CAAC5H,IAAI,CAAC8H,KAAK,CAAC,CAAC,CAAC;;IAElB,OAAO/H,CAAC,GAAG0H,OAAO,CAACrL,MAAM,EAAE;MACzB,IAAIiG,IAAI,GAAGoF,OAAO,CAAC1H,CAAC,CAAC,CAAC,CAAC;;MAEvB,IAAI,uBAAuB,CAAC4B,IAAI,CAACU,IAAI,CAAC,EAAE;QACtC;MACF,CAAC,CAAC;;MAGF,IAAI0F,MAAM,GAAG,0CAA0C,CAAChJ,IAAI,CAACsD,IAAI,CAAC;MAElE,IAAI0F,MAAM,EAAE;QACVD,KAAK,CAACA,KAAK,GAAGC,MAAM,CAAC,CAAC,CAAC;MACzB;MAEAhI,CAAC,EAAE;IACL,CAAC,CAAC;IACF;;IAGAiI,eAAe,CAACF,KAAK,CAAC;IACtBE,eAAe,CAACF,KAAK,CAAC,CAAC,CAAC;;IAExBA,KAAK,CAACG,KAAK,GAAG,EAAE;IAEhB,OAAOlI,CAAC,GAAG0H,OAAO,CAACrL,MAAM,EAAE;MACzB,IAAI8L,KAAK,GAAGT,OAAO,CAAC1H,CAAC,CAAC;MAEtB,IAAI,gCAAgC,CAAC4B,IAAI,CAACuG,KAAK,CAAC,EAAE;QAChD;MACF,CAAC,MAAM,IAAI,KAAK,CAACvG,IAAI,CAACuG,KAAK,CAAC,EAAE;QAC5BJ,KAAK,CAACG,KAAK,CAACjI,IAAI,CAACmI,SAAS,CAAC,CAAC,CAAC;MAC/B,CAAC,MAAM,IAAID,KAAK,IAAIhM,OAAO,CAACkM,MAAM,EAAE;QAClC;QACA,MAAM,IAAIC,KAAK,CAAC,eAAe,IAAItI,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGsG,IAAI,CAACC,SAAS,CAAC4B,KAAK,CAAC,CAAC;MAC1E,CAAC,MAAM;QACLnI,CAAC,EAAE;MACL;IACF;EACF,CAAC,CAAC;EACF;;EAGA,SAASiI,eAAeA,CAACF,KAAK,EAAE;IAC9B,IAAIQ,UAAU,GAAG,uBAAuB,CAACvJ,IAAI,CAAC0I,OAAO,CAAC1H,CAAC,CAAC,CAAC;IAEzD,IAAIuI,UAAU,EAAE;MACd,IAAIC,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;MACvD,IAAIE,IAAI,GAAGF,UAAU,CAAC,CAAC,CAAC,CAACrI,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;MACvC,IAAIwI,QAAQ,GAAGD,IAAI,CAAC,CAAC,CAAC,CAACtG,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;MAE7C,IAAI,QAAQ,CAACP,IAAI,CAAC8G,QAAQ,CAAC,EAAE;QAC3BA,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAAC,CAAC,EAAED,QAAQ,CAACrM,MAAM,GAAG,CAAC,CAAC;MACpD;MAEA0L,KAAK,CAACS,SAAS,GAAG,UAAU,CAAC,GAAGE,QAAQ;MACxCX,KAAK,CAACS,SAAS,GAAG,QAAQ,CAAC,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,EAAEjG,IAAI,CAAC,CAAC;MACpDxC,CAAC,EAAE;IACL;EACF,CAAC,CAAC;EACF;;EAGA,SAASoI,SAASA,CAAA,EAAG;IACnB,IAAIQ,gBAAgB,GAAG5I,CAAC;MACpB6I,eAAe,GAAGnB,OAAO,CAAC1H,CAAC,EAAE,CAAC;MAC9B8I,WAAW,GAAGD,eAAe,CAAC3I,KAAK,CAAC,4CAA4C,CAAC;IACrF,IAAI6I,IAAI,GAAG;MACTC,QAAQ,EAAE,CAACF,WAAW,CAAC,CAAC,CAAC;MACzBG,QAAQ,EAAE,OAAOH,WAAW,CAAC,CAAC,CAAC,KAAK,WAAW,GAAG,CAAC,GAAG,CAACA,WAAW,CAAC,CAAC,CAAC;MACrEI,QAAQ,EAAE,CAACJ,WAAW,CAAC,CAAC,CAAC;MACzBK,QAAQ,EAAE,OAAOL,WAAW,CAAC,CAAC,CAAC,KAAK,WAAW,GAAG,CAAC,GAAG,CAACA,WAAW,CAAC,CAAC,CAAC;MACrEM,KAAK,EAAE,EAAE;MACTC,cAAc,EAAE;IAClB,CAAC,CAAC,CAAC;IACH;IACA;;IAEA,IAAIN,IAAI,CAACE,QAAQ,KAAK,CAAC,EAAE;MACvBF,IAAI,CAACC,QAAQ,IAAI,CAAC;IACpB;IAEA,IAAID,IAAI,CAACI,QAAQ,KAAK,CAAC,EAAE;MACvBJ,IAAI,CAACG,QAAQ,IAAI,CAAC;IACpB;IAEA,IAAII,QAAQ,GAAG,CAAC;MACZC,WAAW,GAAG,CAAC;IAEnB,OAAOvJ,CAAC,GAAG0H,OAAO,CAACrL,MAAM,EAAE2D,CAAC,EAAE,EAAE;MAC9B;MACA;MACA,IAAI0H,OAAO,CAAC1H,CAAC,CAAC,CAACwJ,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAIxJ,CAAC,GAAG,CAAC,GAAG0H,OAAO,CAACrL,MAAM,IAAIqL,OAAO,CAAC1H,CAAC,GAAG,CAAC,CAAC,CAACwJ,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI9B,OAAO,CAAC1H,CAAC,GAAG,CAAC,CAAC,CAACwJ,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QAC5I;MACF;MAEA,IAAIC,SAAS,GAAG/B,OAAO,CAAC1H,CAAC,CAAC,CAAC3D,MAAM,IAAI,CAAC,IAAI2D,CAAC,IAAI0H,OAAO,CAACrL,MAAM,GAAG,CAAC,GAAG,GAAG,GAAGqL,OAAO,CAAC1H,CAAC,CAAC,CAAC,CAAC,CAAC;MAEvF,IAAIyJ,SAAS,KAAK,GAAG,IAAIA,SAAS,KAAK,GAAG,IAAIA,SAAS,KAAK,GAAG,IAAIA,SAAS,KAAK,IAAI,EAAE;QACrFV,IAAI,CAACK,KAAK,CAACnJ,IAAI,CAACyH,OAAO,CAAC1H,CAAC,CAAC,CAAC;QAC3B+I,IAAI,CAACM,cAAc,CAACpJ,IAAI,CAAC0H,UAAU,CAAC3H,CAAC,CAAC,IAAI,IAAI,CAAC;QAE/C,IAAIyJ,SAAS,KAAK,GAAG,EAAE;UACrBH,QAAQ,EAAE;QACZ,CAAC,MAAM,IAAIG,SAAS,KAAK,GAAG,EAAE;UAC5BF,WAAW,EAAE;QACf,CAAC,MAAM,IAAIE,SAAS,KAAK,GAAG,EAAE;UAC5BH,QAAQ,EAAE;UACVC,WAAW,EAAE;QACf;MACF,CAAC,MAAM;QACL;MACF;IACF,CAAC,CAAC;;IAGF,IAAI,CAACD,QAAQ,IAAIP,IAAI,CAACI,QAAQ,KAAK,CAAC,EAAE;MACpCJ,IAAI,CAACI,QAAQ,GAAG,CAAC;IACnB;IAEA,IAAI,CAACI,WAAW,IAAIR,IAAI,CAACE,QAAQ,KAAK,CAAC,EAAE;MACvCF,IAAI,CAACE,QAAQ,GAAG,CAAC;IACnB,CAAC,CAAC;;IAGF,IAAI9M,OAAO,CAACkM,MAAM,EAAE;MAClB,IAAIiB,QAAQ,KAAKP,IAAI,CAACI,QAAQ,EAAE;QAC9B,MAAM,IAAIb,KAAK,CAAC,kDAAkD,IAAIM,gBAAgB,GAAG,CAAC,CAAC,CAAC;MAC9F;MAEA,IAAIW,WAAW,KAAKR,IAAI,CAACE,QAAQ,EAAE;QACjC,MAAM,IAAIX,KAAK,CAAC,oDAAoD,IAAIM,gBAAgB,GAAG,CAAC,CAAC,CAAC;MAChG;IACF;IAEA,OAAOG,IAAI;EACb;EAEA,OAAO/I,CAAC,GAAG0H,OAAO,CAACrL,MAAM,EAAE;IACzByL,UAAU,CAAC,CAAC;EACd;EAEA,OAAOD,IAAI;AACb;;AAEA;AACA;AACA;AACA,SAAS6B,gBAAgBA,CAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAClD,IAAIC,WAAW,GAAG,IAAI;IAClBC,iBAAiB,GAAG,KAAK;IACzBC,gBAAgB,GAAG,KAAK;IACxBC,WAAW,GAAG,CAAC;EACnB,OAAO,SAAS/G,QAAQA,CAAA,EAAG;IACzB,IAAI4G,WAAW,IAAI,CAACE,gBAAgB,EAAE;MACpC,IAAID,iBAAiB,EAAE;QACrBE,WAAW,EAAE;MACf,CAAC,MAAM;QACLH,WAAW,GAAG,KAAK;MACrB,CAAC,CAAC;MACF;;MAGA,IAAIH,KAAK,GAAGM,WAAW,IAAIJ,OAAO,EAAE;QAClC,OAAOI,WAAW;MACpB;MAEAD,gBAAgB,GAAG,IAAI;IACzB;IAEA,IAAI,CAACD,iBAAiB,EAAE;MACtB,IAAI,CAACC,gBAAgB,EAAE;QACrBF,WAAW,GAAG,IAAI;MACpB,CAAC,CAAC;MACF;;MAGA,IAAIF,OAAO,IAAID,KAAK,GAAGM,WAAW,EAAE;QAClC,OAAO,CAACA,WAAW,EAAE;MACvB;MAEAF,iBAAiB,GAAG,IAAI;MACxB,OAAO7G,QAAQ,CAAC,CAAC;IACnB,CAAC,CAAC;IACF;EAEF,CAAC;AACH;AAEA,SAASgH,UAAUA,CAAC3F,MAAM,EAAEkD,OAAO,EAAE;EACnC,IAAItL,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAEpF,IAAI,OAAOqL,OAAO,KAAK,QAAQ,EAAE;IAC/BA,OAAO,GAAGD,UAAU,CAACC,OAAO,CAAC;EAC/B;EAEA,IAAIxC,KAAK,CAACC,OAAO,CAACuC,OAAO,CAAC,EAAE;IAC1B,IAAIA,OAAO,CAACpL,MAAM,GAAG,CAAC,EAAE;MACtB,MAAM,IAAIiM,KAAK,CAAC,4CAA4C,CAAC;IAC/D;IAEAb,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC;EACtB,CAAC,CAAC;;EAGF,IAAI2B,KAAK,GAAG7E,MAAM,CAACrE,KAAK,CAAC,qBAAqB,CAAC;IAC3CyH,UAAU,GAAGpD,MAAM,CAACqD,KAAK,CAAC,sBAAsB,CAAC,IAAI,EAAE;IACvDM,KAAK,GAAGT,OAAO,CAACS,KAAK;IACrBiC,WAAW,GAAGhO,OAAO,CAACgO,WAAW,IAAI,UAAUC,UAAU,EAAE9H,IAAI,EAAEmH,SAAS,EAAEY,YAAY,EAAE;MAC5F,OAAO/H,IAAI,KAAK+H,YAAY;IAC9B,CAAC;IACGC,UAAU,GAAG,CAAC;IACdC,UAAU,GAAGpO,OAAO,CAACoO,UAAU,IAAI,CAAC;IACpCX,OAAO,GAAG,CAAC;IACXY,MAAM,GAAG,CAAC;IACVC,WAAW;IACXC,QAAQ;EACZ;AACF;AACA;;EAGE,SAASC,QAAQA,CAAC5B,IAAI,EAAE6B,KAAK,EAAE;IAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9B,IAAI,CAACK,KAAK,CAAC/M,MAAM,EAAEwO,CAAC,EAAE,EAAE;MAC1C,IAAIvI,IAAI,GAAGyG,IAAI,CAACK,KAAK,CAACyB,CAAC,CAAC;QACpBpB,SAAS,GAAGnH,IAAI,CAACjG,MAAM,GAAG,CAAC,GAAGiG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;QAC3CwI,OAAO,GAAGxI,IAAI,CAACjG,MAAM,GAAG,CAAC,GAAGiG,IAAI,CAACqG,MAAM,CAAC,CAAC,CAAC,GAAGrG,IAAI;MAErD,IAAImH,SAAS,KAAK,GAAG,IAAIA,SAAS,KAAK,GAAG,EAAE;QAC1C;QACA,IAAI,CAACU,WAAW,CAACS,KAAK,GAAG,CAAC,EAAExB,KAAK,CAACwB,KAAK,CAAC,EAAEnB,SAAS,EAAEqB,OAAO,CAAC,EAAE;UAC7DR,UAAU,EAAE;UAEZ,IAAIA,UAAU,GAAGC,UAAU,EAAE;YAC3B,OAAO,KAAK;UACd;QACF;QAEAK,KAAK,EAAE;MACT;IACF;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;;EAGF,KAAK,IAAI5K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkI,KAAK,CAAC7L,MAAM,EAAE2D,CAAC,EAAE,EAAE;IACrC,IAAI+I,IAAI,GAAGb,KAAK,CAAClI,CAAC,CAAC;MACf6J,OAAO,GAAGT,KAAK,CAAC/M,MAAM,GAAG0M,IAAI,CAACE,QAAQ;MACtCgB,WAAW,GAAG,CAAC;MACfW,KAAK,GAAGJ,MAAM,GAAGzB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACtC,IAAI9F,QAAQ,GAAGwG,gBAAgB,CAACkB,KAAK,EAAEhB,OAAO,EAAEC,OAAO,CAAC;IAExD,OAAOI,WAAW,KAAK3N,SAAS,EAAE2N,WAAW,GAAG/G,QAAQ,CAAC,CAAC,EAAE;MAC1D,IAAIyH,QAAQ,CAAC5B,IAAI,EAAE6B,KAAK,GAAGX,WAAW,CAAC,EAAE;QACvClB,IAAI,CAACyB,MAAM,GAAGA,MAAM,IAAIP,WAAW;QACnC;MACF;IACF;IAEA,IAAIA,WAAW,KAAK3N,SAAS,EAAE;MAC7B,OAAO,KAAK;IACd,CAAC,CAAC;IACF;;IAGAsN,OAAO,GAAGb,IAAI,CAACyB,MAAM,GAAGzB,IAAI,CAACC,QAAQ,GAAGD,IAAI,CAACE,QAAQ;EACvD,CAAC,CAAC;;EAGF,IAAI8B,UAAU,GAAG,CAAC;EAElB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG9C,KAAK,CAAC7L,MAAM,EAAE2O,EAAE,EAAE,EAAE;IACxC,IAAIC,KAAK,GAAG/C,KAAK,CAAC8C,EAAE,CAAC;MACjBE,MAAM,GAAGD,KAAK,CAACjC,QAAQ,GAAGiC,KAAK,CAACT,MAAM,GAAGO,UAAU,GAAG,CAAC;IAE3DA,UAAU,IAAIE,KAAK,CAAC9B,QAAQ,GAAG8B,KAAK,CAAChC,QAAQ;IAE7C,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,KAAK,CAAC7B,KAAK,CAAC/M,MAAM,EAAEwO,CAAC,EAAE,EAAE;MAC3C,IAAIvI,IAAI,GAAG2I,KAAK,CAAC7B,KAAK,CAACyB,CAAC,CAAC;QACrBpB,SAAS,GAAGnH,IAAI,CAACjG,MAAM,GAAG,CAAC,GAAGiG,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;QAC3CwI,OAAO,GAAGxI,IAAI,CAACjG,MAAM,GAAG,CAAC,GAAGiG,IAAI,CAACqG,MAAM,CAAC,CAAC,CAAC,GAAGrG,IAAI;QACjD6I,SAAS,GAAGF,KAAK,CAAC5B,cAAc,IAAI4B,KAAK,CAAC5B,cAAc,CAACwB,CAAC,CAAC,IAAI,IAAI;MAEvE,IAAIpB,SAAS,KAAK,GAAG,EAAE;QACrByB,MAAM,EAAE;MACV,CAAC,MAAM,IAAIzB,SAAS,KAAK,GAAG,EAAE;QAC5BL,KAAK,CAACtH,MAAM,CAACoJ,MAAM,EAAE,CAAC,CAAC;QACvBvD,UAAU,CAAC7F,MAAM,CAACoJ,MAAM,EAAE,CAAC,CAAC;QAC5B;MACF,CAAC,MAAM,IAAIzB,SAAS,KAAK,GAAG,EAAE;QAC5BL,KAAK,CAACtH,MAAM,CAACoJ,MAAM,EAAE,CAAC,EAAEJ,OAAO,CAAC;QAChCnD,UAAU,CAAC7F,MAAM,CAACoJ,MAAM,EAAE,CAAC,EAAEC,SAAS,CAAC;QACvCD,MAAM,EAAE;MACV,CAAC,MAAM,IAAIzB,SAAS,KAAK,IAAI,EAAE;QAC7B,IAAI2B,iBAAiB,GAAGH,KAAK,CAAC7B,KAAK,CAACyB,CAAC,GAAG,CAAC,CAAC,GAAGI,KAAK,CAAC7B,KAAK,CAACyB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;QAEzE,IAAIO,iBAAiB,KAAK,GAAG,EAAE;UAC7BX,WAAW,GAAG,IAAI;QACpB,CAAC,MAAM,IAAIW,iBAAiB,KAAK,GAAG,EAAE;UACpCV,QAAQ,GAAG,IAAI;QACjB;MACF;IACF;EACF,CAAC,CAAC;;EAGF,IAAID,WAAW,EAAE;IACf,OAAO,CAACrB,KAAK,CAACA,KAAK,CAAC/M,MAAM,GAAG,CAAC,CAAC,EAAE;MAC/B+M,KAAK,CAACrI,GAAG,CAAC,CAAC;MACX4G,UAAU,CAAC5G,GAAG,CAAC,CAAC;IAClB;EACF,CAAC,MAAM,IAAI2J,QAAQ,EAAE;IACnBtB,KAAK,CAACnJ,IAAI,CAAC,EAAE,CAAC;IACd0H,UAAU,CAAC1H,IAAI,CAAC,IAAI,CAAC;EACvB;EAEA,KAAK,IAAIoL,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGjC,KAAK,CAAC/M,MAAM,GAAG,CAAC,EAAEgP,EAAE,EAAE,EAAE;IAC5CjC,KAAK,CAACiC,EAAE,CAAC,GAAGjC,KAAK,CAACiC,EAAE,CAAC,GAAG1D,UAAU,CAAC0D,EAAE,CAAC;EACxC;EAEA,OAAOjC,KAAK,CAACpL,IAAI,CAAC,EAAE,CAAC;AACvB,CAAC,CAAC;;AAEF,SAASsN,YAAYA,CAAC7D,OAAO,EAAEtL,OAAO,EAAE;EACtC,IAAI,OAAOsL,OAAO,KAAK,QAAQ,EAAE;IAC/BA,OAAO,GAAGD,UAAU,CAACC,OAAO,CAAC;EAC/B;EAEA,IAAI8D,YAAY,GAAG,CAAC;EAEpB,SAASC,YAAYA,CAAA,EAAG;IACtB,IAAIzD,KAAK,GAAGN,OAAO,CAAC8D,YAAY,EAAE,CAAC;IAEnC,IAAI,CAACxD,KAAK,EAAE;MACV,OAAO5L,OAAO,CAACsP,QAAQ,CAAC,CAAC;IAC3B;IAEAtP,OAAO,CAACuP,QAAQ,CAAC3D,KAAK,EAAE,UAAU4D,GAAG,EAAElD,IAAI,EAAE;MAC3C,IAAIkD,GAAG,EAAE;QACP,OAAOxP,OAAO,CAACsP,QAAQ,CAACE,GAAG,CAAC;MAC9B;MAEA,IAAIC,cAAc,GAAG1B,UAAU,CAACzB,IAAI,EAAEV,KAAK,EAAE5L,OAAO,CAAC;MACrDA,OAAO,CAAC0P,OAAO,CAAC9D,KAAK,EAAE6D,cAAc,EAAE,UAAUD,GAAG,EAAE;QACpD,IAAIA,GAAG,EAAE;UACP,OAAOxP,OAAO,CAACsP,QAAQ,CAACE,GAAG,CAAC;QAC9B;QAEAH,YAAY,CAAC,CAAC;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAA,YAAY,CAAC,CAAC;AAChB;AAEA,SAASM,eAAeA,CAACC,WAAW,EAAEC,WAAW,EAAE9K,MAAM,EAAEC,MAAM,EAAE8K,SAAS,EAAEC,SAAS,EAAE/P,OAAO,EAAE;EAChG,IAAI,CAACA,OAAO,EAAE;IACZA,OAAO,GAAG,CAAC,CAAC;EACd;EAEA,IAAI,OAAOA,OAAO,CAACgQ,OAAO,KAAK,WAAW,EAAE;IAC1ChQ,OAAO,CAACgQ,OAAO,GAAG,CAAC;EACrB;EAEA,IAAIpQ,IAAI,GAAG0G,SAAS,CAACvB,MAAM,EAAEC,MAAM,EAAEhF,OAAO,CAAC;EAE7C,IAAI,CAACJ,IAAI,EAAE;IACT;EACF;EAEAA,IAAI,CAACkE,IAAI,CAAC;IACRvD,KAAK,EAAE,EAAE;IACT0M,KAAK,EAAE;EACT,CAAC,CAAC,CAAC,CAAC;;EAEJ,SAASgD,YAAYA,CAAChD,KAAK,EAAE;IAC3B,OAAOA,KAAK,CAACzI,GAAG,CAAC,UAAU0L,KAAK,EAAE;MAChC,OAAO,GAAG,GAAGA,KAAK;IACpB,CAAC,CAAC;EACJ;EAEA,IAAInE,KAAK,GAAG,EAAE;EACd,IAAIoE,aAAa,GAAG,CAAC;IACjBC,aAAa,GAAG,CAAC;IACjBC,QAAQ,GAAG,EAAE;IACbC,OAAO,GAAG,CAAC;IACXC,OAAO,GAAG,CAAC;EAEf,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAAC3M,CAAC,EAAE;IAC5B,IAAI4M,OAAO,GAAG7Q,IAAI,CAACiE,CAAC,CAAC;MACjBoJ,KAAK,GAAGwD,OAAO,CAACxD,KAAK,IAAIwD,OAAO,CAAClQ,KAAK,CAACyF,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACjC,KAAK,CAAC,IAAI,CAAC;IACzE0M,OAAO,CAACxD,KAAK,GAAGA,KAAK;IAErB,IAAIwD,OAAO,CAACzN,KAAK,IAAIyN,OAAO,CAACxN,OAAO,EAAE;MACpC,IAAIyN,SAAS;;MAEb;MACA,IAAI,CAACP,aAAa,EAAE;QAClB,IAAIQ,IAAI,GAAG/Q,IAAI,CAACiE,CAAC,GAAG,CAAC,CAAC;QACtBsM,aAAa,GAAGG,OAAO;QACvBF,aAAa,GAAGG,OAAO;QAEvB,IAAII,IAAI,EAAE;UACRN,QAAQ,GAAGrQ,OAAO,CAACgQ,OAAO,GAAG,CAAC,GAAGC,YAAY,CAACU,IAAI,CAAC1D,KAAK,CAAC1I,KAAK,CAAC,CAACvE,OAAO,CAACgQ,OAAO,CAAC,CAAC,GAAG,EAAE;UACtFG,aAAa,IAAIE,QAAQ,CAACnQ,MAAM;UAChCkQ,aAAa,IAAIC,QAAQ,CAACnQ,MAAM;QAClC;MACF,CAAC,CAAC;;MAGF,CAACwQ,SAAS,GAAGL,QAAQ,EAAEvM,IAAI,CAACmE,KAAK,CAACyI,SAAS,EAAElI,kBAAkB,CAACyE,KAAK,CAACzI,GAAG,CAAC,UAAU0L,KAAK,EAAE;QACzF,OAAO,CAACO,OAAO,CAACzN,KAAK,GAAG,GAAG,GAAG,GAAG,IAAIkN,KAAK;MAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;MAGN,IAAIO,OAAO,CAACzN,KAAK,EAAE;QACjBuN,OAAO,IAAItD,KAAK,CAAC/M,MAAM;MACzB,CAAC,MAAM;QACLoQ,OAAO,IAAIrD,KAAK,CAAC/M,MAAM;MACzB;IACF,CAAC,MAAM;MACL;MACA,IAAIiQ,aAAa,EAAE;QACjB;QACA,IAAIlD,KAAK,CAAC/M,MAAM,IAAIF,OAAO,CAACgQ,OAAO,GAAG,CAAC,IAAInM,CAAC,GAAGjE,IAAI,CAACM,MAAM,GAAG,CAAC,EAAE;UAC9D,IAAI0Q,UAAU;;UAEd;UACA,CAACA,UAAU,GAAGP,QAAQ,EAAEvM,IAAI,CAACmE,KAAK,CAAC2I,UAAU,EAAEpI,kBAAkB,CAACyH,YAAY,CAAChD,KAAK,CAAC,CAAC,CAAC;QACzF,CAAC,MAAM;UACL,IAAI4D,UAAU;;UAEd;UACA,IAAIC,WAAW,GAAG9P,IAAI,CAACC,GAAG,CAACgM,KAAK,CAAC/M,MAAM,EAAEF,OAAO,CAACgQ,OAAO,CAAC;UAEzD,CAACa,UAAU,GAAGR,QAAQ,EAAEvM,IAAI,CAACmE,KAAK,CAAC4I,UAAU,EAAErI,kBAAkB,CAACyH,YAAY,CAAChD,KAAK,CAAC1I,KAAK,CAAC,CAAC,EAAEuM,WAAW,CAAC,CAAC,CAAC,CAAC;UAE7G,IAAIlE,IAAI,GAAG;YACTC,QAAQ,EAAEsD,aAAa;YACvBrD,QAAQ,EAAEwD,OAAO,GAAGH,aAAa,GAAGW,WAAW;YAC/C/D,QAAQ,EAAEqD,aAAa;YACvBpD,QAAQ,EAAEuD,OAAO,GAAGH,aAAa,GAAGU,WAAW;YAC/C7D,KAAK,EAAEoD;UACT,CAAC;UAED,IAAIxM,CAAC,IAAIjE,IAAI,CAACM,MAAM,GAAG,CAAC,IAAI+M,KAAK,CAAC/M,MAAM,IAAIF,OAAO,CAACgQ,OAAO,EAAE;YAC3D;YACA,IAAIe,aAAa,GAAG,KAAK,CAACtL,IAAI,CAACV,MAAM,CAAC;YACtC,IAAIiM,aAAa,GAAG,KAAK,CAACvL,IAAI,CAACT,MAAM,CAAC;YACtC,IAAIiM,cAAc,GAAGhE,KAAK,CAAC/M,MAAM,IAAI,CAAC,IAAImQ,QAAQ,CAACnQ,MAAM,GAAG0M,IAAI,CAACE,QAAQ;YAEzE,IAAI,CAACiE,aAAa,IAAIE,cAAc,IAAIlM,MAAM,CAAC7E,MAAM,GAAG,CAAC,EAAE;cACzD;cACA;cACAmQ,QAAQ,CAAC1K,MAAM,CAACiH,IAAI,CAACE,QAAQ,EAAE,CAAC,EAAE,8BAA8B,CAAC;YACnE;YAEA,IAAI,CAACiE,aAAa,IAAI,CAACE,cAAc,IAAI,CAACD,aAAa,EAAE;cACvDX,QAAQ,CAACvM,IAAI,CAAC,8BAA8B,CAAC;YAC/C;UACF;UAEAiI,KAAK,CAACjI,IAAI,CAAC8I,IAAI,CAAC;UAChBuD,aAAa,GAAG,CAAC;UACjBC,aAAa,GAAG,CAAC;UACjBC,QAAQ,GAAG,EAAE;QACf;MACF;MAEAC,OAAO,IAAIrD,KAAK,CAAC/M,MAAM;MACvBqQ,OAAO,IAAItD,KAAK,CAAC/M,MAAM;IACzB;EACF,CAAC;EAED,KAAK,IAAI2D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjE,IAAI,CAACM,MAAM,EAAE2D,CAAC,EAAE,EAAE;IACpC2M,KAAK,CAAC3M,CAAC,CAAC;EACV;EAEA,OAAO;IACL+L,WAAW,EAAEA,WAAW;IACxBC,WAAW,EAAEA,WAAW;IACxBC,SAAS,EAAEA,SAAS;IACpBC,SAAS,EAAEA,SAAS;IACpBhE,KAAK,EAAEA;EACT,CAAC;AACH;AACA,SAASmF,WAAWA,CAACtR,IAAI,EAAE;EACzB,IAAIkJ,KAAK,CAACC,OAAO,CAACnJ,IAAI,CAAC,EAAE;IACvB,OAAOA,IAAI,CAAC4E,GAAG,CAAC0M,WAAW,CAAC,CAACrP,IAAI,CAAC,IAAI,CAAC;EACzC;EAEA,IAAIiB,GAAG,GAAG,EAAE;EAEZ,IAAIlD,IAAI,CAACgQ,WAAW,IAAIhQ,IAAI,CAACiQ,WAAW,EAAE;IACxC/M,GAAG,CAACgB,IAAI,CAAC,SAAS,GAAGlE,IAAI,CAACgQ,WAAW,CAAC;EACxC;EAEA9M,GAAG,CAACgB,IAAI,CAAC,qEAAqE,CAAC;EAC/EhB,GAAG,CAACgB,IAAI,CAAC,MAAM,GAAGlE,IAAI,CAACgQ,WAAW,IAAI,OAAOhQ,IAAI,CAACkQ,SAAS,KAAK,WAAW,GAAG,EAAE,GAAG,IAAI,GAAGlQ,IAAI,CAACkQ,SAAS,CAAC,CAAC;EAC1GhN,GAAG,CAACgB,IAAI,CAAC,MAAM,GAAGlE,IAAI,CAACiQ,WAAW,IAAI,OAAOjQ,IAAI,CAACmQ,SAAS,KAAK,WAAW,GAAG,EAAE,GAAG,IAAI,GAAGnQ,IAAI,CAACmQ,SAAS,CAAC,CAAC;EAE1G,KAAK,IAAIlM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjE,IAAI,CAACmM,KAAK,CAAC7L,MAAM,EAAE2D,CAAC,EAAE,EAAE;IAC1C,IAAI+I,IAAI,GAAGhN,IAAI,CAACmM,KAAK,CAAClI,CAAC,CAAC,CAAC,CAAC;IAC1B;IACA;;IAEA,IAAI+I,IAAI,CAACE,QAAQ,KAAK,CAAC,EAAE;MACvBF,IAAI,CAACC,QAAQ,IAAI,CAAC;IACpB;IAEA,IAAID,IAAI,CAACI,QAAQ,KAAK,CAAC,EAAE;MACvBJ,IAAI,CAACG,QAAQ,IAAI,CAAC;IACpB;IAEAjK,GAAG,CAACgB,IAAI,CAAC,MAAM,GAAG8I,IAAI,CAACC,QAAQ,GAAG,GAAG,GAAGD,IAAI,CAACE,QAAQ,GAAG,IAAI,GAAGF,IAAI,CAACG,QAAQ,GAAG,GAAG,GAAGH,IAAI,CAACI,QAAQ,GAAG,KAAK,CAAC;IAC3GlK,GAAG,CAACgB,IAAI,CAACmE,KAAK,CAACnF,GAAG,EAAE8J,IAAI,CAACK,KAAK,CAAC;EACjC;EAEA,OAAOnK,GAAG,CAACjB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;AAC9B;AACA,SAASsP,mBAAmBA,CAACvB,WAAW,EAAEC,WAAW,EAAE9K,MAAM,EAAEC,MAAM,EAAE8K,SAAS,EAAEC,SAAS,EAAE/P,OAAO,EAAE;EACpG,OAAOkR,WAAW,CAACvB,eAAe,CAACC,WAAW,EAAEC,WAAW,EAAE9K,MAAM,EAAEC,MAAM,EAAE8K,SAAS,EAAEC,SAAS,EAAE/P,OAAO,CAAC,CAAC;AAC9G;AACA,SAASoR,WAAWA,CAAC7E,QAAQ,EAAExH,MAAM,EAAEC,MAAM,EAAE8K,SAAS,EAAEC,SAAS,EAAE/P,OAAO,EAAE;EAC5E,OAAOmR,mBAAmB,CAAC5E,QAAQ,EAAEA,QAAQ,EAAExH,MAAM,EAAEC,MAAM,EAAE8K,SAAS,EAAEC,SAAS,EAAE/P,OAAO,CAAC;AAC/F;AAEA,SAASqR,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACxB,IAAID,CAAC,CAACpR,MAAM,KAAKqR,CAAC,CAACrR,MAAM,EAAE;IACzB,OAAO,KAAK;EACd;EAEA,OAAOsR,eAAe,CAACF,CAAC,EAAEC,CAAC,CAAC;AAC9B;AACA,SAASC,eAAeA,CAAC5N,KAAK,EAAE4J,KAAK,EAAE;EACrC,IAAIA,KAAK,CAACtN,MAAM,GAAG0D,KAAK,CAAC1D,MAAM,EAAE;IAC/B,OAAO,KAAK;EACd;EAEA,KAAK,IAAI2D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2J,KAAK,CAACtN,MAAM,EAAE2D,CAAC,EAAE,EAAE;IACrC,IAAI2J,KAAK,CAAC3J,CAAC,CAAC,KAAKD,KAAK,CAACC,CAAC,CAAC,EAAE;MACzB,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb;AAEA,SAAS4N,aAAaA,CAAC7E,IAAI,EAAE;EAC3B,IAAI8E,oBAAoB,GAAGC,mBAAmB,CAAC/E,IAAI,CAACK,KAAK,CAAC;IACtDH,QAAQ,GAAG4E,oBAAoB,CAAC5E,QAAQ;IACxCE,QAAQ,GAAG0E,oBAAoB,CAAC1E,QAAQ;EAE5C,IAAIF,QAAQ,KAAK3M,SAAS,EAAE;IAC1ByM,IAAI,CAACE,QAAQ,GAAGA,QAAQ;EAC1B,CAAC,MAAM;IACL,OAAOF,IAAI,CAACE,QAAQ;EACtB;EAEA,IAAIE,QAAQ,KAAK7M,SAAS,EAAE;IAC1ByM,IAAI,CAACI,QAAQ,GAAGA,QAAQ;EAC1B,CAAC,MAAM;IACL,OAAOJ,IAAI,CAACI,QAAQ;EACtB;AACF;AACA,SAAS4E,KAAKA,CAACC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAE;EACjCF,IAAI,GAAGG,SAAS,CAACH,IAAI,EAAEE,IAAI,CAAC;EAC5BD,MAAM,GAAGE,SAAS,CAACF,MAAM,EAAEC,IAAI,CAAC;EAChC,IAAIjP,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;EACd;EACA;;EAEA,IAAI+O,IAAI,CAACjG,KAAK,IAAIkG,MAAM,CAAClG,KAAK,EAAE;IAC9B9I,GAAG,CAAC8I,KAAK,GAAGiG,IAAI,CAACjG,KAAK,IAAIkG,MAAM,CAAClG,KAAK;EACxC;EAEA,IAAIiG,IAAI,CAAChC,WAAW,IAAIiC,MAAM,CAACjC,WAAW,EAAE;IAC1C,IAAI,CAACoC,eAAe,CAACJ,IAAI,CAAC,EAAE;MAC1B;MACA/O,GAAG,CAAC8M,WAAW,GAAGkC,MAAM,CAAClC,WAAW,IAAIiC,IAAI,CAACjC,WAAW;MACxD9M,GAAG,CAAC+M,WAAW,GAAGiC,MAAM,CAACjC,WAAW,IAAIgC,IAAI,CAAChC,WAAW;MACxD/M,GAAG,CAACgN,SAAS,GAAGgC,MAAM,CAAChC,SAAS,IAAI+B,IAAI,CAAC/B,SAAS;MAClDhN,GAAG,CAACiN,SAAS,GAAG+B,MAAM,CAAC/B,SAAS,IAAI8B,IAAI,CAAC9B,SAAS;IACpD,CAAC,MAAM,IAAI,CAACkC,eAAe,CAACH,MAAM,CAAC,EAAE;MACnC;MACAhP,GAAG,CAAC8M,WAAW,GAAGiC,IAAI,CAACjC,WAAW;MAClC9M,GAAG,CAAC+M,WAAW,GAAGgC,IAAI,CAAChC,WAAW;MAClC/M,GAAG,CAACgN,SAAS,GAAG+B,IAAI,CAAC/B,SAAS;MAC9BhN,GAAG,CAACiN,SAAS,GAAG8B,IAAI,CAAC9B,SAAS;IAChC,CAAC,MAAM;MACL;MACAjN,GAAG,CAAC8M,WAAW,GAAGsC,WAAW,CAACpP,GAAG,EAAE+O,IAAI,CAACjC,WAAW,EAAEkC,MAAM,CAAClC,WAAW,CAAC;MACxE9M,GAAG,CAAC+M,WAAW,GAAGqC,WAAW,CAACpP,GAAG,EAAE+O,IAAI,CAAChC,WAAW,EAAEiC,MAAM,CAACjC,WAAW,CAAC;MACxE/M,GAAG,CAACgN,SAAS,GAAGoC,WAAW,CAACpP,GAAG,EAAE+O,IAAI,CAAC/B,SAAS,EAAEgC,MAAM,CAAChC,SAAS,CAAC;MAClEhN,GAAG,CAACiN,SAAS,GAAGmC,WAAW,CAACpP,GAAG,EAAE+O,IAAI,CAAC9B,SAAS,EAAE+B,MAAM,CAAC/B,SAAS,CAAC;IACpE;EACF;EAEAjN,GAAG,CAACiJ,KAAK,GAAG,EAAE;EACd,IAAIoG,SAAS,GAAG,CAAC;IACbC,WAAW,GAAG,CAAC;IACfC,UAAU,GAAG,CAAC;IACdC,YAAY,GAAG,CAAC;EAEpB,OAAOH,SAAS,GAAGN,IAAI,CAAC9F,KAAK,CAAC7L,MAAM,IAAIkS,WAAW,GAAGN,MAAM,CAAC/F,KAAK,CAAC7L,MAAM,EAAE;IACzE,IAAIqS,WAAW,GAAGV,IAAI,CAAC9F,KAAK,CAACoG,SAAS,CAAC,IAAI;QACzCtF,QAAQ,EAAEzL;MACZ,CAAC;MACGoR,aAAa,GAAGV,MAAM,CAAC/F,KAAK,CAACqG,WAAW,CAAC,IAAI;QAC/CvF,QAAQ,EAAEzL;MACZ,CAAC;IAED,IAAIqR,UAAU,CAACF,WAAW,EAAEC,aAAa,CAAC,EAAE;MAC1C;MACA1P,GAAG,CAACiJ,KAAK,CAACjI,IAAI,CAAC4O,SAAS,CAACH,WAAW,EAAEF,UAAU,CAAC,CAAC;MAClDF,SAAS,EAAE;MACXG,YAAY,IAAIC,WAAW,CAACvF,QAAQ,GAAGuF,WAAW,CAACzF,QAAQ;IAC7D,CAAC,MAAM,IAAI2F,UAAU,CAACD,aAAa,EAAED,WAAW,CAAC,EAAE;MACjD;MACAzP,GAAG,CAACiJ,KAAK,CAACjI,IAAI,CAAC4O,SAAS,CAACF,aAAa,EAAEF,YAAY,CAAC,CAAC;MACtDF,WAAW,EAAE;MACbC,UAAU,IAAIG,aAAa,CAACxF,QAAQ,GAAGwF,aAAa,CAAC1F,QAAQ;IAC/D,CAAC,MAAM;MACL;MACA,IAAI6F,UAAU,GAAG;QACf9F,QAAQ,EAAE7L,IAAI,CAACC,GAAG,CAACsR,WAAW,CAAC1F,QAAQ,EAAE2F,aAAa,CAAC3F,QAAQ,CAAC;QAChEC,QAAQ,EAAE,CAAC;QACXC,QAAQ,EAAE/L,IAAI,CAACC,GAAG,CAACsR,WAAW,CAACxF,QAAQ,GAAGsF,UAAU,EAAEG,aAAa,CAAC3F,QAAQ,GAAGyF,YAAY,CAAC;QAC5FtF,QAAQ,EAAE,CAAC;QACXC,KAAK,EAAE;MACT,CAAC;MACD2F,UAAU,CAACD,UAAU,EAAEJ,WAAW,CAAC1F,QAAQ,EAAE0F,WAAW,CAACtF,KAAK,EAAEuF,aAAa,CAAC3F,QAAQ,EAAE2F,aAAa,CAACvF,KAAK,CAAC;MAC5GmF,WAAW,EAAE;MACbD,SAAS,EAAE;MACXrP,GAAG,CAACiJ,KAAK,CAACjI,IAAI,CAAC6O,UAAU,CAAC;IAC5B;EACF;EAEA,OAAO7P,GAAG;AACZ;AAEA,SAASkP,SAASA,CAACa,KAAK,EAAEd,IAAI,EAAE;EAC9B,IAAI,OAAOc,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAI,MAAM,CAACpN,IAAI,CAACoN,KAAK,CAAC,IAAI,UAAU,CAACpN,IAAI,CAACoN,KAAK,CAAC,EAAE;MAChD,OAAOxH,UAAU,CAACwH,KAAK,CAAC,CAAC,CAAC,CAAC;IAC7B;IAEA,IAAI,CAACd,IAAI,EAAE;MACT,MAAM,IAAI5F,KAAK,CAAC,kDAAkD,CAAC;IACrE;IAEA,OAAOwD,eAAe,CAACxP,SAAS,EAAEA,SAAS,EAAE4R,IAAI,EAAEc,KAAK,CAAC;EAC3D;EAEA,OAAOA,KAAK;AACd;AAEA,SAASZ,eAAeA,CAACa,KAAK,EAAE;EAC9B,OAAOA,KAAK,CAACjD,WAAW,IAAIiD,KAAK,CAACjD,WAAW,KAAKiD,KAAK,CAAClD,WAAW;AACrE;AAEA,SAASsC,WAAWA,CAACtG,KAAK,EAAEiG,IAAI,EAAEC,MAAM,EAAE;EACxC,IAAID,IAAI,KAAKC,MAAM,EAAE;IACnB,OAAOD,IAAI;EACb,CAAC,MAAM;IACLjG,KAAK,CAACmH,QAAQ,GAAG,IAAI;IACrB,OAAO;MACLlB,IAAI,EAAEA,IAAI;MACVC,MAAM,EAAEA;IACV,CAAC;EACH;AACF;AAEA,SAASW,UAAUA,CAAChN,IAAI,EAAEuN,KAAK,EAAE;EAC/B,OAAOvN,IAAI,CAACoH,QAAQ,GAAGmG,KAAK,CAACnG,QAAQ,IAAIpH,IAAI,CAACoH,QAAQ,GAAGpH,IAAI,CAACqH,QAAQ,GAAGkG,KAAK,CAACnG,QAAQ;AACzF;AAEA,SAAS6F,SAASA,CAAC9F,IAAI,EAAEyB,MAAM,EAAE;EAC/B,OAAO;IACLxB,QAAQ,EAAED,IAAI,CAACC,QAAQ;IACvBC,QAAQ,EAAEF,IAAI,CAACE,QAAQ;IACvBC,QAAQ,EAAEH,IAAI,CAACG,QAAQ,GAAGsB,MAAM;IAChCrB,QAAQ,EAAEJ,IAAI,CAACI,QAAQ;IACvBC,KAAK,EAAEL,IAAI,CAACK;EACd,CAAC;AACH;AAEA,SAAS2F,UAAUA,CAAChG,IAAI,EAAEyF,UAAU,EAAEY,SAAS,EAAEC,WAAW,EAAEC,UAAU,EAAE;EACxE;EACA;EACA,IAAItB,IAAI,GAAG;MACTxD,MAAM,EAAEgE,UAAU;MAClBpF,KAAK,EAAEgG,SAAS;MAChBrH,KAAK,EAAE;IACT,CAAC;IACGwH,KAAK,GAAG;MACV/E,MAAM,EAAE6E,WAAW;MACnBjG,KAAK,EAAEkG,UAAU;MACjBvH,KAAK,EAAE;IACT,CAAC,CAAC,CAAC;;EAEHyH,aAAa,CAACzG,IAAI,EAAEiF,IAAI,EAAEuB,KAAK,CAAC;EAChCC,aAAa,CAACzG,IAAI,EAAEwG,KAAK,EAAEvB,IAAI,CAAC,CAAC,CAAC;;EAElC,OAAOA,IAAI,CAACjG,KAAK,GAAGiG,IAAI,CAAC5E,KAAK,CAAC/M,MAAM,IAAIkT,KAAK,CAACxH,KAAK,GAAGwH,KAAK,CAACnG,KAAK,CAAC/M,MAAM,EAAE;IACzE,IAAIqS,WAAW,GAAGV,IAAI,CAAC5E,KAAK,CAAC4E,IAAI,CAACjG,KAAK,CAAC;MACpC0H,YAAY,GAAGF,KAAK,CAACnG,KAAK,CAACmG,KAAK,CAACxH,KAAK,CAAC;IAE3C,IAAI,CAAC2G,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,MAAMe,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;MAC9G;MACAC,YAAY,CAAC3G,IAAI,EAAEiF,IAAI,EAAEuB,KAAK,CAAC;IACjC,CAAC,MAAM,IAAIb,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIe,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5D,IAAIE,WAAW;;MAEf;MACA,CAACA,WAAW,GAAG5G,IAAI,CAACK,KAAK,EAAEnJ,IAAI,CAACmE,KAAK,CAACuL,WAAW,EAAEhL,kBAAkB,CAACiL,aAAa,CAAC5B,IAAI,CAAC,CAAC,CAAC;IAC7F,CAAC,MAAM,IAAIyB,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIf,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5D,IAAImB,YAAY;;MAEhB;MACA,CAACA,YAAY,GAAG9G,IAAI,CAACK,KAAK,EAAEnJ,IAAI,CAACmE,KAAK,CAACyL,YAAY,EAAElL,kBAAkB,CAACiL,aAAa,CAACL,KAAK,CAAC,CAAC,CAAC;IAChG,CAAC,MAAM,IAAIb,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIe,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5D;MACAK,OAAO,CAAC/G,IAAI,EAAEiF,IAAI,EAAEuB,KAAK,CAAC;IAC5B,CAAC,MAAM,IAAIE,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIf,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC5D;MACAoB,OAAO,CAAC/G,IAAI,EAAEwG,KAAK,EAAEvB,IAAI,EAAE,IAAI,CAAC;IAClC,CAAC,MAAM,IAAIU,WAAW,KAAKe,YAAY,EAAE;MACvC;MACA1G,IAAI,CAACK,KAAK,CAACnJ,IAAI,CAACyO,WAAW,CAAC;MAC5BV,IAAI,CAACjG,KAAK,EAAE;MACZwH,KAAK,CAACxH,KAAK,EAAE;IACf,CAAC,MAAM;MACL;MACAmH,QAAQ,CAACnG,IAAI,EAAE6G,aAAa,CAAC5B,IAAI,CAAC,EAAE4B,aAAa,CAACL,KAAK,CAAC,CAAC;IAC3D;EACF,CAAC,CAAC;;EAGFQ,cAAc,CAAChH,IAAI,EAAEiF,IAAI,CAAC;EAC1B+B,cAAc,CAAChH,IAAI,EAAEwG,KAAK,CAAC;EAC3B3B,aAAa,CAAC7E,IAAI,CAAC;AACrB;AAEA,SAAS2G,YAAYA,CAAC3G,IAAI,EAAEiF,IAAI,EAAEuB,KAAK,EAAE;EACvC,IAAIS,SAAS,GAAGJ,aAAa,CAAC5B,IAAI,CAAC;IAC/BiC,YAAY,GAAGL,aAAa,CAACL,KAAK,CAAC;EAEvC,IAAIW,UAAU,CAACF,SAAS,CAAC,IAAIE,UAAU,CAACD,YAAY,CAAC,EAAE;IACrD;IACA,IAAItC,eAAe,CAACqC,SAAS,EAAEC,YAAY,CAAC,IAAIE,kBAAkB,CAACZ,KAAK,EAAES,SAAS,EAAEA,SAAS,CAAC3T,MAAM,GAAG4T,YAAY,CAAC5T,MAAM,CAAC,EAAE;MAC5H,IAAI+T,YAAY;MAEhB,CAACA,YAAY,GAAGrH,IAAI,CAACK,KAAK,EAAEnJ,IAAI,CAACmE,KAAK,CAACgM,YAAY,EAAEzL,kBAAkB,CAACqL,SAAS,CAAC,CAAC;MAEnF;IACF,CAAC,MAAM,IAAIrC,eAAe,CAACsC,YAAY,EAAED,SAAS,CAAC,IAAIG,kBAAkB,CAACnC,IAAI,EAAEiC,YAAY,EAAEA,YAAY,CAAC5T,MAAM,GAAG2T,SAAS,CAAC3T,MAAM,CAAC,EAAE;MACrI,IAAIgU,YAAY;MAEhB,CAACA,YAAY,GAAGtH,IAAI,CAACK,KAAK,EAAEnJ,IAAI,CAACmE,KAAK,CAACiM,YAAY,EAAE1L,kBAAkB,CAACsL,YAAY,CAAC,CAAC;MAEtF;IACF;EACF,CAAC,MAAM,IAAIzC,UAAU,CAACwC,SAAS,EAAEC,YAAY,CAAC,EAAE;IAC9C,IAAIK,YAAY;IAEhB,CAACA,YAAY,GAAGvH,IAAI,CAACK,KAAK,EAAEnJ,IAAI,CAACmE,KAAK,CAACkM,YAAY,EAAE3L,kBAAkB,CAACqL,SAAS,CAAC,CAAC;IAEnF;EACF;EAEAd,QAAQ,CAACnG,IAAI,EAAEiH,SAAS,EAAEC,YAAY,CAAC;AACzC;AAEA,SAASH,OAAOA,CAAC/G,IAAI,EAAEiF,IAAI,EAAEuB,KAAK,EAAEgB,IAAI,EAAE;EACxC,IAAIP,SAAS,GAAGJ,aAAa,CAAC5B,IAAI,CAAC;IAC/BiC,YAAY,GAAGO,cAAc,CAACjB,KAAK,EAAES,SAAS,CAAC;EAEnD,IAAIC,YAAY,CAACQ,MAAM,EAAE;IACvB,IAAIC,YAAY;IAEhB,CAACA,YAAY,GAAG3H,IAAI,CAACK,KAAK,EAAEnJ,IAAI,CAACmE,KAAK,CAACsM,YAAY,EAAE/L,kBAAkB,CAACsL,YAAY,CAACQ,MAAM,CAAC,CAAC;EAC/F,CAAC,MAAM;IACLvB,QAAQ,CAACnG,IAAI,EAAEwH,IAAI,GAAGN,YAAY,GAAGD,SAAS,EAAEO,IAAI,GAAGP,SAAS,GAAGC,YAAY,CAAC;EAClF;AACF;AAEA,SAASf,QAAQA,CAACnG,IAAI,EAAEiF,IAAI,EAAEuB,KAAK,EAAE;EACnCxG,IAAI,CAACmG,QAAQ,GAAG,IAAI;EACpBnG,IAAI,CAACK,KAAK,CAACnJ,IAAI,CAAC;IACdiP,QAAQ,EAAE,IAAI;IACdlB,IAAI,EAAEA,IAAI;IACVC,MAAM,EAAEsB;EACV,CAAC,CAAC;AACJ;AAEA,SAASC,aAAaA,CAACzG,IAAI,EAAE4H,MAAM,EAAEpB,KAAK,EAAE;EAC1C,OAAOoB,MAAM,CAACnG,MAAM,GAAG+E,KAAK,CAAC/E,MAAM,IAAImG,MAAM,CAAC5I,KAAK,GAAG4I,MAAM,CAACvH,KAAK,CAAC/M,MAAM,EAAE;IACzE,IAAIiG,IAAI,GAAGqO,MAAM,CAACvH,KAAK,CAACuH,MAAM,CAAC5I,KAAK,EAAE,CAAC;IACvCgB,IAAI,CAACK,KAAK,CAACnJ,IAAI,CAACqC,IAAI,CAAC;IACrBqO,MAAM,CAACnG,MAAM,EAAE;EACjB;AACF;AAEA,SAASuF,cAAcA,CAAChH,IAAI,EAAE4H,MAAM,EAAE;EACpC,OAAOA,MAAM,CAAC5I,KAAK,GAAG4I,MAAM,CAACvH,KAAK,CAAC/M,MAAM,EAAE;IACzC,IAAIiG,IAAI,GAAGqO,MAAM,CAACvH,KAAK,CAACuH,MAAM,CAAC5I,KAAK,EAAE,CAAC;IACvCgB,IAAI,CAACK,KAAK,CAACnJ,IAAI,CAACqC,IAAI,CAAC;EACvB;AACF;AAEA,SAASsN,aAAaA,CAACgB,KAAK,EAAE;EAC5B,IAAI3R,GAAG,GAAG,EAAE;IACRwK,SAAS,GAAGmH,KAAK,CAACxH,KAAK,CAACwH,KAAK,CAAC7I,KAAK,CAAC,CAAC,CAAC,CAAC;EAE3C,OAAO6I,KAAK,CAAC7I,KAAK,GAAG6I,KAAK,CAACxH,KAAK,CAAC/M,MAAM,EAAE;IACvC,IAAIiG,IAAI,GAAGsO,KAAK,CAACxH,KAAK,CAACwH,KAAK,CAAC7I,KAAK,CAAC,CAAC,CAAC;;IAErC,IAAI0B,SAAS,KAAK,GAAG,IAAInH,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACxCmH,SAAS,GAAG,GAAG;IACjB;IAEA,IAAIA,SAAS,KAAKnH,IAAI,CAAC,CAAC,CAAC,EAAE;MACzBrD,GAAG,CAACgB,IAAI,CAACqC,IAAI,CAAC;MACdsO,KAAK,CAAC7I,KAAK,EAAE;IACf,CAAC,MAAM;MACL;IACF;EACF;EAEA,OAAO9I,GAAG;AACZ;AAEA,SAASuR,cAAcA,CAACI,KAAK,EAAEC,YAAY,EAAE;EAC3C,IAAIC,OAAO,GAAG,EAAE;IACZL,MAAM,GAAG,EAAE;IACXM,UAAU,GAAG,CAAC;IACdC,cAAc,GAAG,KAAK;IACtBC,UAAU,GAAG,KAAK;EAEtB,OAAOF,UAAU,GAAGF,YAAY,CAACxU,MAAM,IAAIuU,KAAK,CAAC7I,KAAK,GAAG6I,KAAK,CAACxH,KAAK,CAAC/M,MAAM,EAAE;IAC3E,IAAI6U,MAAM,GAAGN,KAAK,CAACxH,KAAK,CAACwH,KAAK,CAAC7I,KAAK,CAAC;MACjCH,KAAK,GAAGiJ,YAAY,CAACE,UAAU,CAAC,CAAC,CAAC;;IAEtC,IAAInJ,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACpB;IACF;IAEAoJ,cAAc,GAAGA,cAAc,IAAIE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;IACpDT,MAAM,CAACxQ,IAAI,CAAC2H,KAAK,CAAC;IAClBmJ,UAAU,EAAE,CAAC,CAAC;IACd;;IAEA,IAAIG,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACrBD,UAAU,GAAG,IAAI;MAEjB,OAAOC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QACxBJ,OAAO,CAAC7Q,IAAI,CAACiR,MAAM,CAAC;QACpBA,MAAM,GAAGN,KAAK,CAACxH,KAAK,CAAC,EAAEwH,KAAK,CAAC7I,KAAK,CAAC;MACrC;IACF;IAEA,IAAIH,KAAK,CAACe,MAAM,CAAC,CAAC,CAAC,KAAKuI,MAAM,CAACvI,MAAM,CAAC,CAAC,CAAC,EAAE;MACxCmI,OAAO,CAAC7Q,IAAI,CAACiR,MAAM,CAAC;MACpBN,KAAK,CAAC7I,KAAK,EAAE;IACf,CAAC,MAAM;MACLkJ,UAAU,GAAG,IAAI;IACnB;EACF;EAEA,IAAI,CAACJ,YAAY,CAACE,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,IAAIC,cAAc,EAAE;IACjEC,UAAU,GAAG,IAAI;EACnB;EAEA,IAAIA,UAAU,EAAE;IACd,OAAOH,OAAO;EAChB;EAEA,OAAOC,UAAU,GAAGF,YAAY,CAACxU,MAAM,EAAE;IACvCoU,MAAM,CAACxQ,IAAI,CAAC4Q,YAAY,CAACE,UAAU,EAAE,CAAC,CAAC;EACzC;EAEA,OAAO;IACLN,MAAM,EAAEA,MAAM;IACdK,OAAO,EAAEA;EACX,CAAC;AACH;AAEA,SAASZ,UAAUA,CAACY,OAAO,EAAE;EAC3B,OAAOA,OAAO,CAACK,MAAM,CAAC,UAAUrE,IAAI,EAAEoE,MAAM,EAAE;IAC5C,OAAOpE,IAAI,IAAIoE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;EAClC,CAAC,EAAE,IAAI,CAAC;AACV;AAEA,SAASf,kBAAkBA,CAACS,KAAK,EAAEQ,aAAa,EAAEC,KAAK,EAAE;EACvD,KAAK,IAAIrR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqR,KAAK,EAAErR,CAAC,EAAE,EAAE;IAC9B,IAAIsR,aAAa,GAAGF,aAAa,CAACA,aAAa,CAAC/U,MAAM,GAAGgV,KAAK,GAAGrR,CAAC,CAAC,CAAC2I,MAAM,CAAC,CAAC,CAAC;IAE7E,IAAIiI,KAAK,CAACxH,KAAK,CAACwH,KAAK,CAAC7I,KAAK,GAAG/H,CAAC,CAAC,KAAK,GAAG,GAAGsR,aAAa,EAAE;MACxD,OAAO,KAAK;IACd;EACF;EAEAV,KAAK,CAAC7I,KAAK,IAAIsJ,KAAK;EACpB,OAAO,IAAI;AACb;AAEA,SAASvD,mBAAmBA,CAAC1E,KAAK,EAAE;EAClC,IAAIH,QAAQ,GAAG,CAAC;EAChB,IAAIE,QAAQ,GAAG,CAAC;EAChBC,KAAK,CAAC5E,OAAO,CAAC,UAAUlC,IAAI,EAAE;IAC5B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,IAAIiP,OAAO,GAAGzD,mBAAmB,CAACxL,IAAI,CAAC0L,IAAI,CAAC;MAC5C,IAAIwD,UAAU,GAAG1D,mBAAmB,CAACxL,IAAI,CAAC2L,MAAM,CAAC;MAEjD,IAAIhF,QAAQ,KAAK3M,SAAS,EAAE;QAC1B,IAAIiV,OAAO,CAACtI,QAAQ,KAAKuI,UAAU,CAACvI,QAAQ,EAAE;UAC5CA,QAAQ,IAAIsI,OAAO,CAACtI,QAAQ;QAC9B,CAAC,MAAM;UACLA,QAAQ,GAAG3M,SAAS;QACtB;MACF;MAEA,IAAI6M,QAAQ,KAAK7M,SAAS,EAAE;QAC1B,IAAIiV,OAAO,CAACpI,QAAQ,KAAKqI,UAAU,CAACrI,QAAQ,EAAE;UAC5CA,QAAQ,IAAIoI,OAAO,CAACpI,QAAQ;QAC9B,CAAC,MAAM;UACLA,QAAQ,GAAG7M,SAAS;QACtB;MACF;IACF,CAAC,MAAM;MACL,IAAI6M,QAAQ,KAAK7M,SAAS,KAAKgG,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;QAClE6G,QAAQ,EAAE;MACZ;MAEA,IAAIF,QAAQ,KAAK3M,SAAS,KAAKgG,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE;QAClE2G,QAAQ,EAAE;MACZ;IACF;EACF,CAAC,CAAC;EACF,OAAO;IACLA,QAAQ,EAAEA,QAAQ;IAClBE,QAAQ,EAAEA;EACZ,CAAC;AACH;AAEA,SAASsI,YAAYA,CAAC3F,eAAe,EAAE;EACrC,IAAI7G,KAAK,CAACC,OAAO,CAAC4G,eAAe,CAAC,EAAE;IAClC,OAAOA,eAAe,CAACnL,GAAG,CAAC8Q,YAAY,CAAC,CAACnR,OAAO,CAAC,CAAC;EACpD;EAEA,OAAO+D,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEyH,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;IAC7DC,WAAW,EAAED,eAAe,CAACE,WAAW;IACxCC,SAAS,EAAEH,eAAe,CAACI,SAAS;IACpCF,WAAW,EAAEF,eAAe,CAACC,WAAW;IACxCG,SAAS,EAAEJ,eAAe,CAACG,SAAS;IACpC/D,KAAK,EAAE4D,eAAe,CAAC5D,KAAK,CAACvH,GAAG,CAAC,UAAUoI,IAAI,EAAE;MAC/C,OAAO;QACLE,QAAQ,EAAEF,IAAI,CAACI,QAAQ;QACvBH,QAAQ,EAAED,IAAI,CAACG,QAAQ;QACvBC,QAAQ,EAAEJ,IAAI,CAACE,QAAQ;QACvBC,QAAQ,EAAEH,IAAI,CAACC,QAAQ;QACvBK,cAAc,EAAEN,IAAI,CAACM,cAAc;QACnCD,KAAK,EAAEL,IAAI,CAACK,KAAK,CAACzI,GAAG,CAAC,UAAU+Q,CAAC,EAAE;UACjC,IAAIA,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;YACrB,OAAO,GAAG,CAACC,MAAM,CAACF,CAAC,CAAChR,KAAK,CAAC,CAAC,CAAC,CAAC;UAC/B;UAEA,IAAIgR,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;YACrB,OAAO,GAAG,CAACC,MAAM,CAACF,CAAC,CAAChR,KAAK,CAAC,CAAC,CAAC,CAAC;UAC/B;UAEA,OAAOgR,CAAC;QACV,CAAC;MACH,CAAC;IACH,CAAC;EACH,CAAC,CAAC;AACJ;;AAEA;AACA,SAASG,mBAAmBA,CAACf,OAAO,EAAE;EACpC,IAAI7R,GAAG,GAAG,EAAE;IACRiS,MAAM;IACNzH,SAAS;EAEb,KAAK,IAAIzJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8Q,OAAO,CAACzU,MAAM,EAAE2D,CAAC,EAAE,EAAE;IACvCkR,MAAM,GAAGJ,OAAO,CAAC9Q,CAAC,CAAC;IAEnB,IAAIkR,MAAM,CAAC/R,KAAK,EAAE;MAChBsK,SAAS,GAAG,CAAC;IACf,CAAC,MAAM,IAAIyH,MAAM,CAAC9R,OAAO,EAAE;MACzBqK,SAAS,GAAG,CAAC,CAAC;IAChB,CAAC,MAAM;MACLA,SAAS,GAAG,CAAC;IACf;IAEAxK,GAAG,CAACgB,IAAI,CAAC,CAACwJ,SAAS,EAAEyH,MAAM,CAACxU,KAAK,CAAC,CAAC;EACrC;EAEA,OAAOuC,GAAG;AACZ;AAEA,SAAS6S,mBAAmBA,CAAChB,OAAO,EAAE;EACpC,IAAI7R,GAAG,GAAG,EAAE;EAEZ,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8Q,OAAO,CAACzU,MAAM,EAAE2D,CAAC,EAAE,EAAE;IACvC,IAAIkR,MAAM,GAAGJ,OAAO,CAAC9Q,CAAC,CAAC;IAEvB,IAAIkR,MAAM,CAAC/R,KAAK,EAAE;MAChBF,GAAG,CAACgB,IAAI,CAAC,OAAO,CAAC;IACnB,CAAC,MAAM,IAAIiR,MAAM,CAAC9R,OAAO,EAAE;MACzBH,GAAG,CAACgB,IAAI,CAAC,OAAO,CAAC;IACnB;IAEAhB,GAAG,CAACgB,IAAI,CAAC8R,UAAU,CAACb,MAAM,CAACxU,KAAK,CAAC,CAAC;IAElC,IAAIwU,MAAM,CAAC/R,KAAK,EAAE;MAChBF,GAAG,CAACgB,IAAI,CAAC,QAAQ,CAAC;IACpB,CAAC,MAAM,IAAIiR,MAAM,CAAC9R,OAAO,EAAE;MACzBH,GAAG,CAACgB,IAAI,CAAC,QAAQ,CAAC;IACpB;EACF;EAEA,OAAOhB,GAAG,CAACjB,IAAI,CAAC,EAAE,CAAC;AACrB;AAEA,SAAS+T,UAAUA,CAACC,CAAC,EAAE;EACrB,IAAIxM,CAAC,GAAGwM,CAAC;EACTxM,CAAC,GAAGA,CAAC,CAACrD,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;EAC5BqD,CAAC,GAAGA,CAAC,CAACrD,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;EAC3BqD,CAAC,GAAGA,CAAC,CAACrD,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;EAC3BqD,CAAC,GAAGA,CAAC,CAACrD,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;EAC7B,OAAOqD,CAAC;AACV;AAEA,SAAS3J,IAAI,EAAEqO,UAAU,EAAEoB,YAAY,EAAE9E,YAAY,EAAEqL,mBAAmB,EAAEC,mBAAmB,EAAEvE,WAAW,EAAED,mBAAmB,EAAEjG,UAAU,EAAEpG,SAAS,EAAE6B,OAAO,EAAE2D,QAAQ,EAAEhE,SAAS,EAAEG,aAAa,EAAEF,gBAAgB,EAAEX,SAAS,EAAEC,kBAAkB,EAAEqL,WAAW,EAAEU,KAAK,EAAEvG,UAAU,EAAEiK,YAAY,EAAE3F,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}