{"ast": null, "code": "const notify = node => !node.isLayoutDirty && node.willUpdate(false);\nfunction nodeGroup() {\n  const nodes = new Set();\n  const subscriptions = new WeakMap();\n  const dirtyAll = () => nodes.forEach(notify);\n  return {\n    add: node => {\n      nodes.add(node);\n      subscriptions.set(node, node.addEventListener(\"willUpdate\", dirtyAll));\n    },\n    remove: node => {\n      nodes.delete(node);\n      const unsubscribe = subscriptions.get(node);\n      if (unsubscribe) {\n        unsubscribe();\n        subscriptions.delete(node);\n      }\n      dirtyAll();\n    },\n    dirty: dirtyAll\n  };\n}\nexport { nodeGroup };", "map": {"version": 3, "names": ["notify", "node", "isLayoutDirty", "willUpdate", "nodeGroup", "nodes", "Set", "subscriptions", "WeakMap", "dirtyAll", "for<PERSON>ach", "add", "set", "addEventListener", "remove", "delete", "unsubscribe", "get", "dirty"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/framer-motion/dist/es/projection/node/group.mjs"], "sourcesContent": ["const notify = (node) => !node.isLayoutDirty && node.willUpdate(false);\nfunction nodeGroup() {\n    const nodes = new Set();\n    const subscriptions = new WeakMap();\n    const dirtyAll = () => nodes.forEach(notify);\n    return {\n        add: (node) => {\n            nodes.add(node);\n            subscriptions.set(node, node.addEventListener(\"willUpdate\", dirtyAll));\n        },\n        remove: (node) => {\n            nodes.delete(node);\n            const unsubscribe = subscriptions.get(node);\n            if (unsubscribe) {\n                unsubscribe();\n                subscriptions.delete(node);\n            }\n            dirtyAll();\n        },\n        dirty: dirtyAll,\n    };\n}\n\nexport { nodeGroup };\n"], "mappings": "AAAA,MAAMA,MAAM,GAAIC,IAAI,IAAK,CAACA,IAAI,CAACC,aAAa,IAAID,IAAI,CAACE,UAAU,CAAC,KAAK,CAAC;AACtE,SAASC,SAASA,CAAA,EAAG;EACjB,MAAMC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;EACvB,MAAMC,aAAa,GAAG,IAAIC,OAAO,CAAC,CAAC;EACnC,MAAMC,QAAQ,GAAGA,CAAA,KAAMJ,KAAK,CAACK,OAAO,CAACV,MAAM,CAAC;EAC5C,OAAO;IACHW,GAAG,EAAGV,IAAI,IAAK;MACXI,KAAK,CAACM,GAAG,CAACV,IAAI,CAAC;MACfM,aAAa,CAACK,GAAG,CAACX,IAAI,EAAEA,IAAI,CAACY,gBAAgB,CAAC,YAAY,EAAEJ,QAAQ,CAAC,CAAC;IAC1E,CAAC;IACDK,MAAM,EAAGb,IAAI,IAAK;MACdI,KAAK,CAACU,MAAM,CAACd,IAAI,CAAC;MAClB,MAAMe,WAAW,GAAGT,aAAa,CAACU,GAAG,CAAChB,IAAI,CAAC;MAC3C,IAAIe,WAAW,EAAE;QACbA,WAAW,CAAC,CAAC;QACbT,aAAa,CAACQ,MAAM,CAACd,IAAI,CAAC;MAC9B;MACAQ,QAAQ,CAAC,CAAC;IACd,CAAC;IACDS,KAAK,EAAET;EACX,CAAC;AACL;AAEA,SAASL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}