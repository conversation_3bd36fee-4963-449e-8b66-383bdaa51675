{"ast": null, "code": "export { pointEnd, pointStart, position } from './lib/index.js';", "map": {"version": 3, "names": ["pointEnd", "pointStart", "position"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/unist-util-position/index.js"], "sourcesContent": ["export {pointEnd, pointStart, position} from './lib/index.js'\n"], "mappings": "AAAA,SAAQA,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}