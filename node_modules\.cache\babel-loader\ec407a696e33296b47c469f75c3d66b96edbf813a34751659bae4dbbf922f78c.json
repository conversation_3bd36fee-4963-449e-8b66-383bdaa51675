{"ast": null, "code": "/**\n * @typedef {import('./lib/index.js').Options} Options\n */\n\nexport { toString } from './lib/index.js';", "map": {"version": 3, "names": ["toString"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/mdast-util-to-string/index.js"], "sourcesContent": ["/**\n * @typedef {import('./lib/index.js').Options} Options\n */\n\nexport {toString} from './lib/index.js'\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAAQA,QAAQ,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}