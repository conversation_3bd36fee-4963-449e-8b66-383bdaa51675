{"ast": null, "code": "/**\n * @typedef {import('hast').Content} HastContent\n * @typedef {import('hast').Root} HastRoot\n *\n * @typedef {import('mdast').Content} MdastContent\n * @typedef {import('mdast').Root} MdastRoot\n *\n * @typedef {import('./state.js').Options} Options\n */\n\n/**\n * @typedef {HastRoot | HastContent} HastNodes\n * @typedef {MdastRoot | MdastContent} MdastNodes\n */\n\nimport { footer } from './footer.js';\nimport { createState } from './state.js';\n\n/**\n * Transform mdast to hast.\n *\n * ##### Notes\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most utilities ignore `raw` nodes but two notable ones don’t:\n *\n * *   `hast-util-to-html` also has an option `allowDangerousHtml` which will\n *     output the raw HTML.\n *     This is typically discouraged as noted by the option name but is useful\n *     if you completely trust authors\n * *   `hast-util-raw` can handle the raw embedded HTML strings by parsing them\n *     into standard hast nodes (`element`, `text`, etc).\n *     This is a heavy task as it needs a full HTML parser, but it is the only\n *     way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark, which we follow by default.\n * They are supported by GitHub, so footnotes can be enabled in markdown with\n * `mdast-util-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes, which is hidden for sighted users but shown to\n * assistive technology.\n * When your page is not in English, you must define translated values.\n *\n * Back references use ARIA attributes, but the section label itself uses a\n * heading that is hidden with an `sr-only` class.\n * To show it to sighted users, define different attributes in\n * `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem, as it links footnote calls to footnote\n * definitions on the page through `id` attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * Example: headings (DOM clobbering) in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * *   when the node has a `value` (and doesn’t have `data.hName`,\n *     `data.hProperties`, or `data.hChildren`, see later), create a hast `text`\n *     node\n * *   otherwise, create a `<div>` element (which could be changed with\n *     `data.hName`), with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @param {MdastNodes} tree\n *   mdast tree.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {HastNodes | null | undefined}\n *   hast tree.\n */\n// To do: next major: always return a single `root`.\nexport function toHast(tree, options) {\n  const state = createState(tree, options);\n  const node = state.one(tree, null);\n  const foot = footer(state);\n  if (foot) {\n    // @ts-expect-error If there’s a footer, there were definitions, meaning block\n    // content.\n    // So assume `node` is a parent node.\n    node.children.push({\n      type: 'text',\n      value: '\\n'\n    }, foot);\n  }\n\n  // To do: next major: always return root?\n  return Array.isArray(node) ? {\n    type: 'root',\n    children: node\n  } : node;\n}", "map": {"version": 3, "names": ["footer", "createState", "toHast", "tree", "options", "state", "node", "one", "foot", "children", "push", "type", "value", "Array", "isArray"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/mdast-util-to-hast/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Content} HastContent\n * @typedef {import('hast').Root} HastRoot\n *\n * @typedef {import('mdast').Content} MdastContent\n * @typedef {import('mdast').Root} MdastRoot\n *\n * @typedef {import('./state.js').Options} Options\n */\n\n/**\n * @typedef {HastRoot | HastContent} HastNodes\n * @typedef {MdastRoot | MdastContent} MdastNodes\n */\n\nimport {footer} from './footer.js'\nimport {createState} from './state.js'\n\n/**\n * Transform mdast to hast.\n *\n * ##### Notes\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most utilities ignore `raw` nodes but two notable ones don’t:\n *\n * *   `hast-util-to-html` also has an option `allowDangerousHtml` which will\n *     output the raw HTML.\n *     This is typically discouraged as noted by the option name but is useful\n *     if you completely trust authors\n * *   `hast-util-raw` can handle the raw embedded HTML strings by parsing them\n *     into standard hast nodes (`element`, `text`, etc).\n *     This is a heavy task as it needs a full HTML parser, but it is the only\n *     way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark, which we follow by default.\n * They are supported by GitHub, so footnotes can be enabled in markdown with\n * `mdast-util-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes, which is hidden for sighted users but shown to\n * assistive technology.\n * When your page is not in English, you must define translated values.\n *\n * Back references use ARIA attributes, but the section label itself uses a\n * heading that is hidden with an `sr-only` class.\n * To show it to sighted users, define different attributes in\n * `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem, as it links footnote calls to footnote\n * definitions on the page through `id` attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * Example: headings (DOM clobbering) in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * *   when the node has a `value` (and doesn’t have `data.hName`,\n *     `data.hProperties`, or `data.hChildren`, see later), create a hast `text`\n *     node\n * *   otherwise, create a `<div>` element (which could be changed with\n *     `data.hName`), with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @param {MdastNodes} tree\n *   mdast tree.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {HastNodes | null | undefined}\n *   hast tree.\n */\n// To do: next major: always return a single `root`.\nexport function toHast(tree, options) {\n  const state = createState(tree, options)\n  const node = state.one(tree, null)\n  const foot = footer(state)\n\n  if (foot) {\n    // @ts-expect-error If there’s a footer, there were definitions, meaning block\n    // content.\n    // So assume `node` is a parent node.\n    node.children.push({type: 'text', value: '\\n'}, foot)\n  }\n\n  // To do: next major: always return root?\n  return Array.isArray(node) ? {type: 'root', children: node} : node\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,SAAQA,MAAM,QAAO,aAAa;AAClC,SAAQC,WAAW,QAAO,YAAY;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACpC,MAAMC,KAAK,GAAGJ,WAAW,CAACE,IAAI,EAAEC,OAAO,CAAC;EACxC,MAAME,IAAI,GAAGD,KAAK,CAACE,GAAG,CAACJ,IAAI,EAAE,IAAI,CAAC;EAClC,MAAMK,IAAI,GAAGR,MAAM,CAACK,KAAK,CAAC;EAE1B,IAAIG,IAAI,EAAE;IACR;IACA;IACA;IACAF,IAAI,CAACG,QAAQ,CAACC,IAAI,CAAC;MAACC,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAI,CAAC,EAAEJ,IAAI,CAAC;EACvD;;EAEA;EACA,OAAOK,KAAK,CAACC,OAAO,CAACR,IAAI,CAAC,GAAG;IAACK,IAAI,EAAE,MAAM;IAAEF,QAAQ,EAAEH;EAAI,CAAC,GAAGA,IAAI;AACpE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}