{"ast": null, "code": "/**\n * @typedef {import('./lib/index.js').GetDefinition} GetDefinition\n */\n\nexport { definitions } from './lib/index.js';", "map": {"version": 3, "names": ["definitions"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/mdast-util-definitions/index.js"], "sourcesContent": ["/**\n * @typedef {import('./lib/index.js').GetDefinition} GetDefinition\n */\n\nexport {definitions} from './lib/index.js'\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAAQA,WAAW,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}