{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoice-extracting-agent\\\\src\\\\components\\\\SuggestedQuestions.js\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { FaQuestionCircle, FaDollarSign, FaCalendarAlt, FaChartLine, FaNewspaper, FaGlobe } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SuggestedQuestions = ({\n  onQuestionClick,\n  hasInvoice\n}) => {\n  const billingQuestions = [{\n    icon: FaDollarSign,\n    text: \"What is my amount due?\",\n    color: \"text-green-400\"\n  }, {\n    icon: FaCalendarAlt,\n    text: \"When is this bill due?\",\n    color: \"text-orange-400\"\n  }, {\n    icon: FaQuestionCircle,\n    text: \"What services am I being charged for?\",\n    color: \"text-blue-400\"\n  }, {\n    icon: FaDollarSign,\n    text: \"Pay this bill\",\n    color: \"text-purple-400\"\n  }];\n  const generalQuestions = [{\n    icon: FaChartLine,\n    text: \"Why are mutual fund returns low this month?\",\n    color: \"text-red-400\"\n  }, {\n    icon: FaChartLine,\n    text: \"What's causing the stock market decline?\",\n    color: \"text-yellow-400\"\n  }, {\n    icon: FaNewspaper,\n    text: \"Explain the current inflation trends\",\n    color: \"text-cyan-400\"\n  }, {\n    icon: FaGlobe,\n    text: \"How does the economy affect my investments?\",\n    color: \"text-pink-400\"\n  }];\n  const questions = hasInvoice ? billingQuestions : generalQuestions;\n  const containerVariants = {\n    initial: {\n      opacity: 0,\n      y: 20\n    },\n    animate: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const itemVariants = {\n    initial: {\n      opacity: 0,\n      scale: 0.9\n    },\n    animate: {\n      opacity: 1,\n      scale: 1\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"p-4 border-t border-dark-700\",\n    variants: containerVariants,\n    initial: \"initial\",\n    animate: \"animate\",\n    children: [/*#__PURE__*/_jsxDEV(motion.h4, {\n      className: \"text-sm font-medium text-gray-400 mb-3 flex items-center\",\n      variants: itemVariants,\n      children: [/*#__PURE__*/_jsxDEV(FaQuestionCircle, {\n        className: \"mr-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), hasInvoice ? 'Try asking about your bill:' : 'Try these questions:']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 gap-2\",\n      children: questions.map((question, index) => /*#__PURE__*/_jsxDEV(motion.button, {\n        className: \"flex items-center space-x-3 p-3 rounded-lg bg-dark-800/50 hover:bg-dark-700/50 border border-dark-700 hover:border-dark-600 text-left transition-all duration-200 group\",\n        variants: itemVariants,\n        whileHover: {\n          scale: 1.02,\n          y: -2\n        },\n        whileTap: {\n          scale: 0.98\n        },\n        onClick: () => onQuestionClick(question.text),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `p-2 rounded-lg bg-dark-700 ${question.color} group-hover:scale-110 transition-transform`,\n          children: /*#__PURE__*/_jsxDEV(question.icon, {\n            className: \"text-sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-300 text-sm font-medium group-hover:text-white transition-colors\",\n          children: question.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"mt-4 text-center\",\n      variants: itemVariants,\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-500\",\n        children: hasInvoice ? \"I can help you understand your bill and take actions like payments or address changes.\" : \"I can answer questions about finance, markets, or any general topic you're curious about.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_c = SuggestedQuestions;\nexport default SuggestedQuestions;\nvar _c;\n$RefreshReg$(_c, \"SuggestedQuestions\");", "map": {"version": 3, "names": ["React", "motion", "FaQuestionCircle", "FaDollarSign", "FaCalendarAlt", "FaChartLine", "FaNewspaper", "FaGlobe", "jsxDEV", "_jsxDEV", "SuggestedQuestions", "onQuestionClick", "hasInvoice", "billingQuestions", "icon", "text", "color", "generalQuestions", "questions", "containerVariants", "initial", "opacity", "y", "animate", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "itemVariants", "scale", "div", "className", "variants", "children", "h4", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "question", "index", "button", "whileHover", "whileTap", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/src/components/SuggestedQuestions.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { FaQuestionCircle, FaDollarSign, FaCalendarAlt, FaChartLine, FaNewspaper, FaGlobe } from 'react-icons/fa';\n\nconst SuggestedQuestions = ({ onQuestionClick, hasInvoice }) => {\n  const billingQuestions = [\n    {\n      icon: FaDollarSign,\n      text: \"What is my amount due?\",\n      color: \"text-green-400\"\n    },\n    {\n      icon: FaCalendarAlt,\n      text: \"When is this bill due?\",\n      color: \"text-orange-400\"\n    },\n    {\n      icon: FaQuestionCircle,\n      text: \"What services am I being charged for?\",\n      color: \"text-blue-400\"\n    },\n    {\n      icon: FaDollarSign,\n      text: \"Pay this bill\",\n      color: \"text-purple-400\"\n    }\n  ];\n\n  const generalQuestions = [\n    {\n      icon: FaChartLine,\n      text: \"Why are mutual fund returns low this month?\",\n      color: \"text-red-400\"\n    },\n    {\n      icon: FaChartLine,\n      text: \"What's causing the stock market decline?\",\n      color: \"text-yellow-400\"\n    },\n    {\n      icon: FaNewspaper,\n      text: \"Explain the current inflation trends\",\n      color: \"text-cyan-400\"\n    },\n    {\n      icon: FaGlobe,\n      text: \"How does the economy affect my investments?\",\n      color: \"text-pink-400\"\n    }\n  ];\n\n  const questions = hasInvoice ? billingQuestions : generalQuestions;\n\n  const containerVariants = {\n    initial: { opacity: 0, y: 20 },\n    animate: { \n      opacity: 1, \n      y: 0,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    initial: { opacity: 0, scale: 0.9 },\n    animate: { opacity: 1, scale: 1 }\n  };\n\n  return (\n    <motion.div\n      className=\"p-4 border-t border-dark-700\"\n      variants={containerVariants}\n      initial=\"initial\"\n      animate=\"animate\"\n    >\n      <motion.h4 \n        className=\"text-sm font-medium text-gray-400 mb-3 flex items-center\"\n        variants={itemVariants}\n      >\n        <FaQuestionCircle className=\"mr-2\" />\n        {hasInvoice ? 'Try asking about your bill:' : 'Try these questions:'}\n      </motion.h4>\n      \n      <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-2\">\n        {questions.map((question, index) => (\n          <motion.button\n            key={index}\n            className=\"flex items-center space-x-3 p-3 rounded-lg bg-dark-800/50 hover:bg-dark-700/50 border border-dark-700 hover:border-dark-600 text-left transition-all duration-200 group\"\n            variants={itemVariants}\n            whileHover={{ scale: 1.02, y: -2 }}\n            whileTap={{ scale: 0.98 }}\n            onClick={() => onQuestionClick(question.text)}\n          >\n            <div className={`p-2 rounded-lg bg-dark-700 ${question.color} group-hover:scale-110 transition-transform`}>\n              <question.icon className=\"text-sm\" />\n            </div>\n            <span className=\"text-gray-300 text-sm font-medium group-hover:text-white transition-colors\">\n              {question.text}\n            </span>\n          </motion.button>\n        ))}\n      </div>\n\n      {/* Additional context */}\n      <motion.div\n        className=\"mt-4 text-center\"\n        variants={itemVariants}\n      >\n        <p className=\"text-xs text-gray-500\">\n          {hasInvoice \n            ? \"I can help you understand your bill and take actions like payments or address changes.\"\n            : \"I can answer questions about finance, markets, or any general topic you're curious about.\"\n          }\n        </p>\n      </motion.div>\n    </motion.div>\n  );\n};\n\nexport default SuggestedQuestions;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,gBAAgB,EAAEC,YAAY,EAAEC,aAAa,EAAEC,WAAW,EAAEC,WAAW,EAAEC,OAAO,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElH,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,eAAe;EAAEC;AAAW,CAAC,KAAK;EAC9D,MAAMC,gBAAgB,GAAG,CACvB;IACEC,IAAI,EAAEX,YAAY;IAClBY,IAAI,EAAE,wBAAwB;IAC9BC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,IAAI,EAAEV,aAAa;IACnBW,IAAI,EAAE,wBAAwB;IAC9BC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,IAAI,EAAEZ,gBAAgB;IACtBa,IAAI,EAAE,uCAAuC;IAC7CC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,IAAI,EAAEX,YAAY;IAClBY,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,gBAAgB,GAAG,CACvB;IACEH,IAAI,EAAET,WAAW;IACjBU,IAAI,EAAE,6CAA6C;IACnDC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,IAAI,EAAET,WAAW;IACjBU,IAAI,EAAE,0CAA0C;IAChDC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,IAAI,EAAER,WAAW;IACjBS,IAAI,EAAE,sCAAsC;IAC5CC,KAAK,EAAE;EACT,CAAC,EACD;IACEF,IAAI,EAAEP,OAAO;IACbQ,IAAI,EAAE,6CAA6C;IACnDC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAME,SAAS,GAAGN,UAAU,GAAGC,gBAAgB,GAAGI,gBAAgB;EAElE,MAAME,iBAAiB,GAAG;IACxBC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAC;IAC9BC,OAAO,EAAE;MACPF,OAAO,EAAE,CAAC;MACVC,CAAC,EAAE,CAAC;MACJE,UAAU,EAAE;QACVC,eAAe,EAAE;MACnB;IACF;EACF,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBN,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEM,KAAK,EAAE;IAAI,CAAC;IACnCJ,OAAO,EAAE;MAAEF,OAAO,EAAE,CAAC;MAAEM,KAAK,EAAE;IAAE;EAClC,CAAC;EAED,oBACElB,OAAA,CAACR,MAAM,CAAC2B,GAAG;IACTC,SAAS,EAAC,8BAA8B;IACxCC,QAAQ,EAAEX,iBAAkB;IAC5BC,OAAO,EAAC,SAAS;IACjBG,OAAO,EAAC,SAAS;IAAAQ,QAAA,gBAEjBtB,OAAA,CAACR,MAAM,CAAC+B,EAAE;MACRH,SAAS,EAAC,0DAA0D;MACpEC,QAAQ,EAAEJ,YAAa;MAAAK,QAAA,gBAEvBtB,OAAA,CAACP,gBAAgB;QAAC2B,SAAS,EAAC;MAAM;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACpCxB,UAAU,GAAG,6BAA6B,GAAG,sBAAsB;IAAA;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC,eAEZ3B,OAAA;MAAKoB,SAAS,EAAC,uCAAuC;MAAAE,QAAA,EACnDb,SAAS,CAACmB,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,kBAC7B9B,OAAA,CAACR,MAAM,CAACuC,MAAM;QAEZX,SAAS,EAAC,yKAAyK;QACnLC,QAAQ,EAAEJ,YAAa;QACvBe,UAAU,EAAE;UAAEd,KAAK,EAAE,IAAI;UAAEL,CAAC,EAAE,CAAC;QAAE,CAAE;QACnCoB,QAAQ,EAAE;UAAEf,KAAK,EAAE;QAAK,CAAE;QAC1BgB,OAAO,EAAEA,CAAA,KAAMhC,eAAe,CAAC2B,QAAQ,CAACvB,IAAI,CAAE;QAAAgB,QAAA,gBAE9CtB,OAAA;UAAKoB,SAAS,EAAE,8BAA8BS,QAAQ,CAACtB,KAAK,6CAA8C;UAAAe,QAAA,eACxGtB,OAAA,CAAC6B,QAAQ,CAACxB,IAAI;YAACe,SAAS,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACN3B,OAAA;UAAMoB,SAAS,EAAC,4EAA4E;UAAAE,QAAA,EACzFO,QAAQ,CAACvB;QAAI;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA,GAZFG,KAAK;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAaG,CAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN3B,OAAA,CAACR,MAAM,CAAC2B,GAAG;MACTC,SAAS,EAAC,kBAAkB;MAC5BC,QAAQ,EAAEJ,YAAa;MAAAK,QAAA,eAEvBtB,OAAA;QAAGoB,SAAS,EAAC,uBAAuB;QAAAE,QAAA,EACjCnB,UAAU,GACP,wFAAwF,GACxF;MAA2F;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAE9F;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEjB,CAAC;AAACQ,EAAA,GAlHIlC,kBAAkB;AAoHxB,eAAeA,kBAAkB;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}