{"ast": null, "code": "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Point} Point\n * @typedef {import('./minurl.shared.js').URL} URL\n * @typedef {import('../index.js').Data} Data\n * @typedef {import('../index.js').Value} Value\n */\n\n/**\n * @typedef {Record<string, unknown> & {type: string, position?: Position | undefined}} NodeLike\n *\n * @typedef {'ascii' | 'utf8' | 'utf-8' | 'utf16le' | 'ucs2' | 'ucs-2' | 'base64' | 'base64url' | 'latin1' | 'binary' | 'hex'} BufferEncoding\n *   Encodings supported by the buffer class.\n *\n *   This is a copy of the types from Node, copied to prevent Node globals from\n *   being needed.\n *   Copied from: <https://github.com/DefinitelyTyped/DefinitelyTyped/blob/90a4ec8/types/node/buffer.d.ts#L170>\n *\n * @typedef {Options | URL | Value | VFile} Compatible\n *   Things that can be passed to the constructor.\n *\n * @typedef VFileCoreOptions\n *   Set multiple values.\n * @property {Value | null | undefined} [value]\n *   Set `value`.\n * @property {string | null | undefined} [cwd]\n *   Set `cwd`.\n * @property {Array<string> | null | undefined} [history]\n *   Set `history`.\n * @property {URL | string | null | undefined} [path]\n *   Set `path`.\n * @property {string | null | undefined} [basename]\n *   Set `basename`.\n * @property {string | null | undefined} [stem]\n *   Set `stem`.\n * @property {string | null | undefined} [extname]\n *   Set `extname`.\n * @property {string | null | undefined} [dirname]\n *   Set `dirname`.\n * @property {Data | null | undefined} [data]\n *   Set `data`.\n *\n * @typedef Map\n *   Raw source map.\n *\n *   See:\n *   <https://github.com/mozilla/source-map/blob/58819f0/source-map.d.ts#L15-L23>.\n * @property {number} version\n *   Which version of the source map spec this map is following.\n * @property {Array<string>} sources\n *   An array of URLs to the original source files.\n * @property {Array<string>} names\n *   An array of identifiers which can be referenced by individual mappings.\n * @property {string | undefined} [sourceRoot]\n *   The URL root from which all sources are relative.\n * @property {Array<string> | undefined} [sourcesContent]\n *   An array of contents of the original source files.\n * @property {string} mappings\n *   A string of base64 VLQs which contain the actual mappings.\n * @property {string} file\n *   The generated file this source map is associated with.\n *\n * @typedef {{[key: string]: unknown} & VFileCoreOptions} Options\n *   Configuration.\n *\n *   A bunch of keys that will be shallow copied over to the new file.\n *\n * @typedef {Record<string, unknown>} ReporterSettings\n *   Configuration for reporters.\n */\n\n/**\n * @template {ReporterSettings} Settings\n *   Options type.\n * @callback Reporter\n *   Type for a reporter.\n * @param {Array<VFile>} files\n *   Files to report.\n * @param {Settings} options\n *   Configuration.\n * @returns {string}\n *   Report.\n */\n\nimport bufferLike from 'is-buffer';\nimport { VFileMessage } from 'vfile-message';\nimport { path } from './minpath.js';\nimport { proc } from './minproc.js';\nimport { urlToPath, isUrl } from './minurl.js';\n\n/**\n * Order of setting (least specific to most), we need this because otherwise\n * `{stem: 'a', path: '~/b.js'}` would throw, as a path is needed before a\n * stem can be set.\n *\n * @type {Array<'basename' | 'dirname' | 'extname' | 'history' | 'path' | 'stem'>}\n */\nconst order = ['history', 'path', 'basename', 'stem', 'extname', 'dirname'];\nexport class VFile {\n  /**\n   * Create a new virtual file.\n   *\n   * `options` is treated as:\n   *\n   * *   `string` or `Buffer` — `{value: options}`\n   * *   `URL` — `{path: options}`\n   * *   `VFile` — shallow copies its data over to the new file\n   * *   `object` — all fields are shallow copied over to the new file\n   *\n   * Path related fields are set in the following order (least specific to\n   * most specific): `history`, `path`, `basename`, `stem`, `extname`,\n   * `dirname`.\n   *\n   * You cannot set `dirname` or `extname` without setting either `history`,\n   * `path`, `basename`, or `stem` too.\n   *\n   * @param {Compatible | null | undefined} [value]\n   *   File value.\n   * @returns\n   *   New instance.\n   */\n  constructor(value) {\n    /** @type {Options | VFile} */\n    let options;\n    if (!value) {\n      options = {};\n    } else if (typeof value === 'string' || buffer(value)) {\n      options = {\n        value\n      };\n    } else if (isUrl(value)) {\n      options = {\n        path: value\n      };\n    } else {\n      options = value;\n    }\n\n    /**\n     * Place to store custom information (default: `{}`).\n     *\n     * It’s OK to store custom data directly on the file but moving it to\n     * `data` is recommended.\n     *\n     * @type {Data}\n     */\n    this.data = {};\n\n    /**\n     * List of messages associated with the file.\n     *\n     * @type {Array<VFileMessage>}\n     */\n    this.messages = [];\n\n    /**\n     * List of filepaths the file moved between.\n     *\n     * The first is the original path and the last is the current path.\n     *\n     * @type {Array<string>}\n     */\n    this.history = [];\n\n    /**\n     * Base of `path` (default: `process.cwd()` or `'/'` in browsers).\n     *\n     * @type {string}\n     */\n    this.cwd = proc.cwd();\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * Raw value.\n     *\n     * @type {Value}\n     */\n    this.value;\n\n    // The below are non-standard, they are “well-known”.\n    // As in, used in several tools.\n\n    /**\n     * Whether a file was saved to disk.\n     *\n     * This is used by vfile reporters.\n     *\n     * @type {boolean}\n     */\n    this.stored;\n\n    /**\n     * Custom, non-string, compiled, representation.\n     *\n     * This is used by unified to store non-string results.\n     * One example is when turning markdown into React nodes.\n     *\n     * @type {unknown}\n     */\n    this.result;\n\n    /**\n     * Source map.\n     *\n     * This type is equivalent to the `RawSourceMap` type from the `source-map`\n     * module.\n     *\n     * @type {Map | null | undefined}\n     */\n    this.map;\n    /* eslint-enable no-unused-expressions */\n\n    // Set path related properties in the correct order.\n    let index = -1;\n    while (++index < order.length) {\n      const prop = order[index];\n\n      // Note: we specifically use `in` instead of `hasOwnProperty` to accept\n      // `vfile`s too.\n      if (prop in options && options[prop] !== undefined && options[prop] !== null) {\n        // @ts-expect-error: TS doesn’t understand basic reality.\n        this[prop] = prop === 'history' ? [...options[prop]] : options[prop];\n      }\n    }\n\n    /** @type {string} */\n    let prop;\n\n    // Set non-path related properties.\n    for (prop in options) {\n      // @ts-expect-error: fine to set other things.\n      if (!order.includes(prop)) {\n        // @ts-expect-error: fine to set other things.\n        this[prop] = options[prop];\n      }\n    }\n  }\n\n  /**\n   * Get the full path (example: `'~/index.min.js'`).\n   *\n   * @returns {string}\n   */\n  get path() {\n    return this.history[this.history.length - 1];\n  }\n\n  /**\n   * Set the full path (example: `'~/index.min.js'`).\n   *\n   * Cannot be nullified.\n   * You can set a file URL (a `URL` object with a `file:` protocol) which will\n   * be turned into a path with `url.fileURLToPath`.\n   *\n   * @param {string | URL} path\n   */\n  set path(path) {\n    if (isUrl(path)) {\n      path = urlToPath(path);\n    }\n    assertNonEmpty(path, 'path');\n    if (this.path !== path) {\n      this.history.push(path);\n    }\n  }\n\n  /**\n   * Get the parent path (example: `'~'`).\n   */\n  get dirname() {\n    return typeof this.path === 'string' ? path.dirname(this.path) : undefined;\n  }\n\n  /**\n   * Set the parent path (example: `'~'`).\n   *\n   * Cannot be set if there’s no `path` yet.\n   */\n  set dirname(dirname) {\n    assertPath(this.basename, 'dirname');\n    this.path = path.join(dirname || '', this.basename);\n  }\n\n  /**\n   * Get the basename (including extname) (example: `'index.min.js'`).\n   */\n  get basename() {\n    return typeof this.path === 'string' ? path.basename(this.path) : undefined;\n  }\n\n  /**\n   * Set basename (including extname) (`'index.min.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   */\n  set basename(basename) {\n    assertNonEmpty(basename, 'basename');\n    assertPart(basename, 'basename');\n    this.path = path.join(this.dirname || '', basename);\n  }\n\n  /**\n   * Get the extname (including dot) (example: `'.js'`).\n   */\n  get extname() {\n    return typeof this.path === 'string' ? path.extname(this.path) : undefined;\n  }\n\n  /**\n   * Set the extname (including dot) (example: `'.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be set if there’s no `path` yet.\n   */\n  set extname(extname) {\n    assertPart(extname, 'extname');\n    assertPath(this.dirname, 'extname');\n    if (extname) {\n      if (extname.charCodeAt(0) !== 46 /* `.` */) {\n        throw new Error('`extname` must start with `.`');\n      }\n      if (extname.includes('.', 1)) {\n        throw new Error('`extname` cannot contain multiple dots');\n      }\n    }\n    this.path = path.join(this.dirname, this.stem + (extname || ''));\n  }\n\n  /**\n   * Get the stem (basename w/o extname) (example: `'index.min'`).\n   */\n  get stem() {\n    return typeof this.path === 'string' ? path.basename(this.path, this.extname) : undefined;\n  }\n\n  /**\n   * Set the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   */\n  set stem(stem) {\n    assertNonEmpty(stem, 'stem');\n    assertPart(stem, 'stem');\n    this.path = path.join(this.dirname || '', stem + (this.extname || ''));\n  }\n\n  /**\n   * Serialize the file.\n   *\n   * @param {BufferEncoding | null | undefined} [encoding='utf8']\n   *   Character encoding to understand `value` as when it’s a `Buffer`\n   *   (default: `'utf8'`).\n   * @returns {string}\n   *   Serialized file.\n   */\n  toString(encoding) {\n    return (this.value || '').toString(encoding || undefined);\n  }\n\n  /**\n   * Create a warning message associated with the file.\n   *\n   * Its `fatal` is set to `false` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  message(reason, place, origin) {\n    const message = new VFileMessage(reason, place, origin);\n    if (this.path) {\n      message.name = this.path + ':' + message.name;\n      message.file = this.path;\n    }\n    message.fatal = false;\n    this.messages.push(message);\n    return message;\n  }\n\n  /**\n   * Create an info message associated with the file.\n   *\n   * Its `fatal` is set to `null` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  info(reason, place, origin) {\n    const message = this.message(reason, place, origin);\n    message.fatal = null;\n    return message;\n  }\n\n  /**\n   * Create a fatal error associated with the file.\n   *\n   * Its `fatal` is set to `true` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * > 👉 **Note**: a fatal error means that a file is no longer processable.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {never}\n   *   Message.\n   * @throws {VFileMessage}\n   *   Message.\n   */\n  fail(reason, place, origin) {\n    const message = this.message(reason, place, origin);\n    message.fatal = true;\n    throw message;\n  }\n}\n\n/**\n * Assert that `part` is not a path (as in, does not contain `path.sep`).\n *\n * @param {string | null | undefined} part\n *   File path part.\n * @param {string} name\n *   Part name.\n * @returns {void}\n *   Nothing.\n */\nfunction assertPart(part, name) {\n  if (part && part.includes(path.sep)) {\n    throw new Error('`' + name + '` cannot be a path: did not expect `' + path.sep + '`');\n  }\n}\n\n/**\n * Assert that `part` is not empty.\n *\n * @param {string | undefined} part\n *   Thing.\n * @param {string} name\n *   Part name.\n * @returns {asserts part is string}\n *   Nothing.\n */\nfunction assertNonEmpty(part, name) {\n  if (!part) {\n    throw new Error('`' + name + '` cannot be empty');\n  }\n}\n\n/**\n * Assert `path` exists.\n *\n * @param {string | undefined} path\n *   Path.\n * @param {string} name\n *   Dependency name.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path, name) {\n  if (!path) {\n    throw new Error('Setting `' + name + '` requires `path` to be set too');\n  }\n}\n\n/**\n * Assert `value` is a buffer.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Buffer}\n *   Whether `value` is a Node.js buffer.\n */\nfunction buffer(value) {\n  return bufferLike(value);\n}", "map": {"version": 3, "names": ["bufferLike", "VFileMessage", "path", "proc", "urlToPath", "isUrl", "order", "VFile", "constructor", "value", "options", "buffer", "data", "messages", "history", "cwd", "stored", "result", "map", "index", "length", "prop", "undefined", "includes", "assertNonEmpty", "push", "dirname", "assertPath", "basename", "join", "assertPart", "extname", "charCodeAt", "Error", "stem", "toString", "encoding", "message", "reason", "place", "origin", "name", "file", "fatal", "info", "fail", "part", "sep"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/vfile/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Point} Point\n * @typedef {import('./minurl.shared.js').URL} URL\n * @typedef {import('../index.js').Data} Data\n * @typedef {import('../index.js').Value} Value\n */\n\n/**\n * @typedef {Record<string, unknown> & {type: string, position?: Position | undefined}} NodeLike\n *\n * @typedef {'ascii' | 'utf8' | 'utf-8' | 'utf16le' | 'ucs2' | 'ucs-2' | 'base64' | 'base64url' | 'latin1' | 'binary' | 'hex'} BufferEncoding\n *   Encodings supported by the buffer class.\n *\n *   This is a copy of the types from Node, copied to prevent Node globals from\n *   being needed.\n *   Copied from: <https://github.com/DefinitelyTyped/DefinitelyTyped/blob/90a4ec8/types/node/buffer.d.ts#L170>\n *\n * @typedef {Options | URL | Value | VFile} Compatible\n *   Things that can be passed to the constructor.\n *\n * @typedef VFileCoreOptions\n *   Set multiple values.\n * @property {Value | null | undefined} [value]\n *   Set `value`.\n * @property {string | null | undefined} [cwd]\n *   Set `cwd`.\n * @property {Array<string> | null | undefined} [history]\n *   Set `history`.\n * @property {URL | string | null | undefined} [path]\n *   Set `path`.\n * @property {string | null | undefined} [basename]\n *   Set `basename`.\n * @property {string | null | undefined} [stem]\n *   Set `stem`.\n * @property {string | null | undefined} [extname]\n *   Set `extname`.\n * @property {string | null | undefined} [dirname]\n *   Set `dirname`.\n * @property {Data | null | undefined} [data]\n *   Set `data`.\n *\n * @typedef Map\n *   Raw source map.\n *\n *   See:\n *   <https://github.com/mozilla/source-map/blob/58819f0/source-map.d.ts#L15-L23>.\n * @property {number} version\n *   Which version of the source map spec this map is following.\n * @property {Array<string>} sources\n *   An array of URLs to the original source files.\n * @property {Array<string>} names\n *   An array of identifiers which can be referenced by individual mappings.\n * @property {string | undefined} [sourceRoot]\n *   The URL root from which all sources are relative.\n * @property {Array<string> | undefined} [sourcesContent]\n *   An array of contents of the original source files.\n * @property {string} mappings\n *   A string of base64 VLQs which contain the actual mappings.\n * @property {string} file\n *   The generated file this source map is associated with.\n *\n * @typedef {{[key: string]: unknown} & VFileCoreOptions} Options\n *   Configuration.\n *\n *   A bunch of keys that will be shallow copied over to the new file.\n *\n * @typedef {Record<string, unknown>} ReporterSettings\n *   Configuration for reporters.\n */\n\n/**\n * @template {ReporterSettings} Settings\n *   Options type.\n * @callback Reporter\n *   Type for a reporter.\n * @param {Array<VFile>} files\n *   Files to report.\n * @param {Settings} options\n *   Configuration.\n * @returns {string}\n *   Report.\n */\n\nimport bufferLike from 'is-buffer'\nimport {VFileMessage} from 'vfile-message'\nimport {path} from './minpath.js'\nimport {proc} from './minproc.js'\nimport {urlToPath, isUrl} from './minurl.js'\n\n/**\n * Order of setting (least specific to most), we need this because otherwise\n * `{stem: 'a', path: '~/b.js'}` would throw, as a path is needed before a\n * stem can be set.\n *\n * @type {Array<'basename' | 'dirname' | 'extname' | 'history' | 'path' | 'stem'>}\n */\nconst order = ['history', 'path', 'basename', 'stem', 'extname', 'dirname']\n\nexport class VFile {\n  /**\n   * Create a new virtual file.\n   *\n   * `options` is treated as:\n   *\n   * *   `string` or `Buffer` — `{value: options}`\n   * *   `URL` — `{path: options}`\n   * *   `VFile` — shallow copies its data over to the new file\n   * *   `object` — all fields are shallow copied over to the new file\n   *\n   * Path related fields are set in the following order (least specific to\n   * most specific): `history`, `path`, `basename`, `stem`, `extname`,\n   * `dirname`.\n   *\n   * You cannot set `dirname` or `extname` without setting either `history`,\n   * `path`, `basename`, or `stem` too.\n   *\n   * @param {Compatible | null | undefined} [value]\n   *   File value.\n   * @returns\n   *   New instance.\n   */\n  constructor(value) {\n    /** @type {Options | VFile} */\n    let options\n\n    if (!value) {\n      options = {}\n    } else if (typeof value === 'string' || buffer(value)) {\n      options = {value}\n    } else if (isUrl(value)) {\n      options = {path: value}\n    } else {\n      options = value\n    }\n\n    /**\n     * Place to store custom information (default: `{}`).\n     *\n     * It’s OK to store custom data directly on the file but moving it to\n     * `data` is recommended.\n     *\n     * @type {Data}\n     */\n    this.data = {}\n\n    /**\n     * List of messages associated with the file.\n     *\n     * @type {Array<VFileMessage>}\n     */\n    this.messages = []\n\n    /**\n     * List of filepaths the file moved between.\n     *\n     * The first is the original path and the last is the current path.\n     *\n     * @type {Array<string>}\n     */\n    this.history = []\n\n    /**\n     * Base of `path` (default: `process.cwd()` or `'/'` in browsers).\n     *\n     * @type {string}\n     */\n    this.cwd = proc.cwd()\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * Raw value.\n     *\n     * @type {Value}\n     */\n    this.value\n\n    // The below are non-standard, they are “well-known”.\n    // As in, used in several tools.\n\n    /**\n     * Whether a file was saved to disk.\n     *\n     * This is used by vfile reporters.\n     *\n     * @type {boolean}\n     */\n    this.stored\n\n    /**\n     * Custom, non-string, compiled, representation.\n     *\n     * This is used by unified to store non-string results.\n     * One example is when turning markdown into React nodes.\n     *\n     * @type {unknown}\n     */\n    this.result\n\n    /**\n     * Source map.\n     *\n     * This type is equivalent to the `RawSourceMap` type from the `source-map`\n     * module.\n     *\n     * @type {Map | null | undefined}\n     */\n    this.map\n    /* eslint-enable no-unused-expressions */\n\n    // Set path related properties in the correct order.\n    let index = -1\n\n    while (++index < order.length) {\n      const prop = order[index]\n\n      // Note: we specifically use `in` instead of `hasOwnProperty` to accept\n      // `vfile`s too.\n      if (\n        prop in options &&\n        options[prop] !== undefined &&\n        options[prop] !== null\n      ) {\n        // @ts-expect-error: TS doesn’t understand basic reality.\n        this[prop] = prop === 'history' ? [...options[prop]] : options[prop]\n      }\n    }\n\n    /** @type {string} */\n    let prop\n\n    // Set non-path related properties.\n    for (prop in options) {\n      // @ts-expect-error: fine to set other things.\n      if (!order.includes(prop)) {\n        // @ts-expect-error: fine to set other things.\n        this[prop] = options[prop]\n      }\n    }\n  }\n\n  /**\n   * Get the full path (example: `'~/index.min.js'`).\n   *\n   * @returns {string}\n   */\n  get path() {\n    return this.history[this.history.length - 1]\n  }\n\n  /**\n   * Set the full path (example: `'~/index.min.js'`).\n   *\n   * Cannot be nullified.\n   * You can set a file URL (a `URL` object with a `file:` protocol) which will\n   * be turned into a path with `url.fileURLToPath`.\n   *\n   * @param {string | URL} path\n   */\n  set path(path) {\n    if (isUrl(path)) {\n      path = urlToPath(path)\n    }\n\n    assertNonEmpty(path, 'path')\n\n    if (this.path !== path) {\n      this.history.push(path)\n    }\n  }\n\n  /**\n   * Get the parent path (example: `'~'`).\n   */\n  get dirname() {\n    return typeof this.path === 'string' ? path.dirname(this.path) : undefined\n  }\n\n  /**\n   * Set the parent path (example: `'~'`).\n   *\n   * Cannot be set if there’s no `path` yet.\n   */\n  set dirname(dirname) {\n    assertPath(this.basename, 'dirname')\n    this.path = path.join(dirname || '', this.basename)\n  }\n\n  /**\n   * Get the basename (including extname) (example: `'index.min.js'`).\n   */\n  get basename() {\n    return typeof this.path === 'string' ? path.basename(this.path) : undefined\n  }\n\n  /**\n   * Set basename (including extname) (`'index.min.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   */\n  set basename(basename) {\n    assertNonEmpty(basename, 'basename')\n    assertPart(basename, 'basename')\n    this.path = path.join(this.dirname || '', basename)\n  }\n\n  /**\n   * Get the extname (including dot) (example: `'.js'`).\n   */\n  get extname() {\n    return typeof this.path === 'string' ? path.extname(this.path) : undefined\n  }\n\n  /**\n   * Set the extname (including dot) (example: `'.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be set if there’s no `path` yet.\n   */\n  set extname(extname) {\n    assertPart(extname, 'extname')\n    assertPath(this.dirname, 'extname')\n\n    if (extname) {\n      if (extname.charCodeAt(0) !== 46 /* `.` */) {\n        throw new Error('`extname` must start with `.`')\n      }\n\n      if (extname.includes('.', 1)) {\n        throw new Error('`extname` cannot contain multiple dots')\n      }\n    }\n\n    this.path = path.join(this.dirname, this.stem + (extname || ''))\n  }\n\n  /**\n   * Get the stem (basename w/o extname) (example: `'index.min'`).\n   */\n  get stem() {\n    return typeof this.path === 'string'\n      ? path.basename(this.path, this.extname)\n      : undefined\n  }\n\n  /**\n   * Set the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   */\n  set stem(stem) {\n    assertNonEmpty(stem, 'stem')\n    assertPart(stem, 'stem')\n    this.path = path.join(this.dirname || '', stem + (this.extname || ''))\n  }\n\n  /**\n   * Serialize the file.\n   *\n   * @param {BufferEncoding | null | undefined} [encoding='utf8']\n   *   Character encoding to understand `value` as when it’s a `Buffer`\n   *   (default: `'utf8'`).\n   * @returns {string}\n   *   Serialized file.\n   */\n  toString(encoding) {\n    return (this.value || '').toString(encoding || undefined)\n  }\n\n  /**\n   * Create a warning message associated with the file.\n   *\n   * Its `fatal` is set to `false` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  message(reason, place, origin) {\n    const message = new VFileMessage(reason, place, origin)\n\n    if (this.path) {\n      message.name = this.path + ':' + message.name\n      message.file = this.path\n    }\n\n    message.fatal = false\n\n    this.messages.push(message)\n\n    return message\n  }\n\n  /**\n   * Create an info message associated with the file.\n   *\n   * Its `fatal` is set to `null` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  info(reason, place, origin) {\n    const message = this.message(reason, place, origin)\n\n    message.fatal = null\n\n    return message\n  }\n\n  /**\n   * Create a fatal error associated with the file.\n   *\n   * Its `fatal` is set to `true` and `file` is set to the current file path.\n   * Its added to `file.messages`.\n   *\n   * > 👉 **Note**: a fatal error means that a file is no longer processable.\n   *\n   * @param {string | Error | VFileMessage} reason\n   *   Reason for message, uses the stack and message of the error if given.\n   * @param {Node | NodeLike | Position | Point | null | undefined} [place]\n   *   Place in file where the message occurred.\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {never}\n   *   Message.\n   * @throws {VFileMessage}\n   *   Message.\n   */\n  fail(reason, place, origin) {\n    const message = this.message(reason, place, origin)\n\n    message.fatal = true\n\n    throw message\n  }\n}\n\n/**\n * Assert that `part` is not a path (as in, does not contain `path.sep`).\n *\n * @param {string | null | undefined} part\n *   File path part.\n * @param {string} name\n *   Part name.\n * @returns {void}\n *   Nothing.\n */\nfunction assertPart(part, name) {\n  if (part && part.includes(path.sep)) {\n    throw new Error(\n      '`' + name + '` cannot be a path: did not expect `' + path.sep + '`'\n    )\n  }\n}\n\n/**\n * Assert that `part` is not empty.\n *\n * @param {string | undefined} part\n *   Thing.\n * @param {string} name\n *   Part name.\n * @returns {asserts part is string}\n *   Nothing.\n */\nfunction assertNonEmpty(part, name) {\n  if (!part) {\n    throw new Error('`' + name + '` cannot be empty')\n  }\n}\n\n/**\n * Assert `path` exists.\n *\n * @param {string | undefined} path\n *   Path.\n * @param {string} name\n *   Dependency name.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path, name) {\n  if (!path) {\n    throw new Error('Setting `' + name + '` requires `path` to be set too')\n  }\n}\n\n/**\n * Assert `value` is a buffer.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Buffer}\n *   Whether `value` is a Node.js buffer.\n */\nfunction buffer(value) {\n  return bufferLike(value)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,UAAU,MAAM,WAAW;AAClC,SAAQC,YAAY,QAAO,eAAe;AAC1C,SAAQC,IAAI,QAAO,cAAc;AACjC,SAAQC,IAAI,QAAO,cAAc;AACjC,SAAQC,SAAS,EAAEC,KAAK,QAAO,aAAa;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,KAAK,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC;AAE3E,OAAO,MAAMC,KAAK,CAAC;EACjB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,KAAK,EAAE;IACjB;IACA,IAAIC,OAAO;IAEX,IAAI,CAACD,KAAK,EAAE;MACVC,OAAO,GAAG,CAAC,CAAC;IACd,CAAC,MAAM,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIE,MAAM,CAACF,KAAK,CAAC,EAAE;MACrDC,OAAO,GAAG;QAACD;MAAK,CAAC;IACnB,CAAC,MAAM,IAAIJ,KAAK,CAACI,KAAK,CAAC,EAAE;MACvBC,OAAO,GAAG;QAACR,IAAI,EAAEO;MAAK,CAAC;IACzB,CAAC,MAAM;MACLC,OAAO,GAAGD,KAAK;IACjB;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACG,IAAI,GAAG,CAAC,CAAC;;IAEd;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACC,QAAQ,GAAG,EAAE;;IAElB;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,OAAO,GAAG,EAAE;;IAEjB;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACC,GAAG,GAAGZ,IAAI,CAACY,GAAG,CAAC,CAAC;;IAErB;IACA;AACJ;AACA;AACA;AACA;IACI,IAAI,CAACN,KAAK;;IAEV;IACA;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACO,MAAM;;IAEX;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,MAAM;;IAEX;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,CAACC,GAAG;IACR;;IAEA;IACA,IAAIC,KAAK,GAAG,CAAC,CAAC;IAEd,OAAO,EAAEA,KAAK,GAAGb,KAAK,CAACc,MAAM,EAAE;MAC7B,MAAMC,IAAI,GAAGf,KAAK,CAACa,KAAK,CAAC;;MAEzB;MACA;MACA,IACEE,IAAI,IAAIX,OAAO,IACfA,OAAO,CAACW,IAAI,CAAC,KAAKC,SAAS,IAC3BZ,OAAO,CAACW,IAAI,CAAC,KAAK,IAAI,EACtB;QACA;QACA,IAAI,CAACA,IAAI,CAAC,GAAGA,IAAI,KAAK,SAAS,GAAG,CAAC,GAAGX,OAAO,CAACW,IAAI,CAAC,CAAC,GAAGX,OAAO,CAACW,IAAI,CAAC;MACtE;IACF;;IAEA;IACA,IAAIA,IAAI;;IAER;IACA,KAAKA,IAAI,IAAIX,OAAO,EAAE;MACpB;MACA,IAAI,CAACJ,KAAK,CAACiB,QAAQ,CAACF,IAAI,CAAC,EAAE;QACzB;QACA,IAAI,CAACA,IAAI,CAAC,GAAGX,OAAO,CAACW,IAAI,CAAC;MAC5B;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAInB,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACY,OAAO,CAAC,IAAI,CAACA,OAAO,CAACM,MAAM,GAAG,CAAC,CAAC;EAC9C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIlB,IAAIA,CAACA,IAAI,EAAE;IACb,IAAIG,KAAK,CAACH,IAAI,CAAC,EAAE;MACfA,IAAI,GAAGE,SAAS,CAACF,IAAI,CAAC;IACxB;IAEAsB,cAAc,CAACtB,IAAI,EAAE,MAAM,CAAC;IAE5B,IAAI,IAAI,CAACA,IAAI,KAAKA,IAAI,EAAE;MACtB,IAAI,CAACY,OAAO,CAACW,IAAI,CAACvB,IAAI,CAAC;IACzB;EACF;;EAEA;AACF;AACA;EACE,IAAIwB,OAAOA,CAAA,EAAG;IACZ,OAAO,OAAO,IAAI,CAACxB,IAAI,KAAK,QAAQ,GAAGA,IAAI,CAACwB,OAAO,CAAC,IAAI,CAACxB,IAAI,CAAC,GAAGoB,SAAS;EAC5E;;EAEA;AACF;AACA;AACA;AACA;EACE,IAAII,OAAOA,CAACA,OAAO,EAAE;IACnBC,UAAU,CAAC,IAAI,CAACC,QAAQ,EAAE,SAAS,CAAC;IACpC,IAAI,CAAC1B,IAAI,GAAGA,IAAI,CAAC2B,IAAI,CAACH,OAAO,IAAI,EAAE,EAAE,IAAI,CAACE,QAAQ,CAAC;EACrD;;EAEA;AACF;AACA;EACE,IAAIA,QAAQA,CAAA,EAAG;IACb,OAAO,OAAO,IAAI,CAAC1B,IAAI,KAAK,QAAQ,GAAGA,IAAI,CAAC0B,QAAQ,CAAC,IAAI,CAAC1B,IAAI,CAAC,GAAGoB,SAAS;EAC7E;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAIM,QAAQA,CAACA,QAAQ,EAAE;IACrBJ,cAAc,CAACI,QAAQ,EAAE,UAAU,CAAC;IACpCE,UAAU,CAACF,QAAQ,EAAE,UAAU,CAAC;IAChC,IAAI,CAAC1B,IAAI,GAAGA,IAAI,CAAC2B,IAAI,CAAC,IAAI,CAACH,OAAO,IAAI,EAAE,EAAEE,QAAQ,CAAC;EACrD;;EAEA;AACF;AACA;EACE,IAAIG,OAAOA,CAAA,EAAG;IACZ,OAAO,OAAO,IAAI,CAAC7B,IAAI,KAAK,QAAQ,GAAGA,IAAI,CAAC6B,OAAO,CAAC,IAAI,CAAC7B,IAAI,CAAC,GAAGoB,SAAS;EAC5E;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAIS,OAAOA,CAACA,OAAO,EAAE;IACnBD,UAAU,CAACC,OAAO,EAAE,SAAS,CAAC;IAC9BJ,UAAU,CAAC,IAAI,CAACD,OAAO,EAAE,SAAS,CAAC;IAEnC,IAAIK,OAAO,EAAE;MACX,IAAIA,OAAO,CAACC,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,WAAW;QAC1C,MAAM,IAAIC,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEA,IAAIF,OAAO,CAACR,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE;QAC5B,MAAM,IAAIU,KAAK,CAAC,wCAAwC,CAAC;MAC3D;IACF;IAEA,IAAI,CAAC/B,IAAI,GAAGA,IAAI,CAAC2B,IAAI,CAAC,IAAI,CAACH,OAAO,EAAE,IAAI,CAACQ,IAAI,IAAIH,OAAO,IAAI,EAAE,CAAC,CAAC;EAClE;;EAEA;AACF;AACA;EACE,IAAIG,IAAIA,CAAA,EAAG;IACT,OAAO,OAAO,IAAI,CAAChC,IAAI,KAAK,QAAQ,GAChCA,IAAI,CAAC0B,QAAQ,CAAC,IAAI,CAAC1B,IAAI,EAAE,IAAI,CAAC6B,OAAO,CAAC,GACtCT,SAAS;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAIY,IAAIA,CAACA,IAAI,EAAE;IACbV,cAAc,CAACU,IAAI,EAAE,MAAM,CAAC;IAC5BJ,UAAU,CAACI,IAAI,EAAE,MAAM,CAAC;IACxB,IAAI,CAAChC,IAAI,GAAGA,IAAI,CAAC2B,IAAI,CAAC,IAAI,CAACH,OAAO,IAAI,EAAE,EAAEQ,IAAI,IAAI,IAAI,CAACH,OAAO,IAAI,EAAE,CAAC,CAAC;EACxE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEI,QAAQA,CAACC,QAAQ,EAAE;IACjB,OAAO,CAAC,IAAI,CAAC3B,KAAK,IAAI,EAAE,EAAE0B,QAAQ,CAACC,QAAQ,IAAId,SAAS,CAAC;EAC3D;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEe,OAAOA,CAACC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAC7B,MAAMH,OAAO,GAAG,IAAIpC,YAAY,CAACqC,MAAM,EAAEC,KAAK,EAAEC,MAAM,CAAC;IAEvD,IAAI,IAAI,CAACtC,IAAI,EAAE;MACbmC,OAAO,CAACI,IAAI,GAAG,IAAI,CAACvC,IAAI,GAAG,GAAG,GAAGmC,OAAO,CAACI,IAAI;MAC7CJ,OAAO,CAACK,IAAI,GAAG,IAAI,CAACxC,IAAI;IAC1B;IAEAmC,OAAO,CAACM,KAAK,GAAG,KAAK;IAErB,IAAI,CAAC9B,QAAQ,CAACY,IAAI,CAACY,OAAO,CAAC;IAE3B,OAAOA,OAAO;EAChB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEO,IAAIA,CAACN,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAC1B,MAAMH,OAAO,GAAG,IAAI,CAACA,OAAO,CAACC,MAAM,EAAEC,KAAK,EAAEC,MAAM,CAAC;IAEnDH,OAAO,CAACM,KAAK,GAAG,IAAI;IAEpB,OAAON,OAAO;EAChB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEQ,IAAIA,CAACP,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAE;IAC1B,MAAMH,OAAO,GAAG,IAAI,CAACA,OAAO,CAACC,MAAM,EAAEC,KAAK,EAAEC,MAAM,CAAC;IAEnDH,OAAO,CAACM,KAAK,GAAG,IAAI;IAEpB,MAAMN,OAAO;EACf;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASP,UAAUA,CAACgB,IAAI,EAAEL,IAAI,EAAE;EAC9B,IAAIK,IAAI,IAAIA,IAAI,CAACvB,QAAQ,CAACrB,IAAI,CAAC6C,GAAG,CAAC,EAAE;IACnC,MAAM,IAAId,KAAK,CACb,GAAG,GAAGQ,IAAI,GAAG,sCAAsC,GAAGvC,IAAI,CAAC6C,GAAG,GAAG,GACnE,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASvB,cAAcA,CAACsB,IAAI,EAAEL,IAAI,EAAE;EAClC,IAAI,CAACK,IAAI,EAAE;IACT,MAAM,IAAIb,KAAK,CAAC,GAAG,GAAGQ,IAAI,GAAG,mBAAmB,CAAC;EACnD;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASd,UAAUA,CAACzB,IAAI,EAAEuC,IAAI,EAAE;EAC9B,IAAI,CAACvC,IAAI,EAAE;IACT,MAAM,IAAI+B,KAAK,CAAC,WAAW,GAAGQ,IAAI,GAAG,iCAAiC,CAAC;EACzE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS9B,MAAMA,CAACF,KAAK,EAAE;EACrB,OAAOT,UAAU,CAACS,KAAK,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}