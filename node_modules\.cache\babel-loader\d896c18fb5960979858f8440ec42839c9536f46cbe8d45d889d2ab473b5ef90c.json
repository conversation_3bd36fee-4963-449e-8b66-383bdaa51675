{"ast": null, "code": "/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast-util-from-markdown').Options} Options\n */\n\nimport { fromMarkdown } from 'mdast-util-from-markdown';\n\n/**\n * @this {import('unified').Processor}\n * @type {import('unified').Plugin<[Options?] | void[], string, Root>}\n */\nexport default function remarkParse(options) {\n  /** @type {import('unified').ParserFunction<Root>} */\n  const parser = doc => {\n    // Assume options.\n    const settings = /** @type {Options} */this.data('settings');\n    return fromMarkdown(doc, Object.assign({}, settings, options, {\n      // Note: these options are not in the readme.\n      // The goal is for them to be set by plugins on `data` instead of being\n      // passed by users.\n      extensions: this.data('micromarkExtensions') || [],\n      mdastExtensions: this.data('fromMarkdownExtensions') || []\n    }));\n  };\n  Object.assign(this, {\n    Parser: parser\n  });\n}", "map": {"version": 3, "names": ["fromMarkdown", "remark<PERSON><PERSON><PERSON>", "options", "parser", "doc", "settings", "data", "Object", "assign", "extensions", "mdastExtensions", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/remark-parse/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast-util-from-markdown').Options} Options\n */\n\nimport {fromMarkdown} from 'mdast-util-from-markdown'\n\n/**\n * @this {import('unified').Processor}\n * @type {import('unified').Plugin<[Options?] | void[], string, Root>}\n */\nexport default function remarkParse(options) {\n  /** @type {import('unified').ParserFunction<Root>} */\n  const parser = (doc) => {\n    // Assume options.\n    const settings = /** @type {Options} */ (this.data('settings'))\n\n    return fromMarkdown(\n      doc,\n      Object.assign({}, settings, options, {\n        // Note: these options are not in the readme.\n        // The goal is for them to be set by plugins on `data` instead of being\n        // passed by users.\n        extensions: this.data('micromarkExtensions') || [],\n        mdastExtensions: this.data('fromMarkdownExtensions') || []\n      })\n    )\n  }\n\n  Object.assign(this, {Parser: parser})\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,0BAA0B;;AAErD;AACA;AACA;AACA;AACA,eAAe,SAASC,WAAWA,CAACC,OAAO,EAAE;EAC3C;EACA,MAAMC,MAAM,GAAIC,GAAG,IAAK;IACtB;IACA,MAAMC,QAAQ,GAAG,sBAAwB,IAAI,CAACC,IAAI,CAAC,UAAU,CAAE;IAE/D,OAAON,YAAY,CACjBI,GAAG,EACHG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,QAAQ,EAAEH,OAAO,EAAE;MACnC;MACA;MACA;MACAO,UAAU,EAAE,IAAI,CAACH,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE;MAClDI,eAAe,EAAE,IAAI,CAACJ,IAAI,CAAC,wBAAwB,CAAC,IAAI;IAC1D,CAAC,CACH,CAAC;EACH,CAAC;EAEDC,MAAM,CAACC,MAAM,CAAC,IAAI,EAAE;IAACG,MAAM,EAAER;EAAM,CAAC,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}