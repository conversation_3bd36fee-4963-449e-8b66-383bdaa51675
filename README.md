A Streamlit-based AI assistant that understands your uploaded invoices and allows interactive voice or text-based Q&A. Built using OpenAI GPT-4 and PyMuPDF.

---

## 🚀 Features

- 🔊 Voice commands and responses
- 🧠 GPT-4-based smart invoice summarization
- 📈 Visual comparison of multiple invoices
- 💳 Simulated payment actions
- 🔐 Secure deployment using Streamlit secrets

---

## 📦 Installation

```bash
git clone https://github.com/nams2002/Speech-Oriented-InvoiceSummarizer.git
cd Speech-Oriented-InvoiceSummarizer
pip install -r requirements.txt
