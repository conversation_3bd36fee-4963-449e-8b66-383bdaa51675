{"ast": null, "code": "/**\n * @typedef {import('mdast').Root|import('mdast').Content} Node\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [includeImageAlt=true]\n *   Whether to use `alt` for `image`s.\n * @property {boolean | null | undefined} [includeHtml=true]\n *   Whether to use `value` of HTML.\n */\n\n/** @type {Options} */\nconst emptyOptions = {};\n\n/**\n * Get the text content of a node or list of nodes.\n *\n * Prefers the node’s plain-text fields, otherwise serializes its children,\n * and if the given value is an array, serialize the nodes in it.\n *\n * @param {unknown} value\n *   Thing to serialize, typically `Node`.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `value`.\n */\nexport function toString(value, options) {\n  const settings = options || emptyOptions;\n  const includeImageAlt = typeof settings.includeImageAlt === 'boolean' ? settings.includeImageAlt : true;\n  const includeHtml = typeof settings.includeHtml === 'boolean' ? settings.includeHtml : true;\n  return one(value, includeImageAlt, includeHtml);\n}\n\n/**\n * One node or several nodes.\n *\n * @param {unknown} value\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(value, includeImageAlt, includeHtml) {\n  if (node(value)) {\n    if ('value' in value) {\n      return value.type === 'html' && !includeHtml ? '' : value.value;\n    }\n    if (includeImageAlt && 'alt' in value && value.alt) {\n      return value.alt;\n    }\n    if ('children' in value) {\n      return all(value.children, includeImageAlt, includeHtml);\n    }\n  }\n  if (Array.isArray(value)) {\n    return all(value, includeImageAlt, includeHtml);\n  }\n  return '';\n}\n\n/**\n * Serialize a list of nodes.\n *\n * @param {Array<unknown>} values\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized nodes.\n */\nfunction all(values, includeImageAlt, includeHtml) {\n  /** @type {Array<string>} */\n  const result = [];\n  let index = -1;\n  while (++index < values.length) {\n    result[index] = one(values[index], includeImageAlt, includeHtml);\n  }\n  return result.join('');\n}\n\n/**\n * Check if `value` looks like a node.\n *\n * @param {unknown} value\n *   Thing.\n * @returns {value is Node}\n *   Whether `value` is a node.\n */\nfunction node(value) {\n  return Boolean(value && typeof value === 'object');\n}", "map": {"version": 3, "names": ["emptyOptions", "toString", "value", "options", "settings", "includeImageAlt", "includeHtml", "one", "node", "type", "alt", "all", "children", "Array", "isArray", "values", "result", "index", "length", "join", "Boolean"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/mdast-util-to-string/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('mdast').Root|import('mdast').Content} Node\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [includeImageAlt=true]\n *   Whether to use `alt` for `image`s.\n * @property {boolean | null | undefined} [includeHtml=true]\n *   Whether to use `value` of HTML.\n */\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Get the text content of a node or list of nodes.\n *\n * Prefers the node’s plain-text fields, otherwise serializes its children,\n * and if the given value is an array, serialize the nodes in it.\n *\n * @param {unknown} value\n *   Thing to serialize, typically `Node`.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {string}\n *   Serialized `value`.\n */\nexport function toString(value, options) {\n  const settings = options || emptyOptions\n  const includeImageAlt =\n    typeof settings.includeImageAlt === 'boolean'\n      ? settings.includeImageAlt\n      : true\n  const includeHtml =\n    typeof settings.includeHtml === 'boolean' ? settings.includeHtml : true\n\n  return one(value, includeImageAlt, includeHtml)\n}\n\n/**\n * One node or several nodes.\n *\n * @param {unknown} value\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized node.\n */\nfunction one(value, includeImageAlt, includeHtml) {\n  if (node(value)) {\n    if ('value' in value) {\n      return value.type === 'html' && !includeHtml ? '' : value.value\n    }\n\n    if (includeImageAlt && 'alt' in value && value.alt) {\n      return value.alt\n    }\n\n    if ('children' in value) {\n      return all(value.children, includeImageAlt, includeHtml)\n    }\n  }\n\n  if (Array.isArray(value)) {\n    return all(value, includeImageAlt, includeHtml)\n  }\n\n  return ''\n}\n\n/**\n * Serialize a list of nodes.\n *\n * @param {Array<unknown>} values\n *   Thing to serialize.\n * @param {boolean} includeImageAlt\n *   Include image `alt`s.\n * @param {boolean} includeHtml\n *   Include HTML.\n * @returns {string}\n *   Serialized nodes.\n */\nfunction all(values, includeImageAlt, includeHtml) {\n  /** @type {Array<string>} */\n  const result = []\n  let index = -1\n\n  while (++index < values.length) {\n    result[index] = one(values[index], includeImageAlt, includeHtml)\n  }\n\n  return result.join('')\n}\n\n/**\n * Check if `value` looks like a node.\n *\n * @param {unknown} value\n *   Thing.\n * @returns {value is Node}\n *   Whether `value` is a node.\n */\nfunction node(value) {\n  return Boolean(value && typeof value === 'object')\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAMA,YAAY,GAAG,CAAC,CAAC;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACvC,MAAMC,QAAQ,GAAGD,OAAO,IAAIH,YAAY;EACxC,MAAMK,eAAe,GACnB,OAAOD,QAAQ,CAACC,eAAe,KAAK,SAAS,GACzCD,QAAQ,CAACC,eAAe,GACxB,IAAI;EACV,MAAMC,WAAW,GACf,OAAOF,QAAQ,CAACE,WAAW,KAAK,SAAS,GAAGF,QAAQ,CAACE,WAAW,GAAG,IAAI;EAEzE,OAAOC,GAAG,CAACL,KAAK,EAAEG,eAAe,EAAEC,WAAW,CAAC;AACjD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,GAAGA,CAACL,KAAK,EAAEG,eAAe,EAAEC,WAAW,EAAE;EAChD,IAAIE,IAAI,CAACN,KAAK,CAAC,EAAE;IACf,IAAI,OAAO,IAAIA,KAAK,EAAE;MACpB,OAAOA,KAAK,CAACO,IAAI,KAAK,MAAM,IAAI,CAACH,WAAW,GAAG,EAAE,GAAGJ,KAAK,CAACA,KAAK;IACjE;IAEA,IAAIG,eAAe,IAAI,KAAK,IAAIH,KAAK,IAAIA,KAAK,CAACQ,GAAG,EAAE;MAClD,OAAOR,KAAK,CAACQ,GAAG;IAClB;IAEA,IAAI,UAAU,IAAIR,KAAK,EAAE;MACvB,OAAOS,GAAG,CAACT,KAAK,CAACU,QAAQ,EAAEP,eAAe,EAAEC,WAAW,CAAC;IAC1D;EACF;EAEA,IAAIO,KAAK,CAACC,OAAO,CAACZ,KAAK,CAAC,EAAE;IACxB,OAAOS,GAAG,CAACT,KAAK,EAAEG,eAAe,EAAEC,WAAW,CAAC;EACjD;EAEA,OAAO,EAAE;AACX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,GAAGA,CAACI,MAAM,EAAEV,eAAe,EAAEC,WAAW,EAAE;EACjD;EACA,MAAMU,MAAM,GAAG,EAAE;EACjB,IAAIC,KAAK,GAAG,CAAC,CAAC;EAEd,OAAO,EAAEA,KAAK,GAAGF,MAAM,CAACG,MAAM,EAAE;IAC9BF,MAAM,CAACC,KAAK,CAAC,GAAGV,GAAG,CAACQ,MAAM,CAACE,KAAK,CAAC,EAAEZ,eAAe,EAAEC,WAAW,CAAC;EAClE;EAEA,OAAOU,MAAM,CAACG,IAAI,CAAC,EAAE,CAAC;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASX,IAAIA,CAACN,KAAK,EAAE;EACnB,OAAOkB,OAAO,CAAClB,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}