{"ast": null, "code": "import { useMemo } from 'react';\nimport { copyRawValuesOnly } from '../html/use-props.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nfunction useSVGProps(props, visualState, _isStatic, Component) {\n  const visualProps = useMemo(() => {\n    const state = createSvgRenderState();\n    buildSVGAttrs(state, visualState, {\n      enableHardwareAcceleration: false\n    }, isSVGTag(Component), props.transformTemplate);\n    return {\n      ...state.attrs,\n      style: {\n        ...state.style\n      }\n    };\n  }, [visualState]);\n  if (props.style) {\n    const rawStyles = {};\n    copyRawValuesOnly(rawStyles, props.style, props);\n    visualProps.style = {\n      ...rawStyles,\n      ...visualProps.style\n    };\n  }\n  return visualProps;\n}\nexport { useSVGProps };", "map": {"version": 3, "names": ["useMemo", "copyRawValuesOnly", "buildSVGAttrs", "createSvgRenderState", "isSVGTag", "useSVGProps", "props", "visualState", "_isStatic", "Component", "visualProps", "state", "enableHardwareAcceleration", "transformTemplate", "attrs", "style", "rawStyles"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/framer-motion/dist/es/render/svg/use-props.mjs"], "sourcesContent": ["import { useMemo } from 'react';\nimport { copyRawValuesOnly } from '../html/use-props.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\n\nfunction useSVGProps(props, visualState, _isStatic, Component) {\n    const visualProps = useMemo(() => {\n        const state = createSvgRenderState();\n        buildSVGAttrs(state, visualState, { enableHardwareAcceleration: false }, isSVGTag(Component), props.transformTemplate);\n        return {\n            ...state.attrs,\n            style: { ...state.style },\n        };\n    }, [visualState]);\n    if (props.style) {\n        const rawStyles = {};\n        copyRawValuesOnly(rawStyles, props.style, props);\n        visualProps.style = { ...rawStyles, ...visualProps.style };\n    }\n    return visualProps;\n}\n\nexport { useSVGProps };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAC/B,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,QAAQ,QAAQ,wBAAwB;AAEjD,SAASC,WAAWA,CAACC,KAAK,EAAEC,WAAW,EAAEC,SAAS,EAAEC,SAAS,EAAE;EAC3D,MAAMC,WAAW,GAAGV,OAAO,CAAC,MAAM;IAC9B,MAAMW,KAAK,GAAGR,oBAAoB,CAAC,CAAC;IACpCD,aAAa,CAACS,KAAK,EAAEJ,WAAW,EAAE;MAAEK,0BAA0B,EAAE;IAAM,CAAC,EAAER,QAAQ,CAACK,SAAS,CAAC,EAAEH,KAAK,CAACO,iBAAiB,CAAC;IACtH,OAAO;MACH,GAAGF,KAAK,CAACG,KAAK;MACdC,KAAK,EAAE;QAAE,GAAGJ,KAAK,CAACI;MAAM;IAC5B,CAAC;EACL,CAAC,EAAE,CAACR,WAAW,CAAC,CAAC;EACjB,IAAID,KAAK,CAACS,KAAK,EAAE;IACb,MAAMC,SAAS,GAAG,CAAC,CAAC;IACpBf,iBAAiB,CAACe,SAAS,EAAEV,KAAK,CAACS,KAAK,EAAET,KAAK,CAAC;IAChDI,WAAW,CAACK,KAAK,GAAG;MAAE,GAAGC,SAAS;MAAE,GAAGN,WAAW,CAACK;IAAM,CAAC;EAC9D;EACA,OAAOL,WAAW;AACtB;AAEA,SAASL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}