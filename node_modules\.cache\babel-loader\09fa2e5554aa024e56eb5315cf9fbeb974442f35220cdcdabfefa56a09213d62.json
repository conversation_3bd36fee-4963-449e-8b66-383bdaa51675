{"ast": null, "code": "function compareByTime(a, b) {\n  if (a.at === b.at) {\n    if (a.value === null) return 1;\n    if (b.value === null) return -1;\n    return 0;\n  } else {\n    return a.at - b.at;\n  }\n}\nexport { compareByTime };", "map": {"version": 3, "names": ["compareByTime", "a", "b", "at", "value"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/framer-motion/dist/es/animation/sequence/utils/sort.mjs"], "sourcesContent": ["function compareByTime(a, b) {\n    if (a.at === b.at) {\n        if (a.value === null)\n            return 1;\n        if (b.value === null)\n            return -1;\n        return 0;\n    }\n    else {\n        return a.at - b.at;\n    }\n}\n\nexport { compareByTime };\n"], "mappings": "AAAA,SAASA,aAAaA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAID,CAAC,CAACE,EAAE,KAAKD,CAAC,CAACC,EAAE,EAAE;IACf,IAAIF,CAAC,CAACG,KAAK,KAAK,IAAI,EAChB,OAAO,CAAC;IACZ,IAAIF,CAAC,CAACE,KAAK,KAAK,IAAI,EAChB,OAAO,CAAC,CAAC;IACb,OAAO,CAAC;EACZ,CAAC,MACI;IACD,OAAOH,CAAC,CAACE,EAAE,GAAGD,CAAC,CAACC,EAAE;EACtB;AACJ;AAEA,SAASH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}