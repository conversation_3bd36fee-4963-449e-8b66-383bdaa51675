{"ast": null, "code": "/**\n * @typedef {import('./lib/index.js').Options} Options\n * @typedef {import('./lib/index.js').Processor} Processor\n */\n\nexport { defaultHandlers, all, one } from 'mdast-util-to-hast';\nexport { default } from './lib/index.js';", "map": {"version": 3, "names": ["defaultHandlers", "all", "one", "default"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/remark-rehype/index.js"], "sourcesContent": ["/**\n * @typedef {import('./lib/index.js').Options} Options\n * @typedef {import('./lib/index.js').Processor} Processor\n */\n\nexport {defaultHandlers, all, one} from 'mdast-util-to-hast'\n\nexport {default} from './lib/index.js'\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAAQA,eAAe,EAAEC,GAAG,EAAEC,GAAG,QAAO,oBAAoB;AAE5D,SAAQC,OAAO,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}