{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').InitialConstruct} InitialConstruct\n * @typedef {import('micromark-util-types').Initializer} Initializer\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n */\n\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding } from 'micromark-util-character';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { constants } from 'micromark-util-symbol/constants.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\n\n/** @type {InitialConstruct} */\nexport const content = {\n  tokenize: initializeContent\n};\n\n/**\n * @this {TokenizeContext}\n * @type {Initializer}\n */\nfunction initializeContent(effects) {\n  const contentStart = effects.attempt(this.parser.constructs.contentInitial, afterContentStartConstruct, paragraphInitial);\n  /** @type {Token} */\n  let previous;\n  return contentStart;\n\n  /** @type {State} */\n  function afterContentStartConstruct(code) {\n    assert(code === codes.eof || markdownLineEnding(code), 'expected eol or eof');\n    if (code === codes.eof) {\n      effects.consume(code);\n      return;\n    }\n    effects.enter(types.lineEnding);\n    effects.consume(code);\n    effects.exit(types.lineEnding);\n    return factorySpace(effects, contentStart, types.linePrefix);\n  }\n\n  /** @type {State} */\n  function paragraphInitial(code) {\n    assert(code !== codes.eof && !markdownLineEnding(code), 'expected anything other than a line ending or EOF');\n    effects.enter(types.paragraph);\n    return lineStart(code);\n  }\n\n  /** @type {State} */\n  function lineStart(code) {\n    const token = effects.enter(types.chunkText, {\n      contentType: constants.contentTypeText,\n      previous\n    });\n    if (previous) {\n      previous.next = token;\n    }\n    previous = token;\n    return data(code);\n  }\n\n  /** @type {State} */\n  function data(code) {\n    if (code === codes.eof) {\n      effects.exit(types.chunkText);\n      effects.exit(types.paragraph);\n      effects.consume(code);\n      return;\n    }\n    if (markdownLineEnding(code)) {\n      effects.consume(code);\n      effects.exit(types.chunkText);\n      return lineStart;\n    }\n\n    // Data.\n    effects.consume(code);\n    return data;\n  }\n}", "map": {"version": 3, "names": ["factorySpace", "markdownLineEnding", "codes", "constants", "types", "ok", "assert", "content", "tokenize", "initializeContent", "effects", "contentStart", "attempt", "parser", "constructs", "contentInitial", "afterContentStartConstruct", "paragraphInitial", "previous", "code", "eof", "consume", "enter", "lineEnding", "exit", "linePrefix", "paragraph", "lineStart", "token", "chunkText", "contentType", "contentTypeText", "next", "data"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/micromark/dev/lib/initialize/content.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').InitialConstruct} InitialConstruct\n * @typedef {import('micromark-util-types').Initializer} Initializer\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/** @type {InitialConstruct} */\nexport const content = {tokenize: initializeContent}\n\n/**\n * @this {TokenizeContext}\n * @type {Initializer}\n */\nfunction initializeContent(effects) {\n  const contentStart = effects.attempt(\n    this.parser.constructs.contentInitial,\n    afterContentStartConstruct,\n    paragraphInitial\n  )\n  /** @type {Token} */\n  let previous\n\n  return contentStart\n\n  /** @type {State} */\n  function afterContentStartConstruct(code) {\n    assert(\n      code === codes.eof || markdownLineEnding(code),\n      'expected eol or eof'\n    )\n\n    if (code === codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return factorySpace(effects, contentStart, types.linePrefix)\n  }\n\n  /** @type {State} */\n  function paragraphInitial(code) {\n    assert(\n      code !== codes.eof && !markdownLineEnding(code),\n      'expected anything other than a line ending or EOF'\n    )\n    effects.enter(types.paragraph)\n    return lineStart(code)\n  }\n\n  /** @type {State} */\n  function lineStart(code) {\n    const token = effects.enter(types.chunkText, {\n      contentType: constants.contentTypeText,\n      previous\n    })\n\n    if (previous) {\n      previous.next = token\n    }\n\n    previous = token\n\n    return data(code)\n  }\n\n  /** @type {State} */\n  function data(code) {\n    if (code === codes.eof) {\n      effects.exit(types.chunkText)\n      effects.exit(types.paragraph)\n      effects.consume(code)\n      return\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.consume(code)\n      effects.exit(types.chunkText)\n      return lineStart\n    }\n\n    // Data.\n    effects.consume(code)\n    return data\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,yBAAyB;AACpD,SAAQC,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,SAAS,QAAO,oCAAoC;AAC5D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;;AAEvC;AACA,OAAO,MAAMC,OAAO,GAAG;EAACC,QAAQ,EAAEC;AAAiB,CAAC;;AAEpD;AACA;AACA;AACA;AACA,SAASA,iBAAiBA,CAACC,OAAO,EAAE;EAClC,MAAMC,YAAY,GAAGD,OAAO,CAACE,OAAO,CAClC,IAAI,CAACC,MAAM,CAACC,UAAU,CAACC,cAAc,EACrCC,0BAA0B,EAC1BC,gBACF,CAAC;EACD;EACA,IAAIC,QAAQ;EAEZ,OAAOP,YAAY;;EAEnB;EACA,SAASK,0BAA0BA,CAACG,IAAI,EAAE;IACxCb,MAAM,CACJa,IAAI,KAAKjB,KAAK,CAACkB,GAAG,IAAInB,kBAAkB,CAACkB,IAAI,CAAC,EAC9C,qBACF,CAAC;IAED,IAAIA,IAAI,KAAKjB,KAAK,CAACkB,GAAG,EAAE;MACtBV,OAAO,CAACW,OAAO,CAACF,IAAI,CAAC;MACrB;IACF;IAEAT,OAAO,CAACY,KAAK,CAAClB,KAAK,CAACmB,UAAU,CAAC;IAC/Bb,OAAO,CAACW,OAAO,CAACF,IAAI,CAAC;IACrBT,OAAO,CAACc,IAAI,CAACpB,KAAK,CAACmB,UAAU,CAAC;IAC9B,OAAOvB,YAAY,CAACU,OAAO,EAAEC,YAAY,EAAEP,KAAK,CAACqB,UAAU,CAAC;EAC9D;;EAEA;EACA,SAASR,gBAAgBA,CAACE,IAAI,EAAE;IAC9Bb,MAAM,CACJa,IAAI,KAAKjB,KAAK,CAACkB,GAAG,IAAI,CAACnB,kBAAkB,CAACkB,IAAI,CAAC,EAC/C,mDACF,CAAC;IACDT,OAAO,CAACY,KAAK,CAAClB,KAAK,CAACsB,SAAS,CAAC;IAC9B,OAAOC,SAAS,CAACR,IAAI,CAAC;EACxB;;EAEA;EACA,SAASQ,SAASA,CAACR,IAAI,EAAE;IACvB,MAAMS,KAAK,GAAGlB,OAAO,CAACY,KAAK,CAAClB,KAAK,CAACyB,SAAS,EAAE;MAC3CC,WAAW,EAAE3B,SAAS,CAAC4B,eAAe;MACtCb;IACF,CAAC,CAAC;IAEF,IAAIA,QAAQ,EAAE;MACZA,QAAQ,CAACc,IAAI,GAAGJ,KAAK;IACvB;IAEAV,QAAQ,GAAGU,KAAK;IAEhB,OAAOK,IAAI,CAACd,IAAI,CAAC;EACnB;;EAEA;EACA,SAASc,IAAIA,CAACd,IAAI,EAAE;IAClB,IAAIA,IAAI,KAAKjB,KAAK,CAACkB,GAAG,EAAE;MACtBV,OAAO,CAACc,IAAI,CAACpB,KAAK,CAACyB,SAAS,CAAC;MAC7BnB,OAAO,CAACc,IAAI,CAACpB,KAAK,CAACsB,SAAS,CAAC;MAC7BhB,OAAO,CAACW,OAAO,CAACF,IAAI,CAAC;MACrB;IACF;IAEA,IAAIlB,kBAAkB,CAACkB,IAAI,CAAC,EAAE;MAC5BT,OAAO,CAACW,OAAO,CAACF,IAAI,CAAC;MACrBT,OAAO,CAACc,IAAI,CAACpB,KAAK,CAACyB,SAAS,CAAC;MAC7B,OAAOF,SAAS;IAClB;;IAEA;IACAjB,OAAO,CAACW,OAAO,CAACF,IAAI,CAAC;IACrB,OAAOc,IAAI;EACb;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}