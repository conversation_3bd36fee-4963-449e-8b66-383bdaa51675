{"ast": null, "code": "/**\n * Provided a value and a ValueType, returns the value as that value type.\n */\nconst getValueAsType = (value, type) => {\n  return type && typeof value === \"number\" ? type.transform(value) : value;\n};\nexport { getValueAsType };", "map": {"version": 3, "names": ["getValueAsType", "value", "type", "transform"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/framer-motion/dist/es/render/dom/value-types/get-as-type.mjs"], "sourcesContent": ["/**\n * Provided a value and a ValueType, returns the value as that value type.\n */\nconst getValueAsType = (value, type) => {\n    return type && typeof value === \"number\"\n        ? type.transform(value)\n        : value;\n};\n\nexport { getValueAsType };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,cAAc,GAAGA,CAACC,KAAK,EAAEC,IAAI,KAAK;EACpC,OAAOA,IAAI,IAAI,OAAOD,KAAK,KAAK,QAAQ,GAClCC,IAAI,CAACC,SAAS,CAACF,KAAK,CAAC,GACrBA,KAAK;AACf,CAAC;AAED,SAASD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}