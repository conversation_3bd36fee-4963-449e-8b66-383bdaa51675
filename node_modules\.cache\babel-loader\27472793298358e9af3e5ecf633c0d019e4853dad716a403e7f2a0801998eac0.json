{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Footnote} Footnote\n * @typedef {import('../state.js').State} State\n */\n\nimport { footnoteReference } from './footnote-reference.js';\n\n// To do: when both:\n// * <https://github.com/micromark/micromark-extension-footnote>\n// * <https://github.com/syntax-tree/mdast-util-footnote>\n// …are archived, remove this (also from mdast).\n// These inline notes are not used in GFM.\n\n/**\n * Turn an mdast `footnote` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Footnote} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function footnote(state, node) {\n  const footnoteById = state.footnoteById;\n  let no = 1;\n  while (no in footnoteById) no++;\n  const identifier = String(no);\n  footnoteById[identifier] = {\n    type: 'footnoteDefinition',\n    identifier,\n    children: [{\n      type: 'paragraph',\n      children: node.children\n    }],\n    position: node.position\n  };\n  return footnoteReference(state, {\n    type: 'footnoteReference',\n    identifier,\n    position: node.position\n  });\n}", "map": {"version": 3, "names": ["footnoteReference", "footnote", "state", "node", "footnoteById", "no", "identifier", "String", "type", "children", "position"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/mdast-util-to-hast/lib/handlers/footnote.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Footnote} Footnote\n * @typedef {import('../state.js').State} State\n */\n\nimport {footnoteReference} from './footnote-reference.js'\n\n// To do: when both:\n// * <https://github.com/micromark/micromark-extension-footnote>\n// * <https://github.com/syntax-tree/mdast-util-footnote>\n// …are archived, remove this (also from mdast).\n// These inline notes are not used in GFM.\n\n/**\n * Turn an mdast `footnote` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Footnote} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function footnote(state, node) {\n  const footnoteById = state.footnoteById\n  let no = 1\n\n  while (no in footnoteById) no++\n\n  const identifier = String(no)\n\n  footnoteById[identifier] = {\n    type: 'footnoteDefinition',\n    identifier,\n    children: [{type: 'paragraph', children: node.children}],\n    position: node.position\n  }\n\n  return footnoteReference(state, {\n    type: 'footnoteReference',\n    identifier,\n    position: node.position\n  })\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,iBAAiB,QAAO,yBAAyB;;AAEzD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,QAAQA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACpC,MAAMC,YAAY,GAAGF,KAAK,CAACE,YAAY;EACvC,IAAIC,EAAE,GAAG,CAAC;EAEV,OAAOA,EAAE,IAAID,YAAY,EAAEC,EAAE,EAAE;EAE/B,MAAMC,UAAU,GAAGC,MAAM,CAACF,EAAE,CAAC;EAE7BD,YAAY,CAACE,UAAU,CAAC,GAAG;IACzBE,IAAI,EAAE,oBAAoB;IAC1BF,UAAU;IACVG,QAAQ,EAAE,CAAC;MAACD,IAAI,EAAE,WAAW;MAAEC,QAAQ,EAAEN,IAAI,CAACM;IAAQ,CAAC,CAAC;IACxDC,QAAQ,EAAEP,IAAI,CAACO;EACjB,CAAC;EAED,OAAOV,iBAAiB,CAACE,KAAK,EAAE;IAC9BM,IAAI,EAAE,mBAAmB;IACzBF,UAAU;IACVI,QAAQ,EAAEP,IAAI,CAACO;EACjB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}