{"ast": null, "code": "/**\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n */\n\n/**\n * Get the starting point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointStart = point('start');\n\n/**\n * Get the ending point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointEnd = point('end');\n\n/**\n * Get the positional info of `node`.\n *\n * @param {NodeLike | Node | null | undefined} [node]\n *   Node.\n * @returns {Position}\n *   Position.\n */\nexport function position(node) {\n  return {\n    start: pointStart(node),\n    end: pointEnd(node)\n  };\n}\n\n/**\n * Get the positional info of `node`.\n *\n * @param {'start' | 'end'} type\n *   Side.\n * @returns\n *   Getter.\n */\nfunction point(type) {\n  return point;\n\n  /**\n   * Get the point info of `node` at a bound side.\n   *\n   * @param {NodeLike | Node | null | undefined} [node]\n   * @returns {Point}\n   */\n  function point(node) {\n    const point = node && node.position && node.position[type] || {};\n\n    // To do: next major: don’t return points when invalid.\n    return {\n      // @ts-expect-error: in practice, null is allowed.\n      line: point.line || null,\n      // @ts-expect-error: in practice, null is allowed.\n      column: point.column || null,\n      // @ts-expect-error: in practice, null is allowed.\n      offset: point.offset > -1 ? point.offset : null\n    };\n  }\n}", "map": {"version": 3, "names": ["pointStart", "point", "pointEnd", "position", "node", "start", "end", "type", "line", "column", "offset"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/unist-util-position/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n */\n\n/**\n * Get the starting point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointStart = point('start')\n\n/**\n * Get the ending point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointEnd = point('end')\n\n/**\n * Get the positional info of `node`.\n *\n * @param {NodeLike | Node | null | undefined} [node]\n *   Node.\n * @returns {Position}\n *   Position.\n */\nexport function position(node) {\n  return {start: pointStart(node), end: pointEnd(node)}\n}\n\n/**\n * Get the positional info of `node`.\n *\n * @param {'start' | 'end'} type\n *   Side.\n * @returns\n *   Getter.\n */\nfunction point(type) {\n  return point\n\n  /**\n   * Get the point info of `node` at a bound side.\n   *\n   * @param {NodeLike | Node | null | undefined} [node]\n   * @returns {Point}\n   */\n  function point(node) {\n    const point = (node && node.position && node.position[type]) || {}\n\n    // To do: next major: don’t return points when invalid.\n    return {\n      // @ts-expect-error: in practice, null is allowed.\n      line: point.line || null,\n      // @ts-expect-error: in practice, null is allowed.\n      column: point.column || null,\n      // @ts-expect-error: in practice, null is allowed.\n      offset: point.offset > -1 ? point.offset : null\n    }\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,UAAU,GAAGC,KAAK,CAAC,OAAO,CAAC;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,QAAQ,GAAGD,KAAK,CAAC,KAAK,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,QAAQA,CAACC,IAAI,EAAE;EAC7B,OAAO;IAACC,KAAK,EAAEL,UAAU,CAACI,IAAI,CAAC;IAAEE,GAAG,EAAEJ,QAAQ,CAACE,IAAI;EAAC,CAAC;AACvD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASH,KAAKA,CAACM,IAAI,EAAE;EACnB,OAAON,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACG,IAAI,EAAE;IACnB,MAAMH,KAAK,GAAIG,IAAI,IAAIA,IAAI,CAACD,QAAQ,IAAIC,IAAI,CAACD,QAAQ,CAACI,IAAI,CAAC,IAAK,CAAC,CAAC;;IAElE;IACA,OAAO;MACL;MACAC,IAAI,EAAEP,KAAK,CAACO,IAAI,IAAI,IAAI;MACxB;MACAC,MAAM,EAAER,KAAK,CAACQ,MAAM,IAAI,IAAI;MAC5B;MACAC,MAAM,EAAET,KAAK,CAACS,MAAM,GAAG,CAAC,CAAC,GAAGT,KAAK,CAACS,MAAM,GAAG;IAC7C,CAAC;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}