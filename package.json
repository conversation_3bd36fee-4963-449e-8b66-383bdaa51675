{"name": "agentic-ai-billing-assistant", "version": "1.0.0", "description": "Agentic AI for Billing, Payments and Intelligent Customer Interactions", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4", "axios": "^1.4.0", "framer-motion": "^10.16.4", "react-dropzone": "^14.2.3", "react-icons": "^4.10.1", "styled-components": "^6.0.7", "react-speech-kit": "^3.0.1", "react-markdown": "^8.0.7", "react-syntax-highlighter": "^15.5.0", "uuid": "^9.0.0", "date-fns": "^2.30.0"}, "devDependencies": {"@types/uuid": "^9.0.2", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "tailwindcss": "^3.3.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}