{"name": "agentic-ai-billing-assistant", "version": "1.0.0", "description": "Agentic AI for Billing, Payments and Intelligent Customer Interactions", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.4.0", "compression": "^1.8.1", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^17.2.0", "express": "^4.18.2", "express-rate-limit": "^8.0.1", "framer-motion": "^10.16.4", "helmet": "^8.1.0", "morgan": "^1.10.1", "multer": "^2.0.2", "openai": "^5.10.1", "pdf-parse": "^1.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-icons": "^4.10.1", "react-markdown": "^8.0.7", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.5.0", "styled-components": "^6.0.7", "uuid": "^9.0.1", "web-vitals": "^2.1.4"}, "devDependencies": {"@types/uuid": "^9.0.2", "autoprefixer": "^10.4.14", "nodemon": "^3.1.10", "postcss": "^8.4.24", "tailwindcss": "^3.3.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}