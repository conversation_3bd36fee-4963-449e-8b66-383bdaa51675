{"ast": null, "code": "/**\n * @typedef {import('hast').Content} HastContent\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').ElementContent} HastElementContent\n * @typedef {import('hast').Properties} HastProperties\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('hast').Text} HastText\n *\n * @typedef {import('mdast').Content} MdastContent\n * @typedef {import('mdast').Definition} MdastDefinition\n * @typedef {import('mdast').FootnoteDefinition} MdastFootnoteDefinition\n * @typedef {import('mdast').Parent} MdastParent\n * @typedef {import('mdast').Root} MdastRoot\n */\n\n/**\n * @typedef {HastRoot | HastContent} HastNodes\n * @typedef {MdastRoot | MdastContent} MdastNodes\n * @typedef {Extract<MdastNodes, MdastParent>} MdastParents\n *\n * @typedef EmbeddedHastFields\n *   hast fields.\n * @property {string | null | undefined} [hName]\n *   Generate a specific element with this tag name instead.\n * @property {HastProperties | null | undefined} [hProperties]\n *   Generate an element with these properties instead.\n * @property {Array<HastElementContent> | null | undefined} [hChildren]\n *   Generate an element with this content instead.\n *\n * @typedef {Record<string, unknown> & EmbeddedHastFields} MdastData\n *   mdast data with embedded hast fields.\n *\n * @typedef {MdastNodes & {data?: MdastData | null | undefined}} MdastNodeWithData\n *   mdast node with embedded hast data.\n *\n * @typedef PointLike\n *   Point-like value.\n * @property {number | null | undefined} [line]\n *   Line.\n * @property {number | null | undefined} [column]\n *   Column.\n * @property {number | null | undefined} [offset]\n *   Offset.\n *\n * @typedef PositionLike\n *   Position-like value.\n * @property {PointLike | null | undefined} [start]\n *   Point-like value.\n * @property {PointLike | null | undefined} [end]\n *   Point-like value.\n *\n * @callback Handler\n *   Handle a node.\n * @param {State} state\n *   Info passed around.\n * @param {any} node\n *   mdast node to handle.\n * @param {MdastParents | null | undefined} parent\n *   Parent of `node`.\n * @returns {HastElementContent | Array<HastElementContent> | null | undefined}\n *   hast node.\n *\n * @callback HFunctionProps\n *   Signature of `state` for when props are passed.\n * @param {MdastNodes | PositionLike | null | undefined} node\n *   mdast node or unist position.\n * @param {string} tagName\n *   HTML tag name.\n * @param {HastProperties} props\n *   Properties.\n * @param {Array<HastElementContent> | null | undefined} [children]\n *   hast content.\n * @returns {HastElement}\n *   Compiled element.\n *\n * @callback HFunctionNoProps\n *   Signature of `state` for when no props are passed.\n * @param {MdastNodes | PositionLike | null | undefined} node\n *   mdast node or unist position.\n * @param {string} tagName\n *   HTML tag name.\n * @param {Array<HastElementContent> | null | undefined} [children]\n *   hast content.\n * @returns {HastElement}\n *   Compiled element.\n *\n * @typedef HFields\n *   Info on `state`.\n * @property {boolean} dangerous\n *   Whether HTML is allowed.\n * @property {string} clobberPrefix\n *   Prefix to use to prevent DOM clobbering.\n * @property {string} footnoteLabel\n *   Label to use to introduce the footnote section.\n * @property {string} footnoteLabelTagName\n *   HTML used for the footnote label.\n * @property {HastProperties} footnoteLabelProperties\n *   Properties on the HTML tag used for the footnote label.\n * @property {string} footnoteBackLabel\n *   Label to use from backreferences back to their footnote call.\n * @property {(identifier: string) => MdastDefinition | null} definition\n *   Definition cache.\n * @property {Record<string, MdastFootnoteDefinition>} footnoteById\n *   Footnote definitions by their identifier.\n * @property {Array<string>} footnoteOrder\n *   Identifiers of order when footnote calls first appear in tree order.\n * @property {Record<string, number>} footnoteCounts\n *   Counts for how often the same footnote was called.\n * @property {Handlers} handlers\n *   Applied handlers.\n * @property {Handler} unknownHandler\n *   Handler for any none not in `passThrough` or otherwise handled.\n * @property {(from: MdastNodes, node: HastNodes) => void} patch\n *   Copy a node’s positional info.\n * @property {<Type extends HastNodes>(from: MdastNodes, to: Type) => Type | HastElement} applyData\n *   Honor the `data` of `from`, and generate an element instead of `node`.\n * @property {(node: MdastNodes, parent: MdastParents | null | undefined) => HastElementContent | Array<HastElementContent> | null | undefined} one\n *   Transform an mdast node to hast.\n * @property {(node: MdastNodes) => Array<HastElementContent>} all\n *   Transform the children of an mdast parent to hast.\n * @property {<Type extends HastContent>(nodes: Array<Type>, loose?: boolean | null | undefined) => Array<Type | HastText>} wrap\n *   Wrap `nodes` with line endings between each node, adds initial/final line endings when `loose`.\n * @property {(left: MdastNodeWithData | PositionLike | null | undefined, right: HastElementContent) => HastElementContent} augment\n *   Like `state` but lower-level and usable on non-elements.\n *   Deprecated: use `patch` and `applyData`.\n * @property {Array<string>} passThrough\n *   List of node types to pass through untouched (except for their children).\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Whether to persist raw HTML in markdown in the hast tree.\n * @property {string | null | undefined} [clobberPrefix='user-content-']\n *   Prefix to use before the `id` attribute on footnotes to prevent it from\n *   *clobbering*.\n * @property {string | null | undefined} [footnoteBackLabel='Back to content']\n *   Label to use from backreferences back to their footnote call (affects\n *   screen readers).\n * @property {string | null | undefined} [footnoteLabel='Footnotes']\n *   Label to use for the footnotes section (affects screen readers).\n * @property {HastProperties | null | undefined} [footnoteLabelProperties={className: ['sr-only']}]\n *   Properties to use on the footnote label (note that `id: 'footnote-label'`\n *   is always added as footnote calls use it with `aria-describedby` to\n *   provide an accessible label).\n * @property {string | null | undefined} [footnoteLabelTagName='h2']\n *   Tag name to use for the footnote label.\n * @property {Handlers | null | undefined} [handlers]\n *   Extra handlers for nodes.\n * @property {Array<string> | null | undefined} [passThrough]\n *   List of custom mdast node types to pass through (keep) in hast (note that\n *   the node itself is passed, but eventual children are transformed).\n * @property {Handler | null | undefined} [unknownHandler]\n *   Handler for all unknown nodes.\n *\n * @typedef {Record<string, Handler>} Handlers\n *   Handle nodes.\n *\n * @typedef {HFunctionProps & HFunctionNoProps & HFields} State\n *   Info passed around.\n */\n\nimport { visit } from 'unist-util-visit';\nimport { position, pointStart, pointEnd } from 'unist-util-position';\nimport { generated } from 'unist-util-generated';\nimport { definitions } from 'mdast-util-definitions';\nimport { handlers } from './handlers/index.js';\nconst own = {}.hasOwnProperty;\n\n/**\n * Create `state` from an mdast tree.\n *\n * @param {MdastNodes} tree\n *   mdast node to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {State}\n *   `state` function.\n */\nexport function createState(tree, options) {\n  const settings = options || {};\n  const dangerous = settings.allowDangerousHtml || false;\n  /** @type {Record<string, MdastFootnoteDefinition>} */\n  const footnoteById = {};\n\n  // To do: next major: add `options` to state, remove:\n  // `dangerous`, `clobberPrefix`, `footnoteLabel`, `footnoteLabelTagName`,\n  // `footnoteLabelProperties`, `footnoteBackLabel`, `passThrough`,\n  // `unknownHandler`.\n\n  // To do: next major: move to `state.options.allowDangerousHtml`.\n  state.dangerous = dangerous;\n  // To do: next major: move to `state.options`.\n  state.clobberPrefix = settings.clobberPrefix === undefined || settings.clobberPrefix === null ? 'user-content-' : settings.clobberPrefix;\n  // To do: next major: move to `state.options`.\n  state.footnoteLabel = settings.footnoteLabel || 'Footnotes';\n  // To do: next major: move to `state.options`.\n  state.footnoteLabelTagName = settings.footnoteLabelTagName || 'h2';\n  // To do: next major: move to `state.options`.\n  state.footnoteLabelProperties = settings.footnoteLabelProperties || {\n    className: ['sr-only']\n  };\n  // To do: next major: move to `state.options`.\n  state.footnoteBackLabel = settings.footnoteBackLabel || 'Back to content';\n  // To do: next major: move to `state.options`.\n  state.unknownHandler = settings.unknownHandler;\n  // To do: next major: move to `state.options`.\n  state.passThrough = settings.passThrough;\n  state.handlers = {\n    ...handlers,\n    ...settings.handlers\n  };\n\n  // To do: next major: replace utility with `definitionById` object, so we\n  // only walk once (as we need footnotes too).\n  state.definition = definitions(tree);\n  state.footnoteById = footnoteById;\n  /** @type {Array<string>} */\n  state.footnoteOrder = [];\n  /** @type {Record<string, number>} */\n  state.footnoteCounts = {};\n  state.patch = patch;\n  state.applyData = applyData;\n  state.one = oneBound;\n  state.all = allBound;\n  state.wrap = wrap;\n  // To do: next major: remove `augment`.\n  state.augment = augment;\n  visit(tree, 'footnoteDefinition', definition => {\n    const id = String(definition.identifier).toUpperCase();\n\n    // Mimick CM behavior of link definitions.\n    // See: <https://github.com/syntax-tree/mdast-util-definitions/blob/8290999/index.js#L26>.\n    if (!own.call(footnoteById, id)) {\n      footnoteById[id] = definition;\n    }\n  });\n\n  // @ts-expect-error Hush, it’s fine!\n  return state;\n\n  /**\n   * Finalise the created `right`, a hast node, from `left`, an mdast node.\n   *\n   * @param {MdastNodeWithData | PositionLike | null | undefined} left\n   * @param {HastElementContent} right\n   * @returns {HastElementContent}\n   */\n  /* c8 ignore start */\n  // To do: next major: remove.\n  function augment(left, right) {\n    // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n    if (left && 'data' in left && left.data) {\n      /** @type {MdastData} */\n      const data = left.data;\n      if (data.hName) {\n        if (right.type !== 'element') {\n          right = {\n            type: 'element',\n            tagName: '',\n            properties: {},\n            children: []\n          };\n        }\n        right.tagName = data.hName;\n      }\n      if (right.type === 'element' && data.hProperties) {\n        right.properties = {\n          ...right.properties,\n          ...data.hProperties\n        };\n      }\n      if ('children' in right && right.children && data.hChildren) {\n        right.children = data.hChildren;\n      }\n    }\n    if (left) {\n      const ctx = 'type' in left ? left : {\n        position: left\n      };\n      if (!generated(ctx)) {\n        // @ts-expect-error: fine.\n        right.position = {\n          start: pointStart(ctx),\n          end: pointEnd(ctx)\n        };\n      }\n    }\n    return right;\n  }\n  /* c8 ignore stop */\n\n  /**\n   * Create an element for `node`.\n   *\n   * @type {HFunctionProps}\n   */\n  /* c8 ignore start */\n  // To do: next major: remove.\n  function state(node, tagName, props, children) {\n    if (Array.isArray(props)) {\n      children = props;\n      props = {};\n    }\n\n    // @ts-expect-error augmenting an element yields an element.\n    return augment(node, {\n      type: 'element',\n      tagName,\n      properties: props || {},\n      children: children || []\n    });\n  }\n  /* c8 ignore stop */\n\n  /**\n   * Transform an mdast node into a hast node.\n   *\n   * @param {MdastNodes} node\n   *   mdast node.\n   * @param {MdastParents | null | undefined} [parent]\n   *   Parent of `node`.\n   * @returns {HastElementContent | Array<HastElementContent> | null | undefined}\n   *   Resulting hast node.\n   */\n  function oneBound(node, parent) {\n    // @ts-expect-error: that’s a state :)\n    return one(state, node, parent);\n  }\n\n  /**\n   * Transform the children of an mdast node into hast nodes.\n   *\n   * @param {MdastNodes} parent\n   *   mdast node to compile\n   * @returns {Array<HastElementContent>}\n   *   Resulting hast nodes.\n   */\n  function allBound(parent) {\n    // @ts-expect-error: that’s a state :)\n    return all(state, parent);\n  }\n}\n\n/**\n * Copy a node’s positional info.\n *\n * @param {MdastNodes} from\n *   mdast node to copy from.\n * @param {HastNodes} to\n *   hast node to copy into.\n * @returns {void}\n *   Nothing.\n */\nfunction patch(from, to) {\n  if (from.position) to.position = position(from);\n}\n\n/**\n * Honor the `data` of `from` and maybe generate an element instead of `to`.\n *\n * @template {HastNodes} Type\n *   Node type.\n * @param {MdastNodes} from\n *   mdast node to use data from.\n * @param {Type} to\n *   hast node to change.\n * @returns {Type | HastElement}\n *   Nothing.\n */\nfunction applyData(from, to) {\n  /** @type {Type | HastElement} */\n  let result = to;\n\n  // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n  if (from && from.data) {\n    const hName = from.data.hName;\n    const hChildren = from.data.hChildren;\n    const hProperties = from.data.hProperties;\n    if (typeof hName === 'string') {\n      // Transforming the node resulted in an element with a different name\n      // than wanted:\n      if (result.type === 'element') {\n        result.tagName = hName;\n      }\n      // Transforming the node resulted in a non-element, which happens for\n      // raw, text, and root nodes (unless custom handlers are passed).\n      // The intent is likely to keep the content around (otherwise: pass\n      // `hChildren`).\n      else {\n        result = {\n          type: 'element',\n          tagName: hName,\n          properties: {},\n          children: []\n        };\n\n        // To do: next major: take the children from the `root`, or inject the\n        // raw/text/comment or so into the element?\n        // if ('children' in node) {\n        //   // @ts-expect-error: assume `children` are allowed in elements.\n        //   result.children = node.children\n        // } else {\n        //   // @ts-expect-error: assume `node` is allowed in elements.\n        //   result.children.push(node)\n        // }\n      }\n    }\n    if (result.type === 'element' && hProperties) {\n      result.properties = {\n        ...result.properties,\n        ...hProperties\n      };\n    }\n    if ('children' in result && result.children && hChildren !== null && hChildren !== undefined) {\n      // @ts-expect-error: assume valid children are defined.\n      result.children = hChildren;\n    }\n  }\n  return result;\n}\n\n/**\n * Transform an mdast node into a hast node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   mdast node.\n * @param {MdastParents | null | undefined} [parent]\n *   Parent of `node`.\n * @returns {HastElementContent | Array<HastElementContent> | null | undefined}\n *   Resulting hast node.\n */\n// To do: next major: do not expose, keep bound.\nexport function one(state, node, parent) {\n  const type = node && node.type;\n\n  // Fail on non-nodes.\n  if (!type) {\n    throw new Error('Expected node, got `' + node + '`');\n  }\n  if (own.call(state.handlers, type)) {\n    return state.handlers[type](state, node, parent);\n  }\n  if (state.passThrough && state.passThrough.includes(type)) {\n    // To do: next major: deep clone.\n    // @ts-expect-error: types of passed through nodes are expected to be added manually.\n    return 'children' in node ? {\n      ...node,\n      children: all(state, node)\n    } : node;\n  }\n  if (state.unknownHandler) {\n    return state.unknownHandler(state, node, parent);\n  }\n  return defaultUnknownHandler(state, node);\n}\n\n/**\n * Transform the children of an mdast node into hast nodes.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} parent\n *   mdast node to compile\n * @returns {Array<HastElementContent>}\n *   Resulting hast nodes.\n */\n// To do: next major: do not expose, keep bound.\nexport function all(state, parent) {\n  /** @type {Array<HastElementContent>} */\n  const values = [];\n  if ('children' in parent) {\n    const nodes = parent.children;\n    let index = -1;\n    while (++index < nodes.length) {\n      const result = one(state, nodes[index], parent);\n\n      // To do: see if we van clean this? Can we merge texts?\n      if (result) {\n        if (index && nodes[index - 1].type === 'break') {\n          if (!Array.isArray(result) && result.type === 'text') {\n            result.value = result.value.replace(/^\\s+/, '');\n          }\n          if (!Array.isArray(result) && result.type === 'element') {\n            const head = result.children[0];\n            if (head && head.type === 'text') {\n              head.value = head.value.replace(/^\\s+/, '');\n            }\n          }\n        }\n        if (Array.isArray(result)) {\n          values.push(...result);\n        } else {\n          values.push(result);\n        }\n      }\n    }\n  }\n  return values;\n}\n\n/**\n * Transform an unknown node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   Unknown mdast node.\n * @returns {HastText | HastElement}\n *   Resulting hast node.\n */\nfunction defaultUnknownHandler(state, node) {\n  const data = node.data || {};\n  /** @type {HastText | HastElement} */\n  const result = 'value' in node && !(own.call(data, 'hProperties') || own.call(data, 'hChildren')) ? {\n    type: 'text',\n    value: node.value\n  } : {\n    type: 'element',\n    tagName: 'div',\n    properties: {},\n    children: all(state, node)\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}\n\n/**\n * Wrap `nodes` with line endings between each node.\n *\n * @template {HastContent} Type\n *   Node type.\n * @param {Array<Type>} nodes\n *   List of nodes to wrap.\n * @param {boolean | null | undefined} [loose=false]\n *   Whether to add line endings at start and end.\n * @returns {Array<Type | HastText>}\n *   Wrapped nodes.\n */\nexport function wrap(nodes, loose) {\n  /** @type {Array<Type | HastText>} */\n  const result = [];\n  let index = -1;\n  if (loose) {\n    result.push({\n      type: 'text',\n      value: '\\n'\n    });\n  }\n  while (++index < nodes.length) {\n    if (index) result.push({\n      type: 'text',\n      value: '\\n'\n    });\n    result.push(nodes[index]);\n  }\n  if (loose && nodes.length > 0) {\n    result.push({\n      type: 'text',\n      value: '\\n'\n    });\n  }\n  return result;\n}", "map": {"version": 3, "names": ["visit", "position", "pointStart", "pointEnd", "generated", "definitions", "handlers", "own", "hasOwnProperty", "createState", "tree", "options", "settings", "dangerous", "allowDangerousHtml", "footnoteById", "state", "clobberPrefix", "undefined", "footnote<PERSON>abel", "footnoteLabelTagName", "footnoteLabelProperties", "className", "footnoteBackLabel", "<PERSON><PERSON><PERSON><PERSON>", "passThrough", "definition", "footnoteOrder", "footnoteCounts", "patch", "applyData", "one", "oneBound", "all", "allBound", "wrap", "augment", "id", "String", "identifier", "toUpperCase", "call", "left", "right", "data", "hName", "type", "tagName", "properties", "children", "hProperties", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ctx", "start", "end", "node", "props", "Array", "isArray", "parent", "from", "to", "result", "Error", "includes", "defaultUnknownHandler", "values", "nodes", "index", "length", "value", "replace", "head", "push", "loose"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/mdast-util-to-hast/lib/state.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Content} HastContent\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').ElementContent} HastElementContent\n * @typedef {import('hast').Properties} HastProperties\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('hast').Text} HastText\n *\n * @typedef {import('mdast').Content} MdastContent\n * @typedef {import('mdast').Definition} MdastDefinition\n * @typedef {import('mdast').FootnoteDefinition} MdastFootnoteDefinition\n * @typedef {import('mdast').Parent} MdastParent\n * @typedef {import('mdast').Root} MdastRoot\n */\n\n/**\n * @typedef {HastRoot | HastContent} HastNodes\n * @typedef {MdastRoot | MdastContent} MdastNodes\n * @typedef {Extract<MdastNodes, MdastParent>} MdastParents\n *\n * @typedef EmbeddedHastFields\n *   hast fields.\n * @property {string | null | undefined} [hName]\n *   Generate a specific element with this tag name instead.\n * @property {HastProperties | null | undefined} [hProperties]\n *   Generate an element with these properties instead.\n * @property {Array<HastElementContent> | null | undefined} [hChildren]\n *   Generate an element with this content instead.\n *\n * @typedef {Record<string, unknown> & EmbeddedHastFields} MdastData\n *   mdast data with embedded hast fields.\n *\n * @typedef {MdastNodes & {data?: MdastData | null | undefined}} MdastNodeWithData\n *   mdast node with embedded hast data.\n *\n * @typedef PointLike\n *   Point-like value.\n * @property {number | null | undefined} [line]\n *   Line.\n * @property {number | null | undefined} [column]\n *   Column.\n * @property {number | null | undefined} [offset]\n *   Offset.\n *\n * @typedef PositionLike\n *   Position-like value.\n * @property {PointLike | null | undefined} [start]\n *   Point-like value.\n * @property {PointLike | null | undefined} [end]\n *   Point-like value.\n *\n * @callback Handler\n *   Handle a node.\n * @param {State} state\n *   Info passed around.\n * @param {any} node\n *   mdast node to handle.\n * @param {MdastParents | null | undefined} parent\n *   Parent of `node`.\n * @returns {HastElementContent | Array<HastElementContent> | null | undefined}\n *   hast node.\n *\n * @callback HFunctionProps\n *   Signature of `state` for when props are passed.\n * @param {MdastNodes | PositionLike | null | undefined} node\n *   mdast node or unist position.\n * @param {string} tagName\n *   HTML tag name.\n * @param {HastProperties} props\n *   Properties.\n * @param {Array<HastElementContent> | null | undefined} [children]\n *   hast content.\n * @returns {HastElement}\n *   Compiled element.\n *\n * @callback HFunctionNoProps\n *   Signature of `state` for when no props are passed.\n * @param {MdastNodes | PositionLike | null | undefined} node\n *   mdast node or unist position.\n * @param {string} tagName\n *   HTML tag name.\n * @param {Array<HastElementContent> | null | undefined} [children]\n *   hast content.\n * @returns {HastElement}\n *   Compiled element.\n *\n * @typedef HFields\n *   Info on `state`.\n * @property {boolean} dangerous\n *   Whether HTML is allowed.\n * @property {string} clobberPrefix\n *   Prefix to use to prevent DOM clobbering.\n * @property {string} footnoteLabel\n *   Label to use to introduce the footnote section.\n * @property {string} footnoteLabelTagName\n *   HTML used for the footnote label.\n * @property {HastProperties} footnoteLabelProperties\n *   Properties on the HTML tag used for the footnote label.\n * @property {string} footnoteBackLabel\n *   Label to use from backreferences back to their footnote call.\n * @property {(identifier: string) => MdastDefinition | null} definition\n *   Definition cache.\n * @property {Record<string, MdastFootnoteDefinition>} footnoteById\n *   Footnote definitions by their identifier.\n * @property {Array<string>} footnoteOrder\n *   Identifiers of order when footnote calls first appear in tree order.\n * @property {Record<string, number>} footnoteCounts\n *   Counts for how often the same footnote was called.\n * @property {Handlers} handlers\n *   Applied handlers.\n * @property {Handler} unknownHandler\n *   Handler for any none not in `passThrough` or otherwise handled.\n * @property {(from: MdastNodes, node: HastNodes) => void} patch\n *   Copy a node’s positional info.\n * @property {<Type extends HastNodes>(from: MdastNodes, to: Type) => Type | HastElement} applyData\n *   Honor the `data` of `from`, and generate an element instead of `node`.\n * @property {(node: MdastNodes, parent: MdastParents | null | undefined) => HastElementContent | Array<HastElementContent> | null | undefined} one\n *   Transform an mdast node to hast.\n * @property {(node: MdastNodes) => Array<HastElementContent>} all\n *   Transform the children of an mdast parent to hast.\n * @property {<Type extends HastContent>(nodes: Array<Type>, loose?: boolean | null | undefined) => Array<Type | HastText>} wrap\n *   Wrap `nodes` with line endings between each node, adds initial/final line endings when `loose`.\n * @property {(left: MdastNodeWithData | PositionLike | null | undefined, right: HastElementContent) => HastElementContent} augment\n *   Like `state` but lower-level and usable on non-elements.\n *   Deprecated: use `patch` and `applyData`.\n * @property {Array<string>} passThrough\n *   List of node types to pass through untouched (except for their children).\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Whether to persist raw HTML in markdown in the hast tree.\n * @property {string | null | undefined} [clobberPrefix='user-content-']\n *   Prefix to use before the `id` attribute on footnotes to prevent it from\n *   *clobbering*.\n * @property {string | null | undefined} [footnoteBackLabel='Back to content']\n *   Label to use from backreferences back to their footnote call (affects\n *   screen readers).\n * @property {string | null | undefined} [footnoteLabel='Footnotes']\n *   Label to use for the footnotes section (affects screen readers).\n * @property {HastProperties | null | undefined} [footnoteLabelProperties={className: ['sr-only']}]\n *   Properties to use on the footnote label (note that `id: 'footnote-label'`\n *   is always added as footnote calls use it with `aria-describedby` to\n *   provide an accessible label).\n * @property {string | null | undefined} [footnoteLabelTagName='h2']\n *   Tag name to use for the footnote label.\n * @property {Handlers | null | undefined} [handlers]\n *   Extra handlers for nodes.\n * @property {Array<string> | null | undefined} [passThrough]\n *   List of custom mdast node types to pass through (keep) in hast (note that\n *   the node itself is passed, but eventual children are transformed).\n * @property {Handler | null | undefined} [unknownHandler]\n *   Handler for all unknown nodes.\n *\n * @typedef {Record<string, Handler>} Handlers\n *   Handle nodes.\n *\n * @typedef {HFunctionProps & HFunctionNoProps & HFields} State\n *   Info passed around.\n */\n\nimport {visit} from 'unist-util-visit'\nimport {position, pointStart, pointEnd} from 'unist-util-position'\nimport {generated} from 'unist-util-generated'\nimport {definitions} from 'mdast-util-definitions'\nimport {handlers} from './handlers/index.js'\n\nconst own = {}.hasOwnProperty\n\n/**\n * Create `state` from an mdast tree.\n *\n * @param {MdastNodes} tree\n *   mdast node to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {State}\n *   `state` function.\n */\nexport function createState(tree, options) {\n  const settings = options || {}\n  const dangerous = settings.allowDangerousHtml || false\n  /** @type {Record<string, MdastFootnoteDefinition>} */\n  const footnoteById = {}\n\n  // To do: next major: add `options` to state, remove:\n  // `dangerous`, `clobberPrefix`, `footnoteLabel`, `footnoteLabelTagName`,\n  // `footnoteLabelProperties`, `footnoteBackLabel`, `passThrough`,\n  // `unknownHandler`.\n\n  // To do: next major: move to `state.options.allowDangerousHtml`.\n  state.dangerous = dangerous\n  // To do: next major: move to `state.options`.\n  state.clobberPrefix =\n    settings.clobberPrefix === undefined || settings.clobberPrefix === null\n      ? 'user-content-'\n      : settings.clobberPrefix\n  // To do: next major: move to `state.options`.\n  state.footnoteLabel = settings.footnoteLabel || 'Footnotes'\n  // To do: next major: move to `state.options`.\n  state.footnoteLabelTagName = settings.footnoteLabelTagName || 'h2'\n  // To do: next major: move to `state.options`.\n  state.footnoteLabelProperties = settings.footnoteLabelProperties || {\n    className: ['sr-only']\n  }\n  // To do: next major: move to `state.options`.\n  state.footnoteBackLabel = settings.footnoteBackLabel || 'Back to content'\n  // To do: next major: move to `state.options`.\n  state.unknownHandler = settings.unknownHandler\n  // To do: next major: move to `state.options`.\n  state.passThrough = settings.passThrough\n\n  state.handlers = {...handlers, ...settings.handlers}\n\n  // To do: next major: replace utility with `definitionById` object, so we\n  // only walk once (as we need footnotes too).\n  state.definition = definitions(tree)\n  state.footnoteById = footnoteById\n  /** @type {Array<string>} */\n  state.footnoteOrder = []\n  /** @type {Record<string, number>} */\n  state.footnoteCounts = {}\n\n  state.patch = patch\n  state.applyData = applyData\n  state.one = oneBound\n  state.all = allBound\n  state.wrap = wrap\n  // To do: next major: remove `augment`.\n  state.augment = augment\n\n  visit(tree, 'footnoteDefinition', (definition) => {\n    const id = String(definition.identifier).toUpperCase()\n\n    // Mimick CM behavior of link definitions.\n    // See: <https://github.com/syntax-tree/mdast-util-definitions/blob/8290999/index.js#L26>.\n    if (!own.call(footnoteById, id)) {\n      footnoteById[id] = definition\n    }\n  })\n\n  // @ts-expect-error Hush, it’s fine!\n  return state\n\n  /**\n   * Finalise the created `right`, a hast node, from `left`, an mdast node.\n   *\n   * @param {MdastNodeWithData | PositionLike | null | undefined} left\n   * @param {HastElementContent} right\n   * @returns {HastElementContent}\n   */\n  /* c8 ignore start */\n  // To do: next major: remove.\n  function augment(left, right) {\n    // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n    if (left && 'data' in left && left.data) {\n      /** @type {MdastData} */\n      const data = left.data\n\n      if (data.hName) {\n        if (right.type !== 'element') {\n          right = {\n            type: 'element',\n            tagName: '',\n            properties: {},\n            children: []\n          }\n        }\n\n        right.tagName = data.hName\n      }\n\n      if (right.type === 'element' && data.hProperties) {\n        right.properties = {...right.properties, ...data.hProperties}\n      }\n\n      if ('children' in right && right.children && data.hChildren) {\n        right.children = data.hChildren\n      }\n    }\n\n    if (left) {\n      const ctx = 'type' in left ? left : {position: left}\n\n      if (!generated(ctx)) {\n        // @ts-expect-error: fine.\n        right.position = {start: pointStart(ctx), end: pointEnd(ctx)}\n      }\n    }\n\n    return right\n  }\n  /* c8 ignore stop */\n\n  /**\n   * Create an element for `node`.\n   *\n   * @type {HFunctionProps}\n   */\n  /* c8 ignore start */\n  // To do: next major: remove.\n  function state(node, tagName, props, children) {\n    if (Array.isArray(props)) {\n      children = props\n      props = {}\n    }\n\n    // @ts-expect-error augmenting an element yields an element.\n    return augment(node, {\n      type: 'element',\n      tagName,\n      properties: props || {},\n      children: children || []\n    })\n  }\n  /* c8 ignore stop */\n\n  /**\n   * Transform an mdast node into a hast node.\n   *\n   * @param {MdastNodes} node\n   *   mdast node.\n   * @param {MdastParents | null | undefined} [parent]\n   *   Parent of `node`.\n   * @returns {HastElementContent | Array<HastElementContent> | null | undefined}\n   *   Resulting hast node.\n   */\n  function oneBound(node, parent) {\n    // @ts-expect-error: that’s a state :)\n    return one(state, node, parent)\n  }\n\n  /**\n   * Transform the children of an mdast node into hast nodes.\n   *\n   * @param {MdastNodes} parent\n   *   mdast node to compile\n   * @returns {Array<HastElementContent>}\n   *   Resulting hast nodes.\n   */\n  function allBound(parent) {\n    // @ts-expect-error: that’s a state :)\n    return all(state, parent)\n  }\n}\n\n/**\n * Copy a node’s positional info.\n *\n * @param {MdastNodes} from\n *   mdast node to copy from.\n * @param {HastNodes} to\n *   hast node to copy into.\n * @returns {void}\n *   Nothing.\n */\nfunction patch(from, to) {\n  if (from.position) to.position = position(from)\n}\n\n/**\n * Honor the `data` of `from` and maybe generate an element instead of `to`.\n *\n * @template {HastNodes} Type\n *   Node type.\n * @param {MdastNodes} from\n *   mdast node to use data from.\n * @param {Type} to\n *   hast node to change.\n * @returns {Type | HastElement}\n *   Nothing.\n */\nfunction applyData(from, to) {\n  /** @type {Type | HastElement} */\n  let result = to\n\n  // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n  if (from && from.data) {\n    const hName = from.data.hName\n    const hChildren = from.data.hChildren\n    const hProperties = from.data.hProperties\n\n    if (typeof hName === 'string') {\n      // Transforming the node resulted in an element with a different name\n      // than wanted:\n      if (result.type === 'element') {\n        result.tagName = hName\n      }\n      // Transforming the node resulted in a non-element, which happens for\n      // raw, text, and root nodes (unless custom handlers are passed).\n      // The intent is likely to keep the content around (otherwise: pass\n      // `hChildren`).\n      else {\n        result = {\n          type: 'element',\n          tagName: hName,\n          properties: {},\n          children: []\n        }\n\n        // To do: next major: take the children from the `root`, or inject the\n        // raw/text/comment or so into the element?\n        // if ('children' in node) {\n        //   // @ts-expect-error: assume `children` are allowed in elements.\n        //   result.children = node.children\n        // } else {\n        //   // @ts-expect-error: assume `node` is allowed in elements.\n        //   result.children.push(node)\n        // }\n      }\n    }\n\n    if (result.type === 'element' && hProperties) {\n      result.properties = {...result.properties, ...hProperties}\n    }\n\n    if (\n      'children' in result &&\n      result.children &&\n      hChildren !== null &&\n      hChildren !== undefined\n    ) {\n      // @ts-expect-error: assume valid children are defined.\n      result.children = hChildren\n    }\n  }\n\n  return result\n}\n\n/**\n * Transform an mdast node into a hast node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   mdast node.\n * @param {MdastParents | null | undefined} [parent]\n *   Parent of `node`.\n * @returns {HastElementContent | Array<HastElementContent> | null | undefined}\n *   Resulting hast node.\n */\n// To do: next major: do not expose, keep bound.\nexport function one(state, node, parent) {\n  const type = node && node.type\n\n  // Fail on non-nodes.\n  if (!type) {\n    throw new Error('Expected node, got `' + node + '`')\n  }\n\n  if (own.call(state.handlers, type)) {\n    return state.handlers[type](state, node, parent)\n  }\n\n  if (state.passThrough && state.passThrough.includes(type)) {\n    // To do: next major: deep clone.\n    // @ts-expect-error: types of passed through nodes are expected to be added manually.\n    return 'children' in node ? {...node, children: all(state, node)} : node\n  }\n\n  if (state.unknownHandler) {\n    return state.unknownHandler(state, node, parent)\n  }\n\n  return defaultUnknownHandler(state, node)\n}\n\n/**\n * Transform the children of an mdast node into hast nodes.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} parent\n *   mdast node to compile\n * @returns {Array<HastElementContent>}\n *   Resulting hast nodes.\n */\n// To do: next major: do not expose, keep bound.\nexport function all(state, parent) {\n  /** @type {Array<HastElementContent>} */\n  const values = []\n\n  if ('children' in parent) {\n    const nodes = parent.children\n    let index = -1\n    while (++index < nodes.length) {\n      const result = one(state, nodes[index], parent)\n\n      // To do: see if we van clean this? Can we merge texts?\n      if (result) {\n        if (index && nodes[index - 1].type === 'break') {\n          if (!Array.isArray(result) && result.type === 'text') {\n            result.value = result.value.replace(/^\\s+/, '')\n          }\n\n          if (!Array.isArray(result) && result.type === 'element') {\n            const head = result.children[0]\n\n            if (head && head.type === 'text') {\n              head.value = head.value.replace(/^\\s+/, '')\n            }\n          }\n        }\n\n        if (Array.isArray(result)) {\n          values.push(...result)\n        } else {\n          values.push(result)\n        }\n      }\n    }\n  }\n\n  return values\n}\n\n/**\n * Transform an unknown node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   Unknown mdast node.\n * @returns {HastText | HastElement}\n *   Resulting hast node.\n */\nfunction defaultUnknownHandler(state, node) {\n  const data = node.data || {}\n  /** @type {HastText | HastElement} */\n  const result =\n    'value' in node &&\n    !(own.call(data, 'hProperties') || own.call(data, 'hChildren'))\n      ? {type: 'text', value: node.value}\n      : {\n          type: 'element',\n          tagName: 'div',\n          properties: {},\n          children: all(state, node)\n        }\n\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n\n/**\n * Wrap `nodes` with line endings between each node.\n *\n * @template {HastContent} Type\n *   Node type.\n * @param {Array<Type>} nodes\n *   List of nodes to wrap.\n * @param {boolean | null | undefined} [loose=false]\n *   Whether to add line endings at start and end.\n * @returns {Array<Type | HastText>}\n *   Wrapped nodes.\n */\nexport function wrap(nodes, loose) {\n  /** @type {Array<Type | HastText>} */\n  const result = []\n  let index = -1\n\n  if (loose) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  while (++index < nodes.length) {\n    if (index) result.push({type: 'text', value: '\\n'})\n    result.push(nodes[index])\n  }\n\n  if (loose && nodes.length > 0) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  return result\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,KAAK,QAAO,kBAAkB;AACtC,SAAQC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,QAAO,qBAAqB;AAClE,SAAQC,SAAS,QAAO,sBAAsB;AAC9C,SAAQC,WAAW,QAAO,wBAAwB;AAClD,SAAQC,QAAQ,QAAO,qBAAqB;AAE5C,MAAMC,GAAG,GAAG,CAAC,CAAC,CAACC,cAAc;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzC,MAAMC,QAAQ,GAAGD,OAAO,IAAI,CAAC,CAAC;EAC9B,MAAME,SAAS,GAAGD,QAAQ,CAACE,kBAAkB,IAAI,KAAK;EACtD;EACA,MAAMC,YAAY,GAAG,CAAC,CAAC;;EAEvB;EACA;EACA;EACA;;EAEA;EACAC,KAAK,CAACH,SAAS,GAAGA,SAAS;EAC3B;EACAG,KAAK,CAACC,aAAa,GACjBL,QAAQ,CAACK,aAAa,KAAKC,SAAS,IAAIN,QAAQ,CAACK,aAAa,KAAK,IAAI,GACnE,eAAe,GACfL,QAAQ,CAACK,aAAa;EAC5B;EACAD,KAAK,CAACG,aAAa,GAAGP,QAAQ,CAACO,aAAa,IAAI,WAAW;EAC3D;EACAH,KAAK,CAACI,oBAAoB,GAAGR,QAAQ,CAACQ,oBAAoB,IAAI,IAAI;EAClE;EACAJ,KAAK,CAACK,uBAAuB,GAAGT,QAAQ,CAACS,uBAAuB,IAAI;IAClEC,SAAS,EAAE,CAAC,SAAS;EACvB,CAAC;EACD;EACAN,KAAK,CAACO,iBAAiB,GAAGX,QAAQ,CAACW,iBAAiB,IAAI,iBAAiB;EACzE;EACAP,KAAK,CAACQ,cAAc,GAAGZ,QAAQ,CAACY,cAAc;EAC9C;EACAR,KAAK,CAACS,WAAW,GAAGb,QAAQ,CAACa,WAAW;EAExCT,KAAK,CAACV,QAAQ,GAAG;IAAC,GAAGA,QAAQ;IAAE,GAAGM,QAAQ,CAACN;EAAQ,CAAC;;EAEpD;EACA;EACAU,KAAK,CAACU,UAAU,GAAGrB,WAAW,CAACK,IAAI,CAAC;EACpCM,KAAK,CAACD,YAAY,GAAGA,YAAY;EACjC;EACAC,KAAK,CAACW,aAAa,GAAG,EAAE;EACxB;EACAX,KAAK,CAACY,cAAc,GAAG,CAAC,CAAC;EAEzBZ,KAAK,CAACa,KAAK,GAAGA,KAAK;EACnBb,KAAK,CAACc,SAAS,GAAGA,SAAS;EAC3Bd,KAAK,CAACe,GAAG,GAAGC,QAAQ;EACpBhB,KAAK,CAACiB,GAAG,GAAGC,QAAQ;EACpBlB,KAAK,CAACmB,IAAI,GAAGA,IAAI;EACjB;EACAnB,KAAK,CAACoB,OAAO,GAAGA,OAAO;EAEvBpC,KAAK,CAACU,IAAI,EAAE,oBAAoB,EAAGgB,UAAU,IAAK;IAChD,MAAMW,EAAE,GAAGC,MAAM,CAACZ,UAAU,CAACa,UAAU,CAAC,CAACC,WAAW,CAAC,CAAC;;IAEtD;IACA;IACA,IAAI,CAACjC,GAAG,CAACkC,IAAI,CAAC1B,YAAY,EAAEsB,EAAE,CAAC,EAAE;MAC/BtB,YAAY,CAACsB,EAAE,CAAC,GAAGX,UAAU;IAC/B;EACF,CAAC,CAAC;;EAEF;EACA,OAAOV,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;EACE;EACA;EACA,SAASoB,OAAOA,CAACM,IAAI,EAAEC,KAAK,EAAE;IAC5B;IACA,IAAID,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAIA,IAAI,CAACE,IAAI,EAAE;MACvC;MACA,MAAMA,IAAI,GAAGF,IAAI,CAACE,IAAI;MAEtB,IAAIA,IAAI,CAACC,KAAK,EAAE;QACd,IAAIF,KAAK,CAACG,IAAI,KAAK,SAAS,EAAE;UAC5BH,KAAK,GAAG;YACNG,IAAI,EAAE,SAAS;YACfC,OAAO,EAAE,EAAE;YACXC,UAAU,EAAE,CAAC,CAAC;YACdC,QAAQ,EAAE;UACZ,CAAC;QACH;QAEAN,KAAK,CAACI,OAAO,GAAGH,IAAI,CAACC,KAAK;MAC5B;MAEA,IAAIF,KAAK,CAACG,IAAI,KAAK,SAAS,IAAIF,IAAI,CAACM,WAAW,EAAE;QAChDP,KAAK,CAACK,UAAU,GAAG;UAAC,GAAGL,KAAK,CAACK,UAAU;UAAE,GAAGJ,IAAI,CAACM;QAAW,CAAC;MAC/D;MAEA,IAAI,UAAU,IAAIP,KAAK,IAAIA,KAAK,CAACM,QAAQ,IAAIL,IAAI,CAACO,SAAS,EAAE;QAC3DR,KAAK,CAACM,QAAQ,GAAGL,IAAI,CAACO,SAAS;MACjC;IACF;IAEA,IAAIT,IAAI,EAAE;MACR,MAAMU,GAAG,GAAG,MAAM,IAAIV,IAAI,GAAGA,IAAI,GAAG;QAACzC,QAAQ,EAAEyC;MAAI,CAAC;MAEpD,IAAI,CAACtC,SAAS,CAACgD,GAAG,CAAC,EAAE;QACnB;QACAT,KAAK,CAAC1C,QAAQ,GAAG;UAACoD,KAAK,EAAEnD,UAAU,CAACkD,GAAG,CAAC;UAAEE,GAAG,EAAEnD,QAAQ,CAACiD,GAAG;QAAC,CAAC;MAC/D;IACF;IAEA,OAAOT,KAAK;EACd;EACA;;EAEA;AACF;AACA;AACA;AACA;EACE;EACA;EACA,SAAS3B,KAAKA,CAACuC,IAAI,EAAER,OAAO,EAAES,KAAK,EAAEP,QAAQ,EAAE;IAC7C,IAAIQ,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;MACxBP,QAAQ,GAAGO,KAAK;MAChBA,KAAK,GAAG,CAAC,CAAC;IACZ;;IAEA;IACA,OAAOpB,OAAO,CAACmB,IAAI,EAAE;MACnBT,IAAI,EAAE,SAAS;MACfC,OAAO;MACPC,UAAU,EAAEQ,KAAK,IAAI,CAAC,CAAC;MACvBP,QAAQ,EAAEA,QAAQ,IAAI;IACxB,CAAC,CAAC;EACJ;EACA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASjB,QAAQA,CAACuB,IAAI,EAAEI,MAAM,EAAE;IAC9B;IACA,OAAO5B,GAAG,CAACf,KAAK,EAAEuC,IAAI,EAAEI,MAAM,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASzB,QAAQA,CAACyB,MAAM,EAAE;IACxB;IACA,OAAO1B,GAAG,CAACjB,KAAK,EAAE2C,MAAM,CAAC;EAC3B;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS9B,KAAKA,CAAC+B,IAAI,EAAEC,EAAE,EAAE;EACvB,IAAID,IAAI,CAAC3D,QAAQ,EAAE4D,EAAE,CAAC5D,QAAQ,GAAGA,QAAQ,CAAC2D,IAAI,CAAC;AACjD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS9B,SAASA,CAAC8B,IAAI,EAAEC,EAAE,EAAE;EAC3B;EACA,IAAIC,MAAM,GAAGD,EAAE;;EAEf;EACA,IAAID,IAAI,IAAIA,IAAI,CAAChB,IAAI,EAAE;IACrB,MAAMC,KAAK,GAAGe,IAAI,CAAChB,IAAI,CAACC,KAAK;IAC7B,MAAMM,SAAS,GAAGS,IAAI,CAAChB,IAAI,CAACO,SAAS;IACrC,MAAMD,WAAW,GAAGU,IAAI,CAAChB,IAAI,CAACM,WAAW;IAEzC,IAAI,OAAOL,KAAK,KAAK,QAAQ,EAAE;MAC7B;MACA;MACA,IAAIiB,MAAM,CAAChB,IAAI,KAAK,SAAS,EAAE;QAC7BgB,MAAM,CAACf,OAAO,GAAGF,KAAK;MACxB;MACA;MACA;MACA;MACA;MAAA,KACK;QACHiB,MAAM,GAAG;UACPhB,IAAI,EAAE,SAAS;UACfC,OAAO,EAAEF,KAAK;UACdG,UAAU,EAAE,CAAC,CAAC;UACdC,QAAQ,EAAE;QACZ,CAAC;;QAED;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACF;IACF;IAEA,IAAIa,MAAM,CAAChB,IAAI,KAAK,SAAS,IAAII,WAAW,EAAE;MAC5CY,MAAM,CAACd,UAAU,GAAG;QAAC,GAAGc,MAAM,CAACd,UAAU;QAAE,GAAGE;MAAW,CAAC;IAC5D;IAEA,IACE,UAAU,IAAIY,MAAM,IACpBA,MAAM,CAACb,QAAQ,IACfE,SAAS,KAAK,IAAI,IAClBA,SAAS,KAAKjC,SAAS,EACvB;MACA;MACA4C,MAAM,CAACb,QAAQ,GAAGE,SAAS;IAC7B;EACF;EAEA,OAAOW,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS/B,GAAGA,CAACf,KAAK,EAAEuC,IAAI,EAAEI,MAAM,EAAE;EACvC,MAAMb,IAAI,GAAGS,IAAI,IAAIA,IAAI,CAACT,IAAI;;EAE9B;EACA,IAAI,CAACA,IAAI,EAAE;IACT,MAAM,IAAIiB,KAAK,CAAC,sBAAsB,GAAGR,IAAI,GAAG,GAAG,CAAC;EACtD;EAEA,IAAIhD,GAAG,CAACkC,IAAI,CAACzB,KAAK,CAACV,QAAQ,EAAEwC,IAAI,CAAC,EAAE;IAClC,OAAO9B,KAAK,CAACV,QAAQ,CAACwC,IAAI,CAAC,CAAC9B,KAAK,EAAEuC,IAAI,EAAEI,MAAM,CAAC;EAClD;EAEA,IAAI3C,KAAK,CAACS,WAAW,IAAIT,KAAK,CAACS,WAAW,CAACuC,QAAQ,CAAClB,IAAI,CAAC,EAAE;IACzD;IACA;IACA,OAAO,UAAU,IAAIS,IAAI,GAAG;MAAC,GAAGA,IAAI;MAAEN,QAAQ,EAAEhB,GAAG,CAACjB,KAAK,EAAEuC,IAAI;IAAC,CAAC,GAAGA,IAAI;EAC1E;EAEA,IAAIvC,KAAK,CAACQ,cAAc,EAAE;IACxB,OAAOR,KAAK,CAACQ,cAAc,CAACR,KAAK,EAAEuC,IAAI,EAAEI,MAAM,CAAC;EAClD;EAEA,OAAOM,qBAAqB,CAACjD,KAAK,EAAEuC,IAAI,CAAC;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAStB,GAAGA,CAACjB,KAAK,EAAE2C,MAAM,EAAE;EACjC;EACA,MAAMO,MAAM,GAAG,EAAE;EAEjB,IAAI,UAAU,IAAIP,MAAM,EAAE;IACxB,MAAMQ,KAAK,GAAGR,MAAM,CAACV,QAAQ;IAC7B,IAAImB,KAAK,GAAG,CAAC,CAAC;IACd,OAAO,EAAEA,KAAK,GAAGD,KAAK,CAACE,MAAM,EAAE;MAC7B,MAAMP,MAAM,GAAG/B,GAAG,CAACf,KAAK,EAAEmD,KAAK,CAACC,KAAK,CAAC,EAAET,MAAM,CAAC;;MAE/C;MACA,IAAIG,MAAM,EAAE;QACV,IAAIM,KAAK,IAAID,KAAK,CAACC,KAAK,GAAG,CAAC,CAAC,CAACtB,IAAI,KAAK,OAAO,EAAE;UAC9C,IAAI,CAACW,KAAK,CAACC,OAAO,CAACI,MAAM,CAAC,IAAIA,MAAM,CAAChB,IAAI,KAAK,MAAM,EAAE;YACpDgB,MAAM,CAACQ,KAAK,GAAGR,MAAM,CAACQ,KAAK,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;UACjD;UAEA,IAAI,CAACd,KAAK,CAACC,OAAO,CAACI,MAAM,CAAC,IAAIA,MAAM,CAAChB,IAAI,KAAK,SAAS,EAAE;YACvD,MAAM0B,IAAI,GAAGV,MAAM,CAACb,QAAQ,CAAC,CAAC,CAAC;YAE/B,IAAIuB,IAAI,IAAIA,IAAI,CAAC1B,IAAI,KAAK,MAAM,EAAE;cAChC0B,IAAI,CAACF,KAAK,GAAGE,IAAI,CAACF,KAAK,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;YAC7C;UACF;QACF;QAEA,IAAId,KAAK,CAACC,OAAO,CAACI,MAAM,CAAC,EAAE;UACzBI,MAAM,CAACO,IAAI,CAAC,GAAGX,MAAM,CAAC;QACxB,CAAC,MAAM;UACLI,MAAM,CAACO,IAAI,CAACX,MAAM,CAAC;QACrB;MACF;IACF;EACF;EAEA,OAAOI,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,qBAAqBA,CAACjD,KAAK,EAAEuC,IAAI,EAAE;EAC1C,MAAMX,IAAI,GAAGW,IAAI,CAACX,IAAI,IAAI,CAAC,CAAC;EAC5B;EACA,MAAMkB,MAAM,GACV,OAAO,IAAIP,IAAI,IACf,EAAEhD,GAAG,CAACkC,IAAI,CAACG,IAAI,EAAE,aAAa,CAAC,IAAIrC,GAAG,CAACkC,IAAI,CAACG,IAAI,EAAE,WAAW,CAAC,CAAC,GAC3D;IAACE,IAAI,EAAE,MAAM;IAAEwB,KAAK,EAAEf,IAAI,CAACe;EAAK,CAAC,GACjC;IACExB,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,CAAC,CAAC;IACdC,QAAQ,EAAEhB,GAAG,CAACjB,KAAK,EAAEuC,IAAI;EAC3B,CAAC;EAEPvC,KAAK,CAACa,KAAK,CAAC0B,IAAI,EAAEO,MAAM,CAAC;EACzB,OAAO9C,KAAK,CAACc,SAAS,CAACyB,IAAI,EAAEO,MAAM,CAAC;AACtC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS3B,IAAIA,CAACgC,KAAK,EAAEO,KAAK,EAAE;EACjC;EACA,MAAMZ,MAAM,GAAG,EAAE;EACjB,IAAIM,KAAK,GAAG,CAAC,CAAC;EAEd,IAAIM,KAAK,EAAE;IACTZ,MAAM,CAACW,IAAI,CAAC;MAAC3B,IAAI,EAAE,MAAM;MAAEwB,KAAK,EAAE;IAAI,CAAC,CAAC;EAC1C;EAEA,OAAO,EAAEF,KAAK,GAAGD,KAAK,CAACE,MAAM,EAAE;IAC7B,IAAID,KAAK,EAAEN,MAAM,CAACW,IAAI,CAAC;MAAC3B,IAAI,EAAE,MAAM;MAAEwB,KAAK,EAAE;IAAI,CAAC,CAAC;IACnDR,MAAM,CAACW,IAAI,CAACN,KAAK,CAACC,KAAK,CAAC,CAAC;EAC3B;EAEA,IAAIM,KAAK,IAAIP,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;IAC7BP,MAAM,CAACW,IAAI,CAAC;MAAC3B,IAAI,EAAE,MAAM;MAAEwB,KAAK,EAAE;IAAI,CAAC,CAAC;EAC1C;EAEA,OAAOR,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}