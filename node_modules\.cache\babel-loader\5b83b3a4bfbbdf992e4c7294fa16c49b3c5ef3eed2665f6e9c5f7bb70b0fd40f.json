{"ast": null, "code": "import { create } from './util/create.js';\nexport const xml = create({\n  space: 'xml',\n  transform(_, prop) {\n    return 'xml:' + prop.slice(3).toLowerCase();\n  },\n  properties: {\n    xmlLang: null,\n    xmlBase: null,\n    xmlSpace: null\n  }\n});", "map": {"version": 3, "names": ["create", "xml", "space", "transform", "_", "prop", "slice", "toLowerCase", "properties", "xmlLang", "xmlBase", "xmlSpace"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/property-information/lib/xml.js"], "sourcesContent": ["import {create} from './util/create.js'\n\nexport const xml = create({\n  space: 'xml',\n  transform(_, prop) {\n    return 'xml:' + prop.slice(3).toLowerCase()\n  },\n  properties: {xmlLang: null, xmlBase: null, xmlSpace: null}\n})\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,kBAAkB;AAEvC,OAAO,MAAMC,GAAG,GAAGD,MAAM,CAAC;EACxBE,KAAK,EAAE,KAAK;EACZC,SAASA,CAACC,CAAC,EAAEC,IAAI,EAAE;IACjB,OAAO,MAAM,GAAGA,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAC7C,CAAC;EACDC,UAAU,EAAE;IAACC,OAAO,EAAE,IAAI;IAAEC,OAAO,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAI;AAC3D,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}