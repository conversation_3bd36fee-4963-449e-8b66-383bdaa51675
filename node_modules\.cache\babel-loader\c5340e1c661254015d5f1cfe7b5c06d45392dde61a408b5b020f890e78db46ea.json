{"ast": null, "code": "/**\n * TODO: When we move from string as a source of truth to data models\n * everything in this folder should probably be referred to as models vs types\n */\n// If this number is a decimal, make it just five decimal places\n// to avoid exponents\nconst sanitize = v => Math.round(v * 100000) / 100000;\nconst floatRegex = /(-)?([\\d]*\\.?[\\d])+/g;\nconst colorRegex = /(#[0-9a-f]{3,8}|(rgb|hsl)a?\\((-?[\\d\\.]+%?[,\\s]+){2}(-?[\\d\\.]+%?)\\s*[\\,\\/]?\\s*[\\d\\.]*%?\\))/gi;\nconst singleColorRegex = /^(#[0-9a-f]{3,8}|(rgb|hsl)a?\\((-?[\\d\\.]+%?[,\\s]+){2}(-?[\\d\\.]+%?)\\s*[\\,\\/]?\\s*[\\d\\.]*%?\\))$/i;\nfunction isString(v) {\n  return typeof v === \"string\";\n}\nexport { colorRegex, floatRegex, isString, sanitize, singleColorRegex };", "map": {"version": 3, "names": ["sanitize", "v", "Math", "round", "floatRegex", "colorRegex", "singleColorRegex", "isString"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/framer-motion/dist/es/value/types/utils.mjs"], "sourcesContent": ["/**\n * TODO: When we move from string as a source of truth to data models\n * everything in this folder should probably be referred to as models vs types\n */\n// If this number is a decimal, make it just five decimal places\n// to avoid exponents\nconst sanitize = (v) => Math.round(v * 100000) / 100000;\nconst floatRegex = /(-)?([\\d]*\\.?[\\d])+/g;\nconst colorRegex = /(#[0-9a-f]{3,8}|(rgb|hsl)a?\\((-?[\\d\\.]+%?[,\\s]+){2}(-?[\\d\\.]+%?)\\s*[\\,\\/]?\\s*[\\d\\.]*%?\\))/gi;\nconst singleColorRegex = /^(#[0-9a-f]{3,8}|(rgb|hsl)a?\\((-?[\\d\\.]+%?[,\\s]+){2}(-?[\\d\\.]+%?)\\s*[\\,\\/]?\\s*[\\d\\.]*%?\\))$/i;\nfunction isString(v) {\n    return typeof v === \"string\";\n}\n\nexport { colorRegex, floatRegex, isString, sanitize, singleColorRegex };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,QAAQ,GAAIC,CAAC,IAAKC,IAAI,CAACC,KAAK,CAACF,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM;AACvD,MAAMG,UAAU,GAAG,sBAAsB;AACzC,MAAMC,UAAU,GAAG,6FAA6F;AAChH,MAAMC,gBAAgB,GAAG,8FAA8F;AACvH,SAASC,QAAQA,CAACN,CAAC,EAAE;EACjB,OAAO,OAAOA,CAAC,KAAK,QAAQ;AAChC;AAEA,SAASI,UAAU,EAAED,UAAU,EAAEG,QAAQ,EAAEP,QAAQ,EAAEM,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}