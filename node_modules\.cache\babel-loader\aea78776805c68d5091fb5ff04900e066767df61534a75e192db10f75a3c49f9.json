{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').Event} Event\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport { factoryDestination } from 'micromark-factory-destination';\nimport { factoryLabel } from 'micromark-factory-label';\nimport { factoryTitle } from 'micromark-factory-title';\nimport { factoryWhitespace } from 'micromark-factory-whitespace';\nimport { markdownLineEndingOrSpace } from 'micromark-util-character';\nimport { push, splice } from 'micromark-util-chunked';\nimport { normalizeIdentifier } from 'micromark-util-normalize-identifier';\nimport { resolveAll } from 'micromark-util-resolve-all';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { constants } from 'micromark-util-symbol/constants.js';\nimport { types } from 'micromark-util-symbol/types.js';\nimport { ok as assert } from 'uvu/assert';\n\n/** @type {Construct} */\nexport const labelEnd = {\n  name: 'labelEnd',\n  tokenize: tokenizeLabelEnd,\n  resolveTo: resolveToLabelEnd,\n  resolveAll: resolveAllLabelEnd\n};\n\n/** @type {Construct} */\nconst resourceConstruct = {\n  tokenize: tokenizeResource\n};\n/** @type {Construct} */\nconst referenceFullConstruct = {\n  tokenize: tokenizeReferenceFull\n};\n/** @type {Construct} */\nconst referenceCollapsedConstruct = {\n  tokenize: tokenizeReferenceCollapsed\n};\n\n/** @type {Resolver} */\nfunction resolveAllLabelEnd(events) {\n  let index = -1;\n  while (++index < events.length) {\n    const token = events[index][1];\n    if (token.type === types.labelImage || token.type === types.labelLink || token.type === types.labelEnd) {\n      // Remove the marker.\n      events.splice(index + 1, token.type === types.labelImage ? 4 : 2);\n      token.type = types.data;\n      index++;\n    }\n  }\n  return events;\n}\n\n/** @type {Resolver} */\nfunction resolveToLabelEnd(events, context) {\n  let index = events.length;\n  let offset = 0;\n  /** @type {Token} */\n  let token;\n  /** @type {number | undefined} */\n  let open;\n  /** @type {number | undefined} */\n  let close;\n  /** @type {Array<Event>} */\n  let media;\n\n  // Find an opening.\n  while (index--) {\n    token = events[index][1];\n    if (open) {\n      // If we see another link, or inactive link label, we’ve been here before.\n      if (token.type === types.link || token.type === types.labelLink && token._inactive) {\n        break;\n      }\n\n      // Mark other link openings as inactive, as we can’t have links in\n      // links.\n      if (events[index][0] === 'enter' && token.type === types.labelLink) {\n        token._inactive = true;\n      }\n    } else if (close) {\n      if (events[index][0] === 'enter' && (token.type === types.labelImage || token.type === types.labelLink) && !token._balanced) {\n        open = index;\n        if (token.type !== types.labelLink) {\n          offset = 2;\n          break;\n        }\n      }\n    } else if (token.type === types.labelEnd) {\n      close = index;\n    }\n  }\n  assert(open !== undefined, '`open` is supposed to be found');\n  assert(close !== undefined, '`close` is supposed to be found');\n  const group = {\n    type: events[open][1].type === types.labelLink ? types.link : types.image,\n    start: Object.assign({}, events[open][1].start),\n    end: Object.assign({}, events[events.length - 1][1].end)\n  };\n  const label = {\n    type: types.label,\n    start: Object.assign({}, events[open][1].start),\n    end: Object.assign({}, events[close][1].end)\n  };\n  const text = {\n    type: types.labelText,\n    start: Object.assign({}, events[open + offset + 2][1].end),\n    end: Object.assign({}, events[close - 2][1].start)\n  };\n  media = [['enter', group, context], ['enter', label, context]];\n\n  // Opening marker.\n  media = push(media, events.slice(open + 1, open + offset + 3));\n\n  // Text open.\n  media = push(media, [['enter', text, context]]);\n\n  // Always populated by defaults.\n  assert(context.parser.constructs.insideSpan.null, 'expected `insideSpan.null` to be populated');\n  // Between.\n  media = push(media, resolveAll(context.parser.constructs.insideSpan.null, events.slice(open + offset + 4, close - 3), context));\n\n  // Text close, marker close, label close.\n  media = push(media, [['exit', text, context], events[close - 2], events[close - 1], ['exit', label, context]]);\n\n  // Reference, resource, or so.\n  media = push(media, events.slice(close + 1));\n\n  // Media close.\n  media = push(media, [['exit', group, context]]);\n  splice(events, open, events.length, media);\n  return events;\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeLabelEnd(effects, ok, nok) {\n  const self = this;\n  let index = self.events.length;\n  /** @type {Token} */\n  let labelStart;\n  /** @type {boolean} */\n  let defined;\n\n  // Find an opening.\n  while (index--) {\n    if ((self.events[index][1].type === types.labelImage || self.events[index][1].type === types.labelLink) && !self.events[index][1]._balanced) {\n      labelStart = self.events[index][1];\n      break;\n    }\n  }\n  return start;\n\n  /**\n   * Start of label end.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.rightSquareBracket, 'expected `]`');\n\n    // If there is not an okay opening.\n    if (!labelStart) {\n      return nok(code);\n    }\n\n    // If the corresponding label (link) start is marked as inactive,\n    // it means we’d be wrapping a link, like this:\n    //\n    // ```markdown\n    // > | a [b [c](d) e](f) g.\n    //                  ^\n    // ```\n    //\n    // We can’t have that, so it’s just balanced brackets.\n    if (labelStart._inactive) {\n      return labelEndNok(code);\n    }\n    defined = self.parser.defined.includes(normalizeIdentifier(self.sliceSerialize({\n      start: labelStart.end,\n      end: self.now()\n    })));\n    effects.enter(types.labelEnd);\n    effects.enter(types.labelMarker);\n    effects.consume(code);\n    effects.exit(types.labelMarker);\n    effects.exit(types.labelEnd);\n    return after;\n  }\n\n  /**\n   * After `]`.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // Note: `markdown-rs` also parses GFM footnotes here, which for us is in\n    // an extension.\n\n    // Resource (`[asd](fgh)`)?\n    if (code === codes.leftParenthesis) {\n      return effects.attempt(resourceConstruct, labelEndOk, defined ? labelEndOk : labelEndNok)(code);\n    }\n\n    // Full (`[asd][fgh]`) or collapsed (`[asd][]`) reference?\n    if (code === codes.leftSquareBracket) {\n      return effects.attempt(referenceFullConstruct, labelEndOk, defined ? referenceNotFull : labelEndNok)(code);\n    }\n\n    // Shortcut (`[asd]`) reference?\n    return defined ? labelEndOk(code) : labelEndNok(code);\n  }\n\n  /**\n   * After `]`, at `[`, but not at a full reference.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] b\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceNotFull(code) {\n    return effects.attempt(referenceCollapsedConstruct, labelEndOk, labelEndNok)(code);\n  }\n\n  /**\n   * Done, we found something.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *           ^\n   * > | [a][b] c\n   *           ^\n   * > | [a][] b\n   *          ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndOk(code) {\n    // Note: `markdown-rs` does a bunch of stuff here.\n    return ok(code);\n  }\n\n  /**\n   * Done, it’s nothing.\n   *\n   * There was an okay opening, but we didn’t match anything.\n   *\n   * ```markdown\n   * > | [a](b c\n   *        ^\n   * > | [a][b c\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndNok(code) {\n    labelStart._balanced = true;\n    return nok(code);\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeResource(effects, ok, nok) {\n  return resourceStart;\n\n  /**\n   * At a resource.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceStart(code) {\n    assert(code === codes.leftParenthesis, 'expected left paren');\n    effects.enter(types.resource);\n    effects.enter(types.resourceMarker);\n    effects.consume(code);\n    effects.exit(types.resourceMarker);\n    return resourceBefore;\n  }\n\n  /**\n   * In resource, after `(`, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBefore(code) {\n    return markdownLineEndingOrSpace(code) ? factoryWhitespace(effects, resourceOpen)(code) : resourceOpen(code);\n  }\n\n  /**\n   * In resource, after optional whitespace, at `)` or a destination.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceOpen(code) {\n    if (code === codes.rightParenthesis) {\n      return resourceEnd(code);\n    }\n    return factoryDestination(effects, resourceDestinationAfter, resourceDestinationMissing, types.resourceDestination, types.resourceDestinationLiteral, types.resourceDestinationLiteralMarker, types.resourceDestinationRaw, types.resourceDestinationString, constants.linkResourceDestinationBalanceMax)(code);\n  }\n\n  /**\n   * In resource, after destination, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationAfter(code) {\n    return markdownLineEndingOrSpace(code) ? factoryWhitespace(effects, resourceBetween)(code) : resourceEnd(code);\n  }\n\n  /**\n   * At invalid destination.\n   *\n   * ```markdown\n   * > | [a](<<) b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationMissing(code) {\n    return nok(code);\n  }\n\n  /**\n   * In resource, after destination and whitespace, at `(` or title.\n   *\n   * ```markdown\n   * > | [a](b ) c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBetween(code) {\n    if (code === codes.quotationMark || code === codes.apostrophe || code === codes.leftParenthesis) {\n      return factoryTitle(effects, resourceTitleAfter, nok, types.resourceTitle, types.resourceTitleMarker, types.resourceTitleString)(code);\n    }\n    return resourceEnd(code);\n  }\n\n  /**\n   * In resource, after title, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b \"c\") d\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceTitleAfter(code) {\n    return markdownLineEndingOrSpace(code) ? factoryWhitespace(effects, resourceEnd)(code) : resourceEnd(code);\n  }\n\n  /**\n   * In resource, at `)`.\n   *\n   * ```markdown\n   * > | [a](b) d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceEnd(code) {\n    if (code === codes.rightParenthesis) {\n      effects.enter(types.resourceMarker);\n      effects.consume(code);\n      effects.exit(types.resourceMarker);\n      effects.exit(types.resource);\n      return ok;\n    }\n    return nok(code);\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceFull(effects, ok, nok) {\n  const self = this;\n  return referenceFull;\n\n  /**\n   * In a reference (full), at the `[`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFull(code) {\n    assert(code === codes.leftSquareBracket, 'expected left bracket');\n    return factoryLabel.call(self, effects, referenceFullAfter, referenceFullMissing, types.reference, types.referenceMarker, types.referenceString)(code);\n  }\n\n  /**\n   * In a reference (full), after `]`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullAfter(code) {\n    return self.parser.defined.includes(normalizeIdentifier(self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1))) ? ok(code) : nok(code);\n  }\n\n  /**\n   * In reference (full) that was missing.\n   *\n   * ```markdown\n   * > | [a][b d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullMissing(code) {\n    return nok(code);\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceCollapsed(effects, ok, nok) {\n  return referenceCollapsedStart;\n\n  /**\n   * In reference (collapsed), at `[`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceCollapsedStart(code) {\n    // We only attempt a collapsed label if there’s a `[`.\n    assert(code === codes.leftSquareBracket, 'expected left bracket');\n    effects.enter(types.reference);\n    effects.enter(types.referenceMarker);\n    effects.consume(code);\n    effects.exit(types.referenceMarker);\n    return referenceCollapsedOpen;\n  }\n\n  /**\n   * In reference (collapsed), at `]`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *         ^\n   * ```\n   *\n   *  @type {State}\n   */\n  function referenceCollapsedOpen(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.enter(types.referenceMarker);\n      effects.consume(code);\n      effects.exit(types.referenceMarker);\n      effects.exit(types.reference);\n      return ok;\n    }\n    return nok(code);\n  }\n}", "map": {"version": 3, "names": ["factoryDestination", "factoryLabel", "factoryTitle", "factoryWhitespace", "markdownLineEndingOrSpace", "push", "splice", "normalizeIdentifier", "resolveAll", "codes", "constants", "types", "ok", "assert", "labelEnd", "name", "tokenize", "tokenizeLabelEnd", "resolveTo", "resolveToLabelEnd", "resolveAllLabelEnd", "resourceConstruct", "tokenizeResource", "referenceFullConstruct", "tokenizeReferenceFull", "referenceCollapsedConstruct", "tokenizeReferenceCollapsed", "events", "index", "length", "token", "type", "labelImage", "labelLink", "data", "context", "offset", "open", "close", "media", "link", "_inactive", "_balanced", "undefined", "group", "image", "start", "Object", "assign", "end", "label", "text", "labelText", "slice", "parser", "constructs", "insideSpan", "null", "effects", "nok", "self", "labelStart", "defined", "code", "rightSquareBracket", "labelEndNok", "includes", "sliceSerialize", "now", "enter", "labelMarker", "consume", "exit", "after", "leftParenthesis", "attempt", "labelEndOk", "leftSquareBracket", "referenceNotFull", "resourceStart", "resource", "resourceMarker", "resourceBefore", "resourceOpen", "rightParenthesis", "resourceEnd", "resourceDestinationAfter", "resourceDestinationMissing", "resourceDestination", "resourceDestinationLiteral", "resourceDestinationLiteralMarker", "resourceDestinationRaw", "resourceDestinationString", "linkResourceDestinationBalanceMax", "resourceBetween", "quotationMark", "apostrophe", "resourceTitleAfter", "resourceTitle", "resourceTitleMarker", "resourceTitleString", "referenceFull", "call", "referenceFullAfter", "referenceFullMissing", "reference", "<PERSON><PERSON><PERSON><PERSON>", "referenceString", "referenceCollapsedStart", "referenceCollapsedOpen"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/micromark-core-commonmark/dev/lib/label-end.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').Event} Event\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\nimport {factoryDestination} from 'micromark-factory-destination'\nimport {factoryLabel} from 'micromark-factory-label'\nimport {factoryTitle} from 'micromark-factory-title'\nimport {factoryWhitespace} from 'micromark-factory-whitespace'\nimport {markdownLineEndingOrSpace} from 'micromark-util-character'\nimport {push, splice} from 'micromark-util-chunked'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {resolveAll} from 'micromark-util-resolve-all'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {constants} from 'micromark-util-symbol/constants.js'\nimport {types} from 'micromark-util-symbol/types.js'\nimport {ok as assert} from 'uvu/assert'\n\n/** @type {Construct} */\nexport const labelEnd = {\n  name: 'labelEnd',\n  tokenize: tokenizeLabelEnd,\n  resolveTo: resolveToLabelEnd,\n  resolveAll: resolveAllLabelEnd\n}\n\n/** @type {Construct} */\nconst resourceConstruct = {tokenize: tokenizeResource}\n/** @type {Construct} */\nconst referenceFullConstruct = {tokenize: tokenizeReferenceFull}\n/** @type {Construct} */\nconst referenceCollapsedConstruct = {tokenize: tokenizeReferenceCollapsed}\n\n/** @type {Resolver} */\nfunction resolveAllLabelEnd(events) {\n  let index = -1\n\n  while (++index < events.length) {\n    const token = events[index][1]\n\n    if (\n      token.type === types.labelImage ||\n      token.type === types.labelLink ||\n      token.type === types.labelEnd\n    ) {\n      // Remove the marker.\n      events.splice(index + 1, token.type === types.labelImage ? 4 : 2)\n      token.type = types.data\n      index++\n    }\n  }\n\n  return events\n}\n\n/** @type {Resolver} */\nfunction resolveToLabelEnd(events, context) {\n  let index = events.length\n  let offset = 0\n  /** @type {Token} */\n  let token\n  /** @type {number | undefined} */\n  let open\n  /** @type {number | undefined} */\n  let close\n  /** @type {Array<Event>} */\n  let media\n\n  // Find an opening.\n  while (index--) {\n    token = events[index][1]\n\n    if (open) {\n      // If we see another link, or inactive link label, we’ve been here before.\n      if (\n        token.type === types.link ||\n        (token.type === types.labelLink && token._inactive)\n      ) {\n        break\n      }\n\n      // Mark other link openings as inactive, as we can’t have links in\n      // links.\n      if (events[index][0] === 'enter' && token.type === types.labelLink) {\n        token._inactive = true\n      }\n    } else if (close) {\n      if (\n        events[index][0] === 'enter' &&\n        (token.type === types.labelImage || token.type === types.labelLink) &&\n        !token._balanced\n      ) {\n        open = index\n\n        if (token.type !== types.labelLink) {\n          offset = 2\n          break\n        }\n      }\n    } else if (token.type === types.labelEnd) {\n      close = index\n    }\n  }\n\n  assert(open !== undefined, '`open` is supposed to be found')\n  assert(close !== undefined, '`close` is supposed to be found')\n\n  const group = {\n    type: events[open][1].type === types.labelLink ? types.link : types.image,\n    start: Object.assign({}, events[open][1].start),\n    end: Object.assign({}, events[events.length - 1][1].end)\n  }\n\n  const label = {\n    type: types.label,\n    start: Object.assign({}, events[open][1].start),\n    end: Object.assign({}, events[close][1].end)\n  }\n\n  const text = {\n    type: types.labelText,\n    start: Object.assign({}, events[open + offset + 2][1].end),\n    end: Object.assign({}, events[close - 2][1].start)\n  }\n\n  media = [\n    ['enter', group, context],\n    ['enter', label, context]\n  ]\n\n  // Opening marker.\n  media = push(media, events.slice(open + 1, open + offset + 3))\n\n  // Text open.\n  media = push(media, [['enter', text, context]])\n\n  // Always populated by defaults.\n  assert(\n    context.parser.constructs.insideSpan.null,\n    'expected `insideSpan.null` to be populated'\n  )\n  // Between.\n  media = push(\n    media,\n    resolveAll(\n      context.parser.constructs.insideSpan.null,\n      events.slice(open + offset + 4, close - 3),\n      context\n    )\n  )\n\n  // Text close, marker close, label close.\n  media = push(media, [\n    ['exit', text, context],\n    events[close - 2],\n    events[close - 1],\n    ['exit', label, context]\n  ])\n\n  // Reference, resource, or so.\n  media = push(media, events.slice(close + 1))\n\n  // Media close.\n  media = push(media, [['exit', group, context]])\n\n  splice(events, open, events.length, media)\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeLabelEnd(effects, ok, nok) {\n  const self = this\n  let index = self.events.length\n  /** @type {Token} */\n  let labelStart\n  /** @type {boolean} */\n  let defined\n\n  // Find an opening.\n  while (index--) {\n    if (\n      (self.events[index][1].type === types.labelImage ||\n        self.events[index][1].type === types.labelLink) &&\n      !self.events[index][1]._balanced\n    ) {\n      labelStart = self.events[index][1]\n      break\n    }\n  }\n\n  return start\n\n  /**\n   * Start of label end.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.rightSquareBracket, 'expected `]`')\n\n    // If there is not an okay opening.\n    if (!labelStart) {\n      return nok(code)\n    }\n\n    // If the corresponding label (link) start is marked as inactive,\n    // it means we’d be wrapping a link, like this:\n    //\n    // ```markdown\n    // > | a [b [c](d) e](f) g.\n    //                  ^\n    // ```\n    //\n    // We can’t have that, so it’s just balanced brackets.\n    if (labelStart._inactive) {\n      return labelEndNok(code)\n    }\n\n    defined = self.parser.defined.includes(\n      normalizeIdentifier(\n        self.sliceSerialize({start: labelStart.end, end: self.now()})\n      )\n    )\n    effects.enter(types.labelEnd)\n    effects.enter(types.labelMarker)\n    effects.consume(code)\n    effects.exit(types.labelMarker)\n    effects.exit(types.labelEnd)\n    return after\n  }\n\n  /**\n   * After `]`.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // Note: `markdown-rs` also parses GFM footnotes here, which for us is in\n    // an extension.\n\n    // Resource (`[asd](fgh)`)?\n    if (code === codes.leftParenthesis) {\n      return effects.attempt(\n        resourceConstruct,\n        labelEndOk,\n        defined ? labelEndOk : labelEndNok\n      )(code)\n    }\n\n    // Full (`[asd][fgh]`) or collapsed (`[asd][]`) reference?\n    if (code === codes.leftSquareBracket) {\n      return effects.attempt(\n        referenceFullConstruct,\n        labelEndOk,\n        defined ? referenceNotFull : labelEndNok\n      )(code)\n    }\n\n    // Shortcut (`[asd]`) reference?\n    return defined ? labelEndOk(code) : labelEndNok(code)\n  }\n\n  /**\n   * After `]`, at `[`, but not at a full reference.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] b\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceNotFull(code) {\n    return effects.attempt(\n      referenceCollapsedConstruct,\n      labelEndOk,\n      labelEndNok\n    )(code)\n  }\n\n  /**\n   * Done, we found something.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *           ^\n   * > | [a][b] c\n   *           ^\n   * > | [a][] b\n   *          ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndOk(code) {\n    // Note: `markdown-rs` does a bunch of stuff here.\n    return ok(code)\n  }\n\n  /**\n   * Done, it’s nothing.\n   *\n   * There was an okay opening, but we didn’t match anything.\n   *\n   * ```markdown\n   * > | [a](b c\n   *        ^\n   * > | [a][b c\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndNok(code) {\n    labelStart._balanced = true\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeResource(effects, ok, nok) {\n  return resourceStart\n\n  /**\n   * At a resource.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceStart(code) {\n    assert(code === codes.leftParenthesis, 'expected left paren')\n    effects.enter(types.resource)\n    effects.enter(types.resourceMarker)\n    effects.consume(code)\n    effects.exit(types.resourceMarker)\n    return resourceBefore\n  }\n\n  /**\n   * In resource, after `(`, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBefore(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, resourceOpen)(code)\n      : resourceOpen(code)\n  }\n\n  /**\n   * In resource, after optional whitespace, at `)` or a destination.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceOpen(code) {\n    if (code === codes.rightParenthesis) {\n      return resourceEnd(code)\n    }\n\n    return factoryDestination(\n      effects,\n      resourceDestinationAfter,\n      resourceDestinationMissing,\n      types.resourceDestination,\n      types.resourceDestinationLiteral,\n      types.resourceDestinationLiteralMarker,\n      types.resourceDestinationRaw,\n      types.resourceDestinationString,\n      constants.linkResourceDestinationBalanceMax\n    )(code)\n  }\n\n  /**\n   * In resource, after destination, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationAfter(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, resourceBetween)(code)\n      : resourceEnd(code)\n  }\n\n  /**\n   * At invalid destination.\n   *\n   * ```markdown\n   * > | [a](<<) b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationMissing(code) {\n    return nok(code)\n  }\n\n  /**\n   * In resource, after destination and whitespace, at `(` or title.\n   *\n   * ```markdown\n   * > | [a](b ) c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBetween(code) {\n    if (\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.leftParenthesis\n    ) {\n      return factoryTitle(\n        effects,\n        resourceTitleAfter,\n        nok,\n        types.resourceTitle,\n        types.resourceTitleMarker,\n        types.resourceTitleString\n      )(code)\n    }\n\n    return resourceEnd(code)\n  }\n\n  /**\n   * In resource, after title, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b \"c\") d\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceTitleAfter(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, resourceEnd)(code)\n      : resourceEnd(code)\n  }\n\n  /**\n   * In resource, at `)`.\n   *\n   * ```markdown\n   * > | [a](b) d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceEnd(code) {\n    if (code === codes.rightParenthesis) {\n      effects.enter(types.resourceMarker)\n      effects.consume(code)\n      effects.exit(types.resourceMarker)\n      effects.exit(types.resource)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceFull(effects, ok, nok) {\n  const self = this\n\n  return referenceFull\n\n  /**\n   * In a reference (full), at the `[`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFull(code) {\n    assert(code === codes.leftSquareBracket, 'expected left bracket')\n    return factoryLabel.call(\n      self,\n      effects,\n      referenceFullAfter,\n      referenceFullMissing,\n      types.reference,\n      types.referenceMarker,\n      types.referenceString\n    )(code)\n  }\n\n  /**\n   * In a reference (full), after `]`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullAfter(code) {\n    return self.parser.defined.includes(\n      normalizeIdentifier(\n        self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)\n      )\n    )\n      ? ok(code)\n      : nok(code)\n  }\n\n  /**\n   * In reference (full) that was missing.\n   *\n   * ```markdown\n   * > | [a][b d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullMissing(code) {\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceCollapsed(effects, ok, nok) {\n  return referenceCollapsedStart\n\n  /**\n   * In reference (collapsed), at `[`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceCollapsedStart(code) {\n    // We only attempt a collapsed label if there’s a `[`.\n    assert(code === codes.leftSquareBracket, 'expected left bracket')\n    effects.enter(types.reference)\n    effects.enter(types.referenceMarker)\n    effects.consume(code)\n    effects.exit(types.referenceMarker)\n    return referenceCollapsedOpen\n  }\n\n  /**\n   * In reference (collapsed), at `]`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *         ^\n   * ```\n   *\n   *  @type {State}\n   */\n  function referenceCollapsedOpen(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.enter(types.referenceMarker)\n      effects.consume(code)\n      effects.exit(types.referenceMarker)\n      effects.exit(types.reference)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,kBAAkB,QAAO,+BAA+B;AAChE,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,iBAAiB,QAAO,8BAA8B;AAC9D,SAAQC,yBAAyB,QAAO,0BAA0B;AAClE,SAAQC,IAAI,EAAEC,MAAM,QAAO,wBAAwB;AACnD,SAAQC,mBAAmB,QAAO,qCAAqC;AACvE,SAAQC,UAAU,QAAO,4BAA4B;AACrD,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,SAAS,QAAO,oCAAoC;AAC5D,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;;AAEvC;AACA,OAAO,MAAMC,QAAQ,GAAG;EACtBC,IAAI,EAAE,UAAU;EAChBC,QAAQ,EAAEC,gBAAgB;EAC1BC,SAAS,EAAEC,iBAAiB;EAC5BX,UAAU,EAAEY;AACd,CAAC;;AAED;AACA,MAAMC,iBAAiB,GAAG;EAACL,QAAQ,EAAEM;AAAgB,CAAC;AACtD;AACA,MAAMC,sBAAsB,GAAG;EAACP,QAAQ,EAAEQ;AAAqB,CAAC;AAChE;AACA,MAAMC,2BAA2B,GAAG;EAACT,QAAQ,EAAEU;AAA0B,CAAC;;AAE1E;AACA,SAASN,kBAAkBA,CAACO,MAAM,EAAE;EAClC,IAAIC,KAAK,GAAG,CAAC,CAAC;EAEd,OAAO,EAAEA,KAAK,GAAGD,MAAM,CAACE,MAAM,EAAE;IAC9B,MAAMC,KAAK,GAAGH,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IAE9B,IACEE,KAAK,CAACC,IAAI,KAAKpB,KAAK,CAACqB,UAAU,IAC/BF,KAAK,CAACC,IAAI,KAAKpB,KAAK,CAACsB,SAAS,IAC9BH,KAAK,CAACC,IAAI,KAAKpB,KAAK,CAACG,QAAQ,EAC7B;MACA;MACAa,MAAM,CAACrB,MAAM,CAACsB,KAAK,GAAG,CAAC,EAAEE,KAAK,CAACC,IAAI,KAAKpB,KAAK,CAACqB,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;MACjEF,KAAK,CAACC,IAAI,GAAGpB,KAAK,CAACuB,IAAI;MACvBN,KAAK,EAAE;IACT;EACF;EAEA,OAAOD,MAAM;AACf;;AAEA;AACA,SAASR,iBAAiBA,CAACQ,MAAM,EAAEQ,OAAO,EAAE;EAC1C,IAAIP,KAAK,GAAGD,MAAM,CAACE,MAAM;EACzB,IAAIO,MAAM,GAAG,CAAC;EACd;EACA,IAAIN,KAAK;EACT;EACA,IAAIO,IAAI;EACR;EACA,IAAIC,KAAK;EACT;EACA,IAAIC,KAAK;;EAET;EACA,OAAOX,KAAK,EAAE,EAAE;IACdE,KAAK,GAAGH,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IAExB,IAAIS,IAAI,EAAE;MACR;MACA,IACEP,KAAK,CAACC,IAAI,KAAKpB,KAAK,CAAC6B,IAAI,IACxBV,KAAK,CAACC,IAAI,KAAKpB,KAAK,CAACsB,SAAS,IAAIH,KAAK,CAACW,SAAU,EACnD;QACA;MACF;;MAEA;MACA;MACA,IAAId,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,IAAIE,KAAK,CAACC,IAAI,KAAKpB,KAAK,CAACsB,SAAS,EAAE;QAClEH,KAAK,CAACW,SAAS,GAAG,IAAI;MACxB;IACF,CAAC,MAAM,IAAIH,KAAK,EAAE;MAChB,IACEX,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,KAC3BE,KAAK,CAACC,IAAI,KAAKpB,KAAK,CAACqB,UAAU,IAAIF,KAAK,CAACC,IAAI,KAAKpB,KAAK,CAACsB,SAAS,CAAC,IACnE,CAACH,KAAK,CAACY,SAAS,EAChB;QACAL,IAAI,GAAGT,KAAK;QAEZ,IAAIE,KAAK,CAACC,IAAI,KAAKpB,KAAK,CAACsB,SAAS,EAAE;UAClCG,MAAM,GAAG,CAAC;UACV;QACF;MACF;IACF,CAAC,MAAM,IAAIN,KAAK,CAACC,IAAI,KAAKpB,KAAK,CAACG,QAAQ,EAAE;MACxCwB,KAAK,GAAGV,KAAK;IACf;EACF;EAEAf,MAAM,CAACwB,IAAI,KAAKM,SAAS,EAAE,gCAAgC,CAAC;EAC5D9B,MAAM,CAACyB,KAAK,KAAKK,SAAS,EAAE,iCAAiC,CAAC;EAE9D,MAAMC,KAAK,GAAG;IACZb,IAAI,EAAEJ,MAAM,CAACU,IAAI,CAAC,CAAC,CAAC,CAAC,CAACN,IAAI,KAAKpB,KAAK,CAACsB,SAAS,GAAGtB,KAAK,CAAC6B,IAAI,GAAG7B,KAAK,CAACkC,KAAK;IACzEC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAErB,MAAM,CAACU,IAAI,CAAC,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC;IAC/CG,GAAG,EAAEF,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAErB,MAAM,CAACA,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACoB,GAAG;EACzD,CAAC;EAED,MAAMC,KAAK,GAAG;IACZnB,IAAI,EAAEpB,KAAK,CAACuC,KAAK;IACjBJ,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAErB,MAAM,CAACU,IAAI,CAAC,CAAC,CAAC,CAAC,CAACS,KAAK,CAAC;IAC/CG,GAAG,EAAEF,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAErB,MAAM,CAACW,KAAK,CAAC,CAAC,CAAC,CAAC,CAACW,GAAG;EAC7C,CAAC;EAED,MAAME,IAAI,GAAG;IACXpB,IAAI,EAAEpB,KAAK,CAACyC,SAAS;IACrBN,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAErB,MAAM,CAACU,IAAI,GAAGD,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACa,GAAG,CAAC;IAC1DA,GAAG,EAAEF,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAErB,MAAM,CAACW,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACQ,KAAK;EACnD,CAAC;EAEDP,KAAK,GAAG,CACN,CAAC,OAAO,EAAEK,KAAK,EAAET,OAAO,CAAC,EACzB,CAAC,OAAO,EAAEe,KAAK,EAAEf,OAAO,CAAC,CAC1B;;EAED;EACAI,KAAK,GAAGlC,IAAI,CAACkC,KAAK,EAAEZ,MAAM,CAAC0B,KAAK,CAAChB,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGD,MAAM,GAAG,CAAC,CAAC,CAAC;;EAE9D;EACAG,KAAK,GAAGlC,IAAI,CAACkC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAEY,IAAI,EAAEhB,OAAO,CAAC,CAAC,CAAC;;EAE/C;EACAtB,MAAM,CACJsB,OAAO,CAACmB,MAAM,CAACC,UAAU,CAACC,UAAU,CAACC,IAAI,EACzC,4CACF,CAAC;EACD;EACAlB,KAAK,GAAGlC,IAAI,CACVkC,KAAK,EACL/B,UAAU,CACR2B,OAAO,CAACmB,MAAM,CAACC,UAAU,CAACC,UAAU,CAACC,IAAI,EACzC9B,MAAM,CAAC0B,KAAK,CAAChB,IAAI,GAAGD,MAAM,GAAG,CAAC,EAAEE,KAAK,GAAG,CAAC,CAAC,EAC1CH,OACF,CACF,CAAC;;EAED;EACAI,KAAK,GAAGlC,IAAI,CAACkC,KAAK,EAAE,CAClB,CAAC,MAAM,EAAEY,IAAI,EAAEhB,OAAO,CAAC,EACvBR,MAAM,CAACW,KAAK,GAAG,CAAC,CAAC,EACjBX,MAAM,CAACW,KAAK,GAAG,CAAC,CAAC,EACjB,CAAC,MAAM,EAAEY,KAAK,EAAEf,OAAO,CAAC,CACzB,CAAC;;EAEF;EACAI,KAAK,GAAGlC,IAAI,CAACkC,KAAK,EAAEZ,MAAM,CAAC0B,KAAK,CAACf,KAAK,GAAG,CAAC,CAAC,CAAC;;EAE5C;EACAC,KAAK,GAAGlC,IAAI,CAACkC,KAAK,EAAE,CAAC,CAAC,MAAM,EAAEK,KAAK,EAAET,OAAO,CAAC,CAAC,CAAC;EAE/C7B,MAAM,CAACqB,MAAM,EAAEU,IAAI,EAAEV,MAAM,CAACE,MAAM,EAAEU,KAAK,CAAC;EAE1C,OAAOZ,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA,SAASV,gBAAgBA,CAACyC,OAAO,EAAE9C,EAAE,EAAE+C,GAAG,EAAE;EAC1C,MAAMC,IAAI,GAAG,IAAI;EACjB,IAAIhC,KAAK,GAAGgC,IAAI,CAACjC,MAAM,CAACE,MAAM;EAC9B;EACA,IAAIgC,UAAU;EACd;EACA,IAAIC,OAAO;;EAEX;EACA,OAAOlC,KAAK,EAAE,EAAE;IACd,IACE,CAACgC,IAAI,CAACjC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACG,IAAI,KAAKpB,KAAK,CAACqB,UAAU,IAC9C4B,IAAI,CAACjC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACG,IAAI,KAAKpB,KAAK,CAACsB,SAAS,KAChD,CAAC2B,IAAI,CAACjC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACc,SAAS,EAChC;MACAmB,UAAU,GAAGD,IAAI,CAACjC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;MAClC;IACF;EACF;EAEA,OAAOkB,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACiB,IAAI,EAAE;IACnBlD,MAAM,CAACkD,IAAI,KAAKtD,KAAK,CAACuD,kBAAkB,EAAE,cAAc,CAAC;;IAEzD;IACA,IAAI,CAACH,UAAU,EAAE;MACf,OAAOF,GAAG,CAACI,IAAI,CAAC;IAClB;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIF,UAAU,CAACpB,SAAS,EAAE;MACxB,OAAOwB,WAAW,CAACF,IAAI,CAAC;IAC1B;IAEAD,OAAO,GAAGF,IAAI,CAACN,MAAM,CAACQ,OAAO,CAACI,QAAQ,CACpC3D,mBAAmB,CACjBqD,IAAI,CAACO,cAAc,CAAC;MAACrB,KAAK,EAAEe,UAAU,CAACZ,GAAG;MAAEA,GAAG,EAAEW,IAAI,CAACQ,GAAG,CAAC;IAAC,CAAC,CAC9D,CACF,CAAC;IACDV,OAAO,CAACW,KAAK,CAAC1D,KAAK,CAACG,QAAQ,CAAC;IAC7B4C,OAAO,CAACW,KAAK,CAAC1D,KAAK,CAAC2D,WAAW,CAAC;IAChCZ,OAAO,CAACa,OAAO,CAACR,IAAI,CAAC;IACrBL,OAAO,CAACc,IAAI,CAAC7D,KAAK,CAAC2D,WAAW,CAAC;IAC/BZ,OAAO,CAACc,IAAI,CAAC7D,KAAK,CAACG,QAAQ,CAAC;IAC5B,OAAO2D,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACV,IAAI,EAAE;IACnB;IACA;;IAEA;IACA,IAAIA,IAAI,KAAKtD,KAAK,CAACiE,eAAe,EAAE;MAClC,OAAOhB,OAAO,CAACiB,OAAO,CACpBtD,iBAAiB,EACjBuD,UAAU,EACVd,OAAO,GAAGc,UAAU,GAAGX,WACzB,CAAC,CAACF,IAAI,CAAC;IACT;;IAEA;IACA,IAAIA,IAAI,KAAKtD,KAAK,CAACoE,iBAAiB,EAAE;MACpC,OAAOnB,OAAO,CAACiB,OAAO,CACpBpD,sBAAsB,EACtBqD,UAAU,EACVd,OAAO,GAAGgB,gBAAgB,GAAGb,WAC/B,CAAC,CAACF,IAAI,CAAC;IACT;;IAEA;IACA,OAAOD,OAAO,GAAGc,UAAU,CAACb,IAAI,CAAC,GAAGE,WAAW,CAACF,IAAI,CAAC;EACvD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASe,gBAAgBA,CAACf,IAAI,EAAE;IAC9B,OAAOL,OAAO,CAACiB,OAAO,CACpBlD,2BAA2B,EAC3BmD,UAAU,EACVX,WACF,CAAC,CAACF,IAAI,CAAC;EACT;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASa,UAAUA,CAACb,IAAI,EAAE;IACxB;IACA,OAAOnD,EAAE,CAACmD,IAAI,CAAC;EACjB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASE,WAAWA,CAACF,IAAI,EAAE;IACzBF,UAAU,CAACnB,SAAS,GAAG,IAAI;IAC3B,OAAOiB,GAAG,CAACI,IAAI,CAAC;EAClB;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASzC,gBAAgBA,CAACoC,OAAO,EAAE9C,EAAE,EAAE+C,GAAG,EAAE;EAC1C,OAAOoB,aAAa;;EAEpB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,aAAaA,CAAChB,IAAI,EAAE;IAC3BlD,MAAM,CAACkD,IAAI,KAAKtD,KAAK,CAACiE,eAAe,EAAE,qBAAqB,CAAC;IAC7DhB,OAAO,CAACW,KAAK,CAAC1D,KAAK,CAACqE,QAAQ,CAAC;IAC7BtB,OAAO,CAACW,KAAK,CAAC1D,KAAK,CAACsE,cAAc,CAAC;IACnCvB,OAAO,CAACa,OAAO,CAACR,IAAI,CAAC;IACrBL,OAAO,CAACc,IAAI,CAAC7D,KAAK,CAACsE,cAAc,CAAC;IAClC,OAAOC,cAAc;EACvB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,cAAcA,CAACnB,IAAI,EAAE;IAC5B,OAAO3D,yBAAyB,CAAC2D,IAAI,CAAC,GAClC5D,iBAAiB,CAACuD,OAAO,EAAEyB,YAAY,CAAC,CAACpB,IAAI,CAAC,GAC9CoB,YAAY,CAACpB,IAAI,CAAC;EACxB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASoB,YAAYA,CAACpB,IAAI,EAAE;IAC1B,IAAIA,IAAI,KAAKtD,KAAK,CAAC2E,gBAAgB,EAAE;MACnC,OAAOC,WAAW,CAACtB,IAAI,CAAC;IAC1B;IAEA,OAAO/D,kBAAkB,CACvB0D,OAAO,EACP4B,wBAAwB,EACxBC,0BAA0B,EAC1B5E,KAAK,CAAC6E,mBAAmB,EACzB7E,KAAK,CAAC8E,0BAA0B,EAChC9E,KAAK,CAAC+E,gCAAgC,EACtC/E,KAAK,CAACgF,sBAAsB,EAC5BhF,KAAK,CAACiF,yBAAyB,EAC/BlF,SAAS,CAACmF,iCACZ,CAAC,CAAC9B,IAAI,CAAC;EACT;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASuB,wBAAwBA,CAACvB,IAAI,EAAE;IACtC,OAAO3D,yBAAyB,CAAC2D,IAAI,CAAC,GAClC5D,iBAAiB,CAACuD,OAAO,EAAEoC,eAAe,CAAC,CAAC/B,IAAI,CAAC,GACjDsB,WAAW,CAACtB,IAAI,CAAC;EACvB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASwB,0BAA0BA,CAACxB,IAAI,EAAE;IACxC,OAAOJ,GAAG,CAACI,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS+B,eAAeA,CAAC/B,IAAI,EAAE;IAC7B,IACEA,IAAI,KAAKtD,KAAK,CAACsF,aAAa,IAC5BhC,IAAI,KAAKtD,KAAK,CAACuF,UAAU,IACzBjC,IAAI,KAAKtD,KAAK,CAACiE,eAAe,EAC9B;MACA,OAAOxE,YAAY,CACjBwD,OAAO,EACPuC,kBAAkB,EAClBtC,GAAG,EACHhD,KAAK,CAACuF,aAAa,EACnBvF,KAAK,CAACwF,mBAAmB,EACzBxF,KAAK,CAACyF,mBACR,CAAC,CAACrC,IAAI,CAAC;IACT;IAEA,OAAOsB,WAAW,CAACtB,IAAI,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASkC,kBAAkBA,CAAClC,IAAI,EAAE;IAChC,OAAO3D,yBAAyB,CAAC2D,IAAI,CAAC,GAClC5D,iBAAiB,CAACuD,OAAO,EAAE2B,WAAW,CAAC,CAACtB,IAAI,CAAC,GAC7CsB,WAAW,CAACtB,IAAI,CAAC;EACvB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASsB,WAAWA,CAACtB,IAAI,EAAE;IACzB,IAAIA,IAAI,KAAKtD,KAAK,CAAC2E,gBAAgB,EAAE;MACnC1B,OAAO,CAACW,KAAK,CAAC1D,KAAK,CAACsE,cAAc,CAAC;MACnCvB,OAAO,CAACa,OAAO,CAACR,IAAI,CAAC;MACrBL,OAAO,CAACc,IAAI,CAAC7D,KAAK,CAACsE,cAAc,CAAC;MAClCvB,OAAO,CAACc,IAAI,CAAC7D,KAAK,CAACqE,QAAQ,CAAC;MAC5B,OAAOpE,EAAE;IACX;IAEA,OAAO+C,GAAG,CAACI,IAAI,CAAC;EAClB;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASvC,qBAAqBA,CAACkC,OAAO,EAAE9C,EAAE,EAAE+C,GAAG,EAAE;EAC/C,MAAMC,IAAI,GAAG,IAAI;EAEjB,OAAOyC,aAAa;;EAEpB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,aAAaA,CAACtC,IAAI,EAAE;IAC3BlD,MAAM,CAACkD,IAAI,KAAKtD,KAAK,CAACoE,iBAAiB,EAAE,uBAAuB,CAAC;IACjE,OAAO5E,YAAY,CAACqG,IAAI,CACtB1C,IAAI,EACJF,OAAO,EACP6C,kBAAkB,EAClBC,oBAAoB,EACpB7F,KAAK,CAAC8F,SAAS,EACf9F,KAAK,CAAC+F,eAAe,EACrB/F,KAAK,CAACgG,eACR,CAAC,CAAC5C,IAAI,CAAC;EACT;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASwC,kBAAkBA,CAACxC,IAAI,EAAE;IAChC,OAAOH,IAAI,CAACN,MAAM,CAACQ,OAAO,CAACI,QAAQ,CACjC3D,mBAAmB,CACjBqD,IAAI,CAACO,cAAc,CAACP,IAAI,CAACjC,MAAM,CAACiC,IAAI,CAACjC,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACwB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACzE,CACF,CAAC,GACGzC,EAAE,CAACmD,IAAI,CAAC,GACRJ,GAAG,CAACI,IAAI,CAAC;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASyC,oBAAoBA,CAACzC,IAAI,EAAE;IAClC,OAAOJ,GAAG,CAACI,IAAI,CAAC;EAClB;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASrC,0BAA0BA,CAACgC,OAAO,EAAE9C,EAAE,EAAE+C,GAAG,EAAE;EACpD,OAAOiD,uBAAuB;;EAE9B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,uBAAuBA,CAAC7C,IAAI,EAAE;IACrC;IACAlD,MAAM,CAACkD,IAAI,KAAKtD,KAAK,CAACoE,iBAAiB,EAAE,uBAAuB,CAAC;IACjEnB,OAAO,CAACW,KAAK,CAAC1D,KAAK,CAAC8F,SAAS,CAAC;IAC9B/C,OAAO,CAACW,KAAK,CAAC1D,KAAK,CAAC+F,eAAe,CAAC;IACpChD,OAAO,CAACa,OAAO,CAACR,IAAI,CAAC;IACrBL,OAAO,CAACc,IAAI,CAAC7D,KAAK,CAAC+F,eAAe,CAAC;IACnC,OAAOG,sBAAsB;EAC/B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,sBAAsBA,CAAC9C,IAAI,EAAE;IACpC,IAAIA,IAAI,KAAKtD,KAAK,CAACuD,kBAAkB,EAAE;MACrCN,OAAO,CAACW,KAAK,CAAC1D,KAAK,CAAC+F,eAAe,CAAC;MACpChD,OAAO,CAACa,OAAO,CAACR,IAAI,CAAC;MACrBL,OAAO,CAACc,IAAI,CAAC7D,KAAK,CAAC+F,eAAe,CAAC;MACnChD,OAAO,CAACc,IAAI,CAAC7D,KAAK,CAAC8F,SAAS,CAAC;MAC7B,OAAO7F,EAAE;IACX;IAEA,OAAO+C,GAAG,CAACI,IAAI,CAAC;EAClB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}