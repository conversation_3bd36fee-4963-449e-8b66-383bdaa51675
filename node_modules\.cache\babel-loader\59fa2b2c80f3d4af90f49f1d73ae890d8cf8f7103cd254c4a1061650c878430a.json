{"ast": null, "code": "import { spring } from '../../animation/generators/spring/index.mjs';\nimport { calcGeneratorDuration, maxGeneratorDuration } from '../../animation/generators/utils/calc-duration.mjs';\nimport { millisecondsToSeconds } from '../../utils/time-conversion.mjs';\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options, scale = 100) {\n  const generator = spring({\n    keyframes: [0, scale],\n    ...options\n  });\n  const duration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n  return {\n    type: \"keyframes\",\n    ease: progress => generator.next(duration * progress).value / scale,\n    duration: millisecondsToSeconds(duration)\n  };\n}\nexport { createGeneratorEasing };", "map": {"version": 3, "names": ["spring", "calcGeneratorDuration", "maxGeneratorDuration", "millisecondsToSeconds", "createGeneratorEasing", "options", "scale", "generator", "keyframes", "duration", "Math", "min", "type", "ease", "progress", "next", "value"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/framer-motion/dist/es/easing/utils/create-generator-easing.mjs"], "sourcesContent": ["import { spring } from '../../animation/generators/spring/index.mjs';\nimport { calcGeneratorDuration, maxGeneratorDuration } from '../../animation/generators/utils/calc-duration.mjs';\nimport { millisecondsToSeconds } from '../../utils/time-conversion.mjs';\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options, scale = 100) {\n    const generator = spring({ keyframes: [0, scale], ...options });\n    const duration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n    return {\n        type: \"keyframes\",\n        ease: (progress) => generator.next(duration * progress).value / scale,\n        duration: millisecondsToSeconds(duration),\n    };\n}\n\nexport { createGeneratorEasing };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,6CAA6C;AACpE,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,oDAAoD;AAChH,SAASC,qBAAqB,QAAQ,iCAAiC;;AAEvE;AACA;AACA;AACA,SAASC,qBAAqBA,CAACC,OAAO,EAAEC,KAAK,GAAG,GAAG,EAAE;EACjD,MAAMC,SAAS,GAAGP,MAAM,CAAC;IAAEQ,SAAS,EAAE,CAAC,CAAC,EAAEF,KAAK,CAAC;IAAE,GAAGD;EAAQ,CAAC,CAAC;EAC/D,MAAMI,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACV,qBAAqB,CAACM,SAAS,CAAC,EAAEL,oBAAoB,CAAC;EACjF,OAAO;IACHU,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAGC,QAAQ,IAAKP,SAAS,CAACQ,IAAI,CAACN,QAAQ,GAAGK,QAAQ,CAAC,CAACE,KAAK,GAAGV,KAAK;IACrEG,QAAQ,EAAEN,qBAAqB,CAACM,QAAQ;EAC5C,CAAC;AACL;AAEA,SAASL,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}