{"ast": null, "code": "/**\n * @typedef {import('react').ReactNode} ReactNode\n * @typedef {import('react').ReactElement<{}>} ReactElement\n * @typedef {import('unified').PluggableList} PluggableList\n * @typedef {import('hast').Root} Root\n * @typedef {import('./rehype-filter.js').Options} FilterOptions\n * @typedef {import('./ast-to-react.js').Options} TransformOptions\n *\n * @typedef CoreOptions\n * @property {string} children\n *\n * @typedef PluginOptions\n * @property {PluggableList} [remarkPlugins=[]]\n * @property {PluggableList} [rehypePlugins=[]]\n * @property {import('remark-rehype').Options | undefined} [remarkRehypeOptions={}]\n *\n * @typedef LayoutOptions\n * @property {string} [className]\n *\n * @typedef {CoreOptions & PluginOptions & LayoutOptions & FilterOptions & TransformOptions} ReactMarkdownOptions\n *\n * @typedef Deprecation\n * @property {string} id\n * @property {string} [to]\n */\n\nimport React from 'react';\nimport { VFile } from 'vfile';\nimport { unified } from 'unified';\nimport remarkParse from 'remark-parse';\nimport remarkRehype from 'remark-rehype';\nimport PropTypes from 'prop-types';\nimport { html } from 'property-information';\nimport rehypeFilter from './rehype-filter.js';\nimport { childrenToReact } from './ast-to-react.js';\nconst own = {}.hasOwnProperty;\nconst changelog = 'https://github.com/remarkjs/react-markdown/blob/main/changelog.md';\n\n/** @type {Record<string, Deprecation>} */\nconst deprecated = {\n  plugins: {\n    to: 'remarkPlugins',\n    id: 'change-plugins-to-remarkplugins'\n  },\n  renderers: {\n    to: 'components',\n    id: 'change-renderers-to-components'\n  },\n  astPlugins: {\n    id: 'remove-buggy-html-in-markdown-parser'\n  },\n  allowDangerousHtml: {\n    id: 'remove-buggy-html-in-markdown-parser'\n  },\n  escapeHtml: {\n    id: 'remove-buggy-html-in-markdown-parser'\n  },\n  source: {\n    to: 'children',\n    id: 'change-source-to-children'\n  },\n  allowNode: {\n    to: 'allowElement',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes'\n  },\n  allowedTypes: {\n    to: 'allowedElements',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes'\n  },\n  disallowedTypes: {\n    to: 'disallowedElements',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes'\n  },\n  includeNodeIndex: {\n    to: 'includeElementIndex',\n    id: 'change-includenodeindex-to-includeelementindex'\n  }\n};\n\n/**\n * React component to render markdown.\n *\n * @param {ReactMarkdownOptions} options\n * @returns {ReactElement}\n */\nexport function ReactMarkdown(options) {\n  for (const key in deprecated) {\n    if (own.call(deprecated, key) && own.call(options, key)) {\n      const deprecation = deprecated[key];\n      console.warn(`[react-markdown] Warning: please ${deprecation.to ? `use \\`${deprecation.to}\\` instead of` : 'remove'} \\`${key}\\` (see <${changelog}#${deprecation.id}> for more info)`);\n      delete deprecated[key];\n    }\n  }\n  const processor = unified().use(remarkParse).use(options.remarkPlugins || []).use(remarkRehype, {\n    ...options.remarkRehypeOptions,\n    allowDangerousHtml: true\n  }).use(options.rehypePlugins || []).use(rehypeFilter, options);\n  const file = new VFile();\n  if (typeof options.children === 'string') {\n    file.value = options.children;\n  } else if (options.children !== undefined && options.children !== null) {\n    console.warn(`[react-markdown] Warning: please pass a string as \\`children\\` (not: \\`${options.children}\\`)`);\n  }\n  const hastNode = processor.runSync(processor.parse(file), file);\n  if (hastNode.type !== 'root') {\n    throw new TypeError('Expected a `root` node');\n  }\n\n  /** @type {ReactElement} */\n  let result = React.createElement(React.Fragment, {}, childrenToReact({\n    options,\n    schema: html,\n    listDepth: 0\n  }, hastNode));\n  if (options.className) {\n    result = React.createElement('div', {\n      className: options.className\n    }, result);\n  }\n  return result;\n}\nReactMarkdown.propTypes = {\n  // Core options:\n  children: PropTypes.string,\n  // Layout options:\n  className: PropTypes.string,\n  // Filter options:\n  allowElement: PropTypes.func,\n  allowedElements: PropTypes.arrayOf(PropTypes.string),\n  disallowedElements: PropTypes.arrayOf(PropTypes.string),\n  unwrapDisallowed: PropTypes.bool,\n  // Plugin options:\n  remarkPlugins: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.object, PropTypes.func, PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.bool, PropTypes.string, PropTypes.object, PropTypes.func, PropTypes.arrayOf(\n  // prettier-ignore\n  // type-coverage:ignore-next-line\n  PropTypes.any)]))])),\n  rehypePlugins: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.object, PropTypes.func, PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.bool, PropTypes.string, PropTypes.object, PropTypes.func, PropTypes.arrayOf(\n  // prettier-ignore\n  // type-coverage:ignore-next-line\n  PropTypes.any)]))])),\n  // Transform options:\n  sourcePos: PropTypes.bool,\n  rawSourcePos: PropTypes.bool,\n  skipHtml: PropTypes.bool,\n  includeElementIndex: PropTypes.bool,\n  transformLinkUri: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n  linkTarget: PropTypes.oneOfType([PropTypes.func, PropTypes.string]),\n  transformImageUri: PropTypes.func,\n  components: PropTypes.object\n};", "map": {"version": 3, "names": ["React", "VFile", "unified", "remark<PERSON><PERSON><PERSON>", "remarkRehype", "PropTypes", "html", "rehypeFilter", "childrenToReact", "own", "hasOwnProperty", "changelog", "deprecated", "plugins", "to", "id", "renderers", "astPlugins", "allowDangerousHtml", "escapeHtml", "source", "allowNode", "allowedTypes", "disallowedTypes", "includeNodeIndex", "ReactMarkdown", "options", "key", "call", "deprecation", "console", "warn", "processor", "use", "remarkPlugins", "remarkRehypeOptions", "rehypePlugins", "file", "children", "value", "undefined", "hastNode", "runSync", "parse", "type", "TypeError", "result", "createElement", "Fragment", "schema", "listDepth", "className", "propTypes", "string", "allowElement", "func", "allowedElements", "arrayOf", "disallowedElements", "unwrapDisallowed", "bool", "oneOfType", "object", "any", "sourcePos", "rawSourcePos", "skipHtml", "includeElementIndex", "transformLinkUri", "linkTarget", "transformImageUri", "components"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/react-markdown/lib/react-markdown.js"], "sourcesContent": ["/**\n * @typedef {import('react').ReactNode} ReactNode\n * @typedef {import('react').ReactElement<{}>} ReactElement\n * @typedef {import('unified').PluggableList} PluggableList\n * @typedef {import('hast').Root} Root\n * @typedef {import('./rehype-filter.js').Options} FilterOptions\n * @typedef {import('./ast-to-react.js').Options} TransformOptions\n *\n * @typedef CoreOptions\n * @property {string} children\n *\n * @typedef PluginOptions\n * @property {PluggableList} [remarkPlugins=[]]\n * @property {PluggableList} [rehypePlugins=[]]\n * @property {import('remark-rehype').Options | undefined} [remarkRehypeOptions={}]\n *\n * @typedef LayoutOptions\n * @property {string} [className]\n *\n * @typedef {CoreOptions & PluginOptions & LayoutOptions & FilterOptions & TransformOptions} ReactMarkdownOptions\n *\n * @typedef Deprecation\n * @property {string} id\n * @property {string} [to]\n */\n\nimport React from 'react'\nimport {VFile} from 'vfile'\nimport {unified} from 'unified'\nimport remarkParse from 'remark-parse'\nimport remarkRehype from 'remark-rehype'\nimport PropTypes from 'prop-types'\nimport {html} from 'property-information'\nimport rehypeFilter from './rehype-filter.js'\nimport {childrenToReact} from './ast-to-react.js'\n\nconst own = {}.hasOwnProperty\nconst changelog =\n  'https://github.com/remarkjs/react-markdown/blob/main/changelog.md'\n\n/** @type {Record<string, Deprecation>} */\nconst deprecated = {\n  plugins: {to: 'remarkPlugins', id: 'change-plugins-to-remarkplugins'},\n  renderers: {to: 'components', id: 'change-renderers-to-components'},\n  astPlugins: {id: 'remove-buggy-html-in-markdown-parser'},\n  allowDangerousHtml: {id: 'remove-buggy-html-in-markdown-parser'},\n  escapeHtml: {id: 'remove-buggy-html-in-markdown-parser'},\n  source: {to: 'children', id: 'change-source-to-children'},\n  allowNode: {\n    to: 'allowElement',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes'\n  },\n  allowedTypes: {\n    to: 'allowedElements',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes'\n  },\n  disallowedTypes: {\n    to: 'disallowedElements',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes'\n  },\n  includeNodeIndex: {\n    to: 'includeElementIndex',\n    id: 'change-includenodeindex-to-includeelementindex'\n  }\n}\n\n/**\n * React component to render markdown.\n *\n * @param {ReactMarkdownOptions} options\n * @returns {ReactElement}\n */\nexport function ReactMarkdown(options) {\n  for (const key in deprecated) {\n    if (own.call(deprecated, key) && own.call(options, key)) {\n      const deprecation = deprecated[key]\n      console.warn(\n        `[react-markdown] Warning: please ${\n          deprecation.to ? `use \\`${deprecation.to}\\` instead of` : 'remove'\n        } \\`${key}\\` (see <${changelog}#${deprecation.id}> for more info)`\n      )\n      delete deprecated[key]\n    }\n  }\n\n  const processor = unified()\n    .use(remarkParse)\n    .use(options.remarkPlugins || [])\n    .use(remarkRehype, {\n      ...options.remarkRehypeOptions,\n      allowDangerousHtml: true\n    })\n    .use(options.rehypePlugins || [])\n    .use(rehypeFilter, options)\n\n  const file = new VFile()\n\n  if (typeof options.children === 'string') {\n    file.value = options.children\n  } else if (options.children !== undefined && options.children !== null) {\n    console.warn(\n      `[react-markdown] Warning: please pass a string as \\`children\\` (not: \\`${options.children}\\`)`\n    )\n  }\n\n  const hastNode = processor.runSync(processor.parse(file), file)\n\n  if (hastNode.type !== 'root') {\n    throw new TypeError('Expected a `root` node')\n  }\n\n  /** @type {ReactElement} */\n  let result = React.createElement(\n    React.Fragment,\n    {},\n    childrenToReact({options, schema: html, listDepth: 0}, hastNode)\n  )\n\n  if (options.className) {\n    result = React.createElement('div', {className: options.className}, result)\n  }\n\n  return result\n}\n\nReactMarkdown.propTypes = {\n  // Core options:\n  children: PropTypes.string,\n  // Layout options:\n  className: PropTypes.string,\n  // Filter options:\n  allowElement: PropTypes.func,\n  allowedElements: PropTypes.arrayOf(PropTypes.string),\n  disallowedElements: PropTypes.arrayOf(PropTypes.string),\n  unwrapDisallowed: PropTypes.bool,\n  // Plugin options:\n  remarkPlugins: PropTypes.arrayOf(\n    PropTypes.oneOfType([\n      PropTypes.object,\n      PropTypes.func,\n      PropTypes.arrayOf(\n        PropTypes.oneOfType([\n          PropTypes.bool,\n          PropTypes.string,\n          PropTypes.object,\n          PropTypes.func,\n          PropTypes.arrayOf(\n            // prettier-ignore\n            // type-coverage:ignore-next-line\n            PropTypes.any\n          )\n        ])\n      )\n    ])\n  ),\n  rehypePlugins: PropTypes.arrayOf(\n    PropTypes.oneOfType([\n      PropTypes.object,\n      PropTypes.func,\n      PropTypes.arrayOf(\n        PropTypes.oneOfType([\n          PropTypes.bool,\n          PropTypes.string,\n          PropTypes.object,\n          PropTypes.func,\n          PropTypes.arrayOf(\n            // prettier-ignore\n            // type-coverage:ignore-next-line\n            PropTypes.any\n          )\n        ])\n      )\n    ])\n  ),\n  // Transform options:\n  sourcePos: PropTypes.bool,\n  rawSourcePos: PropTypes.bool,\n  skipHtml: PropTypes.bool,\n  includeElementIndex: PropTypes.bool,\n  transformLinkUri: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n  linkTarget: PropTypes.oneOfType([PropTypes.func, PropTypes.string]),\n  transformImageUri: PropTypes.func,\n  components: PropTypes.object\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAAQC,KAAK,QAAO,OAAO;AAC3B,SAAQC,OAAO,QAAO,SAAS;AAC/B,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAOC,YAAY,MAAM,eAAe;AACxC,OAAOC,SAAS,MAAM,YAAY;AAClC,SAAQC,IAAI,QAAO,sBAAsB;AACzC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,SAAQC,eAAe,QAAO,mBAAmB;AAEjD,MAAMC,GAAG,GAAG,CAAC,CAAC,CAACC,cAAc;AAC7B,MAAMC,SAAS,GACb,mEAAmE;;AAErE;AACA,MAAMC,UAAU,GAAG;EACjBC,OAAO,EAAE;IAACC,EAAE,EAAE,eAAe;IAAEC,EAAE,EAAE;EAAiC,CAAC;EACrEC,SAAS,EAAE;IAACF,EAAE,EAAE,YAAY;IAAEC,EAAE,EAAE;EAAgC,CAAC;EACnEE,UAAU,EAAE;IAACF,EAAE,EAAE;EAAsC,CAAC;EACxDG,kBAAkB,EAAE;IAACH,EAAE,EAAE;EAAsC,CAAC;EAChEI,UAAU,EAAE;IAACJ,EAAE,EAAE;EAAsC,CAAC;EACxDK,MAAM,EAAE;IAACN,EAAE,EAAE,UAAU;IAAEC,EAAE,EAAE;EAA2B,CAAC;EACzDM,SAAS,EAAE;IACTP,EAAE,EAAE,cAAc;IAClBC,EAAE,EAAE;EACN,CAAC;EACDO,YAAY,EAAE;IACZR,EAAE,EAAE,iBAAiB;IACrBC,EAAE,EAAE;EACN,CAAC;EACDQ,eAAe,EAAE;IACfT,EAAE,EAAE,oBAAoB;IACxBC,EAAE,EAAE;EACN,CAAC;EACDS,gBAAgB,EAAE;IAChBV,EAAE,EAAE,qBAAqB;IACzBC,EAAE,EAAE;EACN;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASU,aAAaA,CAACC,OAAO,EAAE;EACrC,KAAK,MAAMC,GAAG,IAAIf,UAAU,EAAE;IAC5B,IAAIH,GAAG,CAACmB,IAAI,CAAChB,UAAU,EAAEe,GAAG,CAAC,IAAIlB,GAAG,CAACmB,IAAI,CAACF,OAAO,EAAEC,GAAG,CAAC,EAAE;MACvD,MAAME,WAAW,GAAGjB,UAAU,CAACe,GAAG,CAAC;MACnCG,OAAO,CAACC,IAAI,CACV,oCACEF,WAAW,CAACf,EAAE,GAAG,SAASe,WAAW,CAACf,EAAE,eAAe,GAAG,QAAQ,MAC9Da,GAAG,YAAYhB,SAAS,IAAIkB,WAAW,CAACd,EAAE,kBAClD,CAAC;MACD,OAAOH,UAAU,CAACe,GAAG,CAAC;IACxB;EACF;EAEA,MAAMK,SAAS,GAAG9B,OAAO,CAAC,CAAC,CACxB+B,GAAG,CAAC9B,WAAW,CAAC,CAChB8B,GAAG,CAACP,OAAO,CAACQ,aAAa,IAAI,EAAE,CAAC,CAChCD,GAAG,CAAC7B,YAAY,EAAE;IACjB,GAAGsB,OAAO,CAACS,mBAAmB;IAC9BjB,kBAAkB,EAAE;EACtB,CAAC,CAAC,CACDe,GAAG,CAACP,OAAO,CAACU,aAAa,IAAI,EAAE,CAAC,CAChCH,GAAG,CAAC1B,YAAY,EAAEmB,OAAO,CAAC;EAE7B,MAAMW,IAAI,GAAG,IAAIpC,KAAK,CAAC,CAAC;EAExB,IAAI,OAAOyB,OAAO,CAACY,QAAQ,KAAK,QAAQ,EAAE;IACxCD,IAAI,CAACE,KAAK,GAAGb,OAAO,CAACY,QAAQ;EAC/B,CAAC,MAAM,IAAIZ,OAAO,CAACY,QAAQ,KAAKE,SAAS,IAAId,OAAO,CAACY,QAAQ,KAAK,IAAI,EAAE;IACtER,OAAO,CAACC,IAAI,CACV,0EAA0EL,OAAO,CAACY,QAAQ,KAC5F,CAAC;EACH;EAEA,MAAMG,QAAQ,GAAGT,SAAS,CAACU,OAAO,CAACV,SAAS,CAACW,KAAK,CAACN,IAAI,CAAC,EAAEA,IAAI,CAAC;EAE/D,IAAII,QAAQ,CAACG,IAAI,KAAK,MAAM,EAAE;IAC5B,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAC/C;;EAEA;EACA,IAAIC,MAAM,GAAG9C,KAAK,CAAC+C,aAAa,CAC9B/C,KAAK,CAACgD,QAAQ,EACd,CAAC,CAAC,EACFxC,eAAe,CAAC;IAACkB,OAAO;IAAEuB,MAAM,EAAE3C,IAAI;IAAE4C,SAAS,EAAE;EAAC,CAAC,EAAET,QAAQ,CACjE,CAAC;EAED,IAAIf,OAAO,CAACyB,SAAS,EAAE;IACrBL,MAAM,GAAG9C,KAAK,CAAC+C,aAAa,CAAC,KAAK,EAAE;MAACI,SAAS,EAAEzB,OAAO,CAACyB;IAAS,CAAC,EAAEL,MAAM,CAAC;EAC7E;EAEA,OAAOA,MAAM;AACf;AAEArB,aAAa,CAAC2B,SAAS,GAAG;EACxB;EACAd,QAAQ,EAAEjC,SAAS,CAACgD,MAAM;EAC1B;EACAF,SAAS,EAAE9C,SAAS,CAACgD,MAAM;EAC3B;EACAC,YAAY,EAAEjD,SAAS,CAACkD,IAAI;EAC5BC,eAAe,EAAEnD,SAAS,CAACoD,OAAO,CAACpD,SAAS,CAACgD,MAAM,CAAC;EACpDK,kBAAkB,EAAErD,SAAS,CAACoD,OAAO,CAACpD,SAAS,CAACgD,MAAM,CAAC;EACvDM,gBAAgB,EAAEtD,SAAS,CAACuD,IAAI;EAChC;EACA1B,aAAa,EAAE7B,SAAS,CAACoD,OAAO,CAC9BpD,SAAS,CAACwD,SAAS,CAAC,CAClBxD,SAAS,CAACyD,MAAM,EAChBzD,SAAS,CAACkD,IAAI,EACdlD,SAAS,CAACoD,OAAO,CACfpD,SAAS,CAACwD,SAAS,CAAC,CAClBxD,SAAS,CAACuD,IAAI,EACdvD,SAAS,CAACgD,MAAM,EAChBhD,SAAS,CAACyD,MAAM,EAChBzD,SAAS,CAACkD,IAAI,EACdlD,SAAS,CAACoD,OAAO;EACf;EACA;EACApD,SAAS,CAAC0D,GACZ,CAAC,CACF,CACH,CAAC,CACF,CACH,CAAC;EACD3B,aAAa,EAAE/B,SAAS,CAACoD,OAAO,CAC9BpD,SAAS,CAACwD,SAAS,CAAC,CAClBxD,SAAS,CAACyD,MAAM,EAChBzD,SAAS,CAACkD,IAAI,EACdlD,SAAS,CAACoD,OAAO,CACfpD,SAAS,CAACwD,SAAS,CAAC,CAClBxD,SAAS,CAACuD,IAAI,EACdvD,SAAS,CAACgD,MAAM,EAChBhD,SAAS,CAACyD,MAAM,EAChBzD,SAAS,CAACkD,IAAI,EACdlD,SAAS,CAACoD,OAAO;EACf;EACA;EACApD,SAAS,CAAC0D,GACZ,CAAC,CACF,CACH,CAAC,CACF,CACH,CAAC;EACD;EACAC,SAAS,EAAE3D,SAAS,CAACuD,IAAI;EACzBK,YAAY,EAAE5D,SAAS,CAACuD,IAAI;EAC5BM,QAAQ,EAAE7D,SAAS,CAACuD,IAAI;EACxBO,mBAAmB,EAAE9D,SAAS,CAACuD,IAAI;EACnCQ,gBAAgB,EAAE/D,SAAS,CAACwD,SAAS,CAAC,CAACxD,SAAS,CAACkD,IAAI,EAAElD,SAAS,CAACuD,IAAI,CAAC,CAAC;EACvES,UAAU,EAAEhE,SAAS,CAACwD,SAAS,CAAC,CAACxD,SAAS,CAACkD,IAAI,EAAElD,SAAS,CAACgD,MAAM,CAAC,CAAC;EACnEiB,iBAAiB,EAAEjE,SAAS,CAACkD,IAAI;EACjCgB,UAAU,EAAElE,SAAS,CAACyD;AACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}