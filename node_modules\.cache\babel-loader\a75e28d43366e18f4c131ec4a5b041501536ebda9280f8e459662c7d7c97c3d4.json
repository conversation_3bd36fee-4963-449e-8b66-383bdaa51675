{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').HTML} Html\n * @typedef {import('../state.js').State} State\n * @typedef {import('../../index.js').Raw} Raw\n */\n\n/**\n * Turn an mdast `html` node into hast (`raw` node in dangerous mode, otherwise\n * nothing).\n *\n * @param {State} state\n *   Info passed around.\n * @param {Html} node\n *   mdast node.\n * @returns {Raw | Element | null}\n *   hast node.\n */\nexport function html(state, node) {\n  if (state.dangerous) {\n    /** @type {Raw} */\n    const result = {\n      type: 'raw',\n      value: node.value\n    };\n    state.patch(node, result);\n    return state.applyData(node, result);\n  }\n\n  // To do: next major: return `undefined`.\n  return null;\n}", "map": {"version": 3, "names": ["html", "state", "node", "dangerous", "result", "type", "value", "patch", "applyData"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/mdast-util-to-hast/lib/handlers/html.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').HTML} Html\n * @typedef {import('../state.js').State} State\n * @typedef {import('../../index.js').Raw} Raw\n */\n\n/**\n * Turn an mdast `html` node into hast (`raw` node in dangerous mode, otherwise\n * nothing).\n *\n * @param {State} state\n *   Info passed around.\n * @param {Html} node\n *   mdast node.\n * @returns {Raw | Element | null}\n *   hast node.\n */\nexport function html(state, node) {\n  if (state.dangerous) {\n    /** @type {Raw} */\n    const result = {type: 'raw', value: node.value}\n    state.patch(node, result)\n    return state.applyData(node, result)\n  }\n\n  // To do: next major: return `undefined`.\n  return null\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,IAAIA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAChC,IAAID,KAAK,CAACE,SAAS,EAAE;IACnB;IACA,MAAMC,MAAM,GAAG;MAACC,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAEJ,IAAI,CAACI;IAAK,CAAC;IAC/CL,KAAK,CAACM,KAAK,CAACL,IAAI,EAAEE,MAAM,CAAC;IACzB,OAAOH,KAAK,CAACO,SAAS,CAACN,IAAI,EAAEE,MAAM,CAAC;EACtC;;EAEA;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}