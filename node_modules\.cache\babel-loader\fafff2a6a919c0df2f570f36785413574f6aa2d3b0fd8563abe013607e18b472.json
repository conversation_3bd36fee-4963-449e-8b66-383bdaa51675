{"ast": null, "code": "/**\n * @typedef {import('./lib/react-markdown.js').ReactMarkdownOptions} Options\n * @typedef {import('./lib/ast-to-react.js').Components} Components\n */\n\nexport { uriTransformer } from './lib/uri-transformer.js';\nexport { ReactMarkdown as default } from './lib/react-markdown.js';", "map": {"version": 3, "names": ["uriTransformer", "ReactMarkdown", "default"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/react-markdown/index.js"], "sourcesContent": ["/**\n * @typedef {import('./lib/react-markdown.js').ReactMarkdownOptions} Options\n * @typedef {import('./lib/ast-to-react.js').Components} Components\n */\n\nexport {uriTransformer} from './lib/uri-transformer.js'\n\nexport {ReactMarkdown as default} from './lib/react-markdown.js'\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAAQA,cAAc,QAAO,0BAA0B;AAEvD,SAAQC,aAAa,IAAIC,OAAO,QAAO,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}