{"ast": null, "code": "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @typedef {Record<string, unknown>} Props\n * @typedef {null | undefined | string | Props | TestFunctionAnything | Array<string | Props | TestFunctionAnything>} Test\n *   Check for an arbitrary node, unaware of TypeScript inferral.\n *\n * @callback TestFunctionAnything\n *   Check if a node passes a test, unaware of TypeScript inferral.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | void}\n *   Whether this node passes the test.\n */\n\n/**\n * @template {Node} Kind\n *   Node type.\n * @typedef {Kind['type'] | Partial<Kind> | TestFunctionPredicate<Kind> | Array<Kind['type'] | Partial<Kind> | TestFunctionPredicate<Kind>>} PredicateTest\n *   Check for a node that can be inferred by TypeScript.\n */\n\n/**\n * Check if a node passes a certain test.\n *\n * @template {Node} Kind\n *   Node type.\n * @callback TestFunctionPredicate\n *   Complex test function for a node that can be inferred by TypeScript.\n * @param {Node} node\n *   A node.\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is Kind}\n *   Whether this node passes the test.\n */\n\n/**\n * @callback AssertAnything\n *   Check that an arbitrary value is a node, unaware of TypeScript inferral.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n */\n\n/**\n * Check if a node is a node and passes a certain node test.\n *\n * @template {Node} Kind\n *   Node type.\n * @callback AssertPredicate\n *   Check that an arbitrary value is a specific node, aware of TypeScript.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is Kind}\n *   Whether this is a node and passes a test.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param node\n *   Thing to check, typically `Node`.\n * @param test\n *   A check for a specific node.\n * @param index\n *   The node’s position in its parent.\n * @param parent\n *   The node’s parent.\n * @returns\n *   Whether `node` is a node and passes a test.\n */\nexport const is =\n/**\n * @type {(\n *   (() => false) &\n *   (<Kind extends Node = Node>(node: unknown, test: PredicateTest<Kind>, index: number, parent: Parent, context?: unknown) => node is Kind) &\n *   (<Kind extends Node = Node>(node: unknown, test: PredicateTest<Kind>, index?: null | undefined, parent?: null | undefined, context?: unknown) => node is Kind) &\n *   ((node: unknown, test: Test, index: number, parent: Parent, context?: unknown) => boolean) &\n *   ((node: unknown, test?: Test, index?: null | undefined, parent?: null | undefined, context?: unknown) => boolean)\n * )}\n */\n\n/**\n * @param {unknown} [node]\n * @param {Test} [test]\n * @param {number | null | undefined} [index]\n * @param {Parent | null | undefined} [parent]\n * @param {unknown} [context]\n * @returns {boolean}\n */\n// eslint-disable-next-line max-params\nfunction is(node, test, index, parent, context) {\n  const check = convert(test);\n  if (index !== undefined && index !== null && (typeof index !== 'number' || index < 0 || index === Number.POSITIVE_INFINITY)) {\n    throw new Error('Expected positive finite index');\n  }\n  if (parent !== undefined && parent !== null && (!is(parent) || !parent.children)) {\n    throw new Error('Expected parent node');\n  }\n  if ((parent === undefined || parent === null) !== (index === undefined || index === null)) {\n    throw new Error('Expected both parent and index');\n  }\n\n  // @ts-expect-error Looks like a node.\n  return node && node.type && typeof node.type === 'string' ? Boolean(check.call(context, node, index, parent)) : false;\n};\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns\n *   An assertion.\n */\nexport const convert =\n/**\n * @type {(\n *   (<Kind extends Node>(test: PredicateTest<Kind>) => AssertPredicate<Kind>) &\n *   ((test?: Test) => AssertAnything)\n * )}\n */\n\n/**\n * @param {Test} [test]\n * @returns {AssertAnything}\n */\nfunction (test) {\n  if (test === undefined || test === null) {\n    return ok;\n  }\n  if (typeof test === 'string') {\n    return typeFactory(test);\n  }\n  if (typeof test === 'object') {\n    return Array.isArray(test) ? anyFactory(test) : propsFactory(test);\n  }\n  if (typeof test === 'function') {\n    return castFactory(test);\n  }\n  throw new Error('Expected function, string, or object as test');\n};\n\n/**\n * @param {Array<string | Props | TestFunctionAnything>} tests\n * @returns {AssertAnything}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<AssertAnything>} */\n  const checks = [];\n  let index = -1;\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index]);\n  }\n  return castFactory(any);\n\n  /**\n   * @this {unknown}\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function any(...parameters) {\n    let index = -1;\n    while (++index < checks.length) {\n      if (checks[index].call(this, ...parameters)) return true;\n    }\n    return false;\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {AssertAnything}\n */\nfunction propsFactory(check) {\n  return castFactory(all);\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    /** @type {string} */\n    let key;\n    for (key in check) {\n      // @ts-expect-error: hush, it sure works as an index.\n      if (node[key] !== check[key]) return false;\n    }\n    return true;\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {AssertAnything}\n */\nfunction typeFactory(check) {\n  return castFactory(type);\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check;\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunctionAnything} check\n * @returns {AssertAnything}\n */\nfunction castFactory(check) {\n  return assertion;\n\n  /**\n   * @this {unknown}\n   * @param {unknown} node\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function assertion(node, ...parameters) {\n    return Boolean(node && typeof node === 'object' && 'type' in node &&\n    // @ts-expect-error: fine.\n    Boolean(check.call(this, node, ...parameters)));\n  }\n}\nfunction ok() {\n  return true;\n}", "map": {"version": 3, "names": ["is", "node", "test", "index", "parent", "context", "check", "convert", "undefined", "Number", "POSITIVE_INFINITY", "Error", "children", "type", "Boolean", "call", "ok", "typeFactory", "Array", "isArray", "anyFactory", "propsFactory", "castFactory", "tests", "checks", "length", "any", "parameters", "all", "key", "assertion"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/unist-util-is/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @typedef {Record<string, unknown>} Props\n * @typedef {null | undefined | string | Props | TestFunctionAnything | Array<string | Props | TestFunctionAnything>} Test\n *   Check for an arbitrary node, unaware of TypeScript inferral.\n *\n * @callback TestFunctionAnything\n *   Check if a node passes a test, unaware of TypeScript inferral.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | void}\n *   Whether this node passes the test.\n */\n\n/**\n * @template {Node} Kind\n *   Node type.\n * @typedef {Kind['type'] | Partial<Kind> | TestFunctionPredicate<Kind> | Array<Kind['type'] | Partial<Kind> | TestFunctionPredicate<Kind>>} PredicateTest\n *   Check for a node that can be inferred by TypeScript.\n */\n\n/**\n * Check if a node passes a certain test.\n *\n * @template {Node} Kind\n *   Node type.\n * @callback TestFunctionPredicate\n *   Complex test function for a node that can be inferred by TypeScript.\n * @param {Node} node\n *   A node.\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is Kind}\n *   Whether this node passes the test.\n */\n\n/**\n * @callback AssertAnything\n *   Check that an arbitrary value is a node, unaware of TypeScript inferral.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n */\n\n/**\n * Check if a node is a node and passes a certain node test.\n *\n * @template {Node} Kind\n *   Node type.\n * @callback AssertPredicate\n *   Check that an arbitrary value is a specific node, aware of TypeScript.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {node is Kind}\n *   Whether this is a node and passes a test.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param node\n *   Thing to check, typically `Node`.\n * @param test\n *   A check for a specific node.\n * @param index\n *   The node’s position in its parent.\n * @param parent\n *   The node’s parent.\n * @returns\n *   Whether `node` is a node and passes a test.\n */\nexport const is =\n  /**\n   * @type {(\n   *   (() => false) &\n   *   (<Kind extends Node = Node>(node: unknown, test: PredicateTest<Kind>, index: number, parent: Parent, context?: unknown) => node is Kind) &\n   *   (<Kind extends Node = Node>(node: unknown, test: PredicateTest<Kind>, index?: null | undefined, parent?: null | undefined, context?: unknown) => node is Kind) &\n   *   ((node: unknown, test: Test, index: number, parent: Parent, context?: unknown) => boolean) &\n   *   ((node: unknown, test?: Test, index?: null | undefined, parent?: null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function is(node, test, index, parent, context) {\n      const check = convert(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!is(parent) || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      // @ts-expect-error Looks like a node.\n      return node && node.type && typeof node.type === 'string'\n        ? Boolean(check.call(context, node, index, parent))\n        : false\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns\n *   An assertion.\n */\nexport const convert =\n  /**\n   * @type {(\n   *   (<Kind extends Node>(test: PredicateTest<Kind>) => AssertPredicate<Kind>) &\n   *   ((test?: Test) => AssertAnything)\n   * )}\n   */\n  (\n    /**\n     * @param {Test} [test]\n     * @returns {AssertAnything}\n     */\n    function (test) {\n      if (test === undefined || test === null) {\n        return ok\n      }\n\n      if (typeof test === 'string') {\n        return typeFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return Array.isArray(test) ? anyFactory(test) : propsFactory(test)\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      throw new Error('Expected function, string, or object as test')\n    }\n  )\n\n/**\n * @param {Array<string | Props | TestFunctionAnything>} tests\n * @returns {AssertAnything}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<AssertAnything>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].call(this, ...parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {AssertAnything}\n */\nfunction propsFactory(check) {\n  return castFactory(all)\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    /** @type {string} */\n    let key\n\n    for (key in check) {\n      // @ts-expect-error: hush, it sure works as an index.\n      if (node[key] !== check[key]) return false\n    }\n\n    return true\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {AssertAnything}\n */\nfunction typeFactory(check) {\n  return castFactory(type)\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunctionAnything} check\n * @returns {AssertAnything}\n */\nfunction castFactory(check) {\n  return assertion\n\n  /**\n   * @this {unknown}\n   * @param {unknown} node\n   * @param {Array<unknown>} parameters\n   * @returns {boolean}\n   */\n  function assertion(node, ...parameters) {\n    return Boolean(\n      node &&\n        typeof node === 'object' &&\n        'type' in node &&\n        // @ts-expect-error: fine.\n        Boolean(check.call(this, node, ...parameters))\n    )\n  }\n}\n\nfunction ok() {\n  return true\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,EAAE;AACb;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACI;AACA,SAASA,EAAEA,CAACC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC9C,MAAMC,KAAK,GAAGC,OAAO,CAACL,IAAI,CAAC;EAE3B,IACEC,KAAK,KAAKK,SAAS,IACnBL,KAAK,KAAK,IAAI,KACb,OAAOA,KAAK,KAAK,QAAQ,IACxBA,KAAK,GAAG,CAAC,IACTA,KAAK,KAAKM,MAAM,CAACC,iBAAiB,CAAC,EACrC;IACA,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC;EACnD;EAEA,IACEP,MAAM,KAAKI,SAAS,IACpBJ,MAAM,KAAK,IAAI,KACd,CAACJ,EAAE,CAACI,MAAM,CAAC,IAAI,CAACA,MAAM,CAACQ,QAAQ,CAAC,EACjC;IACA,MAAM,IAAID,KAAK,CAAC,sBAAsB,CAAC;EACzC;EAEA,IACE,CAACP,MAAM,KAAKI,SAAS,IAAIJ,MAAM,KAAK,IAAI,OACvCD,KAAK,KAAKK,SAAS,IAAIL,KAAK,KAAK,IAAI,CAAC,EACvC;IACA,MAAM,IAAIQ,KAAK,CAAC,gCAAgC,CAAC;EACnD;;EAEA;EACA,OAAOV,IAAI,IAAIA,IAAI,CAACY,IAAI,IAAI,OAAOZ,IAAI,CAACY,IAAI,KAAK,QAAQ,GACrDC,OAAO,CAACR,KAAK,CAACS,IAAI,CAACV,OAAO,EAAEJ,IAAI,EAAEE,KAAK,EAAEC,MAAM,CAAC,CAAC,GACjD,KAAK;AACX,CACD;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,OAAO;AAClB;AACF;AACA;AACA;AACA;AACA;;AAEI;AACJ;AACA;AACA;AACI,SAAAA,CAAUL,IAAI,EAAE;EACd,IAAIA,IAAI,KAAKM,SAAS,IAAIN,IAAI,KAAK,IAAI,EAAE;IACvC,OAAOc,EAAE;EACX;EAEA,IAAI,OAAOd,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAOe,WAAW,CAACf,IAAI,CAAC;EAC1B;EAEA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAOgB,KAAK,CAACC,OAAO,CAACjB,IAAI,CAAC,GAAGkB,UAAU,CAAClB,IAAI,CAAC,GAAGmB,YAAY,CAACnB,IAAI,CAAC;EACpE;EAEA,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE;IAC9B,OAAOoB,WAAW,CAACpB,IAAI,CAAC;EAC1B;EAEA,MAAM,IAAIS,KAAK,CAAC,8CAA8C,CAAC;AACjE,CACD;;AAEH;AACA;AACA;AACA;AACA,SAASS,UAAUA,CAACG,KAAK,EAAE;EACzB;EACA,MAAMC,MAAM,GAAG,EAAE;EACjB,IAAIrB,KAAK,GAAG,CAAC,CAAC;EAEd,OAAO,EAAEA,KAAK,GAAGoB,KAAK,CAACE,MAAM,EAAE;IAC7BD,MAAM,CAACrB,KAAK,CAAC,GAAGI,OAAO,CAACgB,KAAK,CAACpB,KAAK,CAAC,CAAC;EACvC;EAEA,OAAOmB,WAAW,CAACI,GAAG,CAAC;;EAEvB;AACF;AACA;AACA;AACA;EACE,SAASA,GAAGA,CAAC,GAAGC,UAAU,EAAE;IAC1B,IAAIxB,KAAK,GAAG,CAAC,CAAC;IAEd,OAAO,EAAEA,KAAK,GAAGqB,MAAM,CAACC,MAAM,EAAE;MAC9B,IAAID,MAAM,CAACrB,KAAK,CAAC,CAACY,IAAI,CAAC,IAAI,EAAE,GAAGY,UAAU,CAAC,EAAE,OAAO,IAAI;IAC1D;IAEA,OAAO,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASN,YAAYA,CAACf,KAAK,EAAE;EAC3B,OAAOgB,WAAW,CAACM,GAAG,CAAC;;EAEvB;AACF;AACA;AACA;EACE,SAASA,GAAGA,CAAC3B,IAAI,EAAE;IACjB;IACA,IAAI4B,GAAG;IAEP,KAAKA,GAAG,IAAIvB,KAAK,EAAE;MACjB;MACA,IAAIL,IAAI,CAAC4B,GAAG,CAAC,KAAKvB,KAAK,CAACuB,GAAG,CAAC,EAAE,OAAO,KAAK;IAC5C;IAEA,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASZ,WAAWA,CAACX,KAAK,EAAE;EAC1B,OAAOgB,WAAW,CAACT,IAAI,CAAC;;EAExB;AACF;AACA;EACE,SAASA,IAAIA,CAACZ,IAAI,EAAE;IAClB,OAAOA,IAAI,IAAIA,IAAI,CAACY,IAAI,KAAKP,KAAK;EACpC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,WAAWA,CAAChB,KAAK,EAAE;EAC1B,OAAOwB,SAAS;;EAEhB;AACF;AACA;AACA;AACA;AACA;EACE,SAASA,SAASA,CAAC7B,IAAI,EAAE,GAAG0B,UAAU,EAAE;IACtC,OAAOb,OAAO,CACZb,IAAI,IACF,OAAOA,IAAI,KAAK,QAAQ,IACxB,MAAM,IAAIA,IAAI;IACd;IACAa,OAAO,CAACR,KAAK,CAACS,IAAI,CAAC,IAAI,EAAEd,IAAI,EAAE,GAAG0B,UAAU,CAAC,CACjD,CAAC;EACH;AACF;AAEA,SAASX,EAAEA,CAAA,EAAG;EACZ,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}