{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\invoice-extracting-agent\\\\src\\\\components\\\\ChatInterface.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaPaperPlane, FaMicrophone, FaStop, FaRobot, FaUser } from 'react-icons/fa';\nimport { useChat } from '../context/ChatContext';\nimport ChatMessage from './ChatMessage';\nimport SuggestedQuestions from './SuggestedQuestions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatInterface = ({\n  conversationId,\n  invoiceData\n}) => {\n  _s();\n  const [inputMessage, setInputMessage] = useState('');\n  const [isListening, setIsListening] = useState(false);\n  const messagesEndRef = useRef(null);\n  const inputRef = useRef(null);\n  const {\n    messages,\n    isLoading,\n    sendMessage,\n    setConversationId,\n    setInvoiceData\n  } = useChat();\n\n  // Update context when props change\n  useEffect(() => {\n    if (conversationId) {\n      setConversationId(conversationId);\n    }\n  }, [conversationId, setConversationId]);\n  useEffect(() => {\n    if (invoiceData) {\n      setInvoiceData(invoiceData);\n    }\n  }, [invoiceData, setInvoiceData]);\n\n  // Auto-scroll to bottom\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [messages]);\n\n  // Focus input on mount\n  useEffect(() => {\n    var _inputRef$current;\n    (_inputRef$current = inputRef.current) === null || _inputRef$current === void 0 ? void 0 : _inputRef$current.focus();\n  }, []);\n  const handleSendMessage = async e => {\n    e.preventDefault();\n    if (!inputMessage.trim() || isLoading) return;\n    const message = inputMessage.trim();\n    setInputMessage('');\n    await sendMessage(message);\n  };\n  const handleSuggestedQuestion = async question => {\n    await sendMessage(question);\n  };\n  const handleVoiceInput = () => {\n    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {\n      alert('Speech recognition is not supported in your browser');\n      return;\n    }\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    const recognition = new SpeechRecognition();\n    recognition.continuous = false;\n    recognition.interimResults = false;\n    recognition.lang = 'en-US';\n    recognition.onstart = () => {\n      setIsListening(true);\n    };\n    recognition.onresult = event => {\n      const transcript = event.results[0][0].transcript;\n      setInputMessage(transcript);\n      setIsListening(false);\n    };\n    recognition.onerror = () => {\n      setIsListening(false);\n    };\n    recognition.onend = () => {\n      setIsListening(false);\n    };\n    if (isListening) {\n      recognition.stop();\n    } else {\n      recognition.start();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-full max-h-[calc(100vh-200px)]\",\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"glass-dark rounded-t-xl p-4 border-b border-dark-700\",\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center\",\n          animate: {\n            boxShadow: [\"0 0 20px rgba(14, 165, 233, 0.3)\", \"0 0 40px rgba(14, 165, 233, 0.6)\", \"0 0 20px rgba(14, 165, 233, 0.3)\"]\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity\n          },\n          children: /*#__PURE__*/_jsxDEV(FaRobot, {\n            className: \"text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-semibold text-white\",\n            children: \"AI Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-400\",\n            children: isLoading ? 'Thinking...' : 'Ready to help'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-4 space-y-4 bg-dark-900/50\",\n      children: [/*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: messages.length === 0 ? /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"text-center py-8\",\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          exit: {\n            opacity: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n            animate: {\n              scale: [1, 1.1, 1],\n              rotate: [0, 5, -5, 0]\n            },\n            transition: {\n              duration: 4,\n              repeat: Infinity\n            },\n            children: /*#__PURE__*/_jsxDEV(FaRobot, {\n              className: \"text-2xl text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold text-white mb-2\",\n            children: \"Welcome! How can I help you today?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-400 max-w-md mx-auto\",\n            children: \"I can help you with your bills, answer questions about market trends, or assist with any general inquiries you might have.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 13\n        }, this) : messages.map(message => /*#__PURE__*/_jsxDEV(ChatMessage, {\n          message: message\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), isLoading && /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"flex items-center space-x-3 p-4\",\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(FaRobot, {\n            className: \"text-white text-sm\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"glass rounded-lg p-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-1\",\n            children: [0, 1, 2].map(i => /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"w-2 h-2 bg-primary-500 rounded-full\",\n              animate: {\n                scale: [1, 1.5, 1],\n                opacity: [0.5, 1, 0.5]\n              },\n              transition: {\n                duration: 1,\n                repeat: Infinity,\n                delay: i * 0.2\n              }\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this), messages.length === 0 && /*#__PURE__*/_jsxDEV(SuggestedQuestions, {\n      onQuestionClick: handleSuggestedQuestion,\n      hasInvoice: !!invoiceData\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"glass-dark rounded-b-xl p-4 border-t border-dark-700\",\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSendMessage,\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            ref: inputRef,\n            type: \"text\",\n            value: inputMessage,\n            onChange: e => setInputMessage(e.target.value),\n            placeholder: \"Ask me anything about your bills or general questions...\",\n            className: \"w-full input-primary pr-12\",\n            disabled: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            type: \"button\",\n            onClick: handleVoiceInput,\n            className: `absolute right-3 top-1/2 transform -translate-y-1/2 p-2 rounded-lg transition-colors ${isListening ? 'text-red-400 bg-red-400/10' : 'text-gray-400 hover:text-white hover:bg-dark-700'}`,\n            whileHover: {\n              scale: 1.1\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            disabled: isLoading,\n            children: isListening ? /*#__PURE__*/_jsxDEV(FaStop, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 30\n            }, this) : /*#__PURE__*/_jsxDEV(FaMicrophone, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          type: \"submit\",\n          disabled: !inputMessage.trim() || isLoading,\n          className: \"btn-primary p-3 disabled:opacity-50 disabled:cursor-not-allowed\",\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          children: /*#__PURE__*/_jsxDEV(FaPaperPlane, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this), isListening && /*#__PURE__*/_jsxDEV(motion.p, {\n        className: \"text-center text-red-400 text-sm mt-2\",\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        children: \"\\uD83C\\uDFA4 Listening... Speak now\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatInterface, \"uheR/gpByQX5KKv4IVKbl29d8s8=\", false, function () {\n  return [useChat];\n});\n_c = ChatInterface;\nexport default ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "AnimatePresence", "FaPaperPlane", "FaMicrophone", "FaStop", "FaRobot", "FaUser", "useChat", "ChatMessage", "SuggestedQuestions", "jsxDEV", "_jsxDEV", "ChatInterface", "conversationId", "invoiceData", "_s", "inputMessage", "setInputMessage", "isListening", "setIsListening", "messagesEndRef", "inputRef", "messages", "isLoading", "sendMessage", "setConversationId", "setInvoiceData", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "_inputRef$current", "focus", "handleSendMessage", "e", "preventDefault", "trim", "message", "handleSuggestedQuestion", "question", "handleVoiceInput", "window", "alert", "SpeechRecognition", "webkitSpeechRecognition", "recognition", "continuous", "interimResults", "lang", "onstart", "on<PERSON>ult", "event", "transcript", "results", "onerror", "onend", "stop", "start", "className", "children", "div", "initial", "opacity", "y", "animate", "boxShadow", "transition", "duration", "repeat", "Infinity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "exit", "scale", "rotate", "map", "id", "i", "delay", "ref", "onQuestionClick", "hasInvoice", "onSubmit", "type", "value", "onChange", "target", "placeholder", "disabled", "button", "onClick", "whileHover", "whileTap", "p", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/src/components/ChatInterface.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaPaperPlane, FaMicrophone, FaStop, FaRobot, FaUser } from 'react-icons/fa';\nimport { useChat } from '../context/ChatContext';\nimport ChatMessage from './ChatMessage';\nimport SuggestedQuestions from './SuggestedQuestions';\n\nconst ChatInterface = ({ conversationId, invoiceData }) => {\n  const [inputMessage, setInputMessage] = useState('');\n  const [isListening, setIsListening] = useState(false);\n  const messagesEndRef = useRef(null);\n  const inputRef = useRef(null);\n  \n  const { \n    messages, \n    isLoading, \n    sendMessage, \n    setConversationId,\n    setInvoiceData \n  } = useChat();\n\n  // Update context when props change\n  useEffect(() => {\n    if (conversationId) {\n      setConversationId(conversationId);\n    }\n  }, [conversationId, setConversationId]);\n\n  useEffect(() => {\n    if (invoiceData) {\n      setInvoiceData(invoiceData);\n    }\n  }, [invoiceData, setInvoiceData]);\n\n  // Auto-scroll to bottom\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  }, [messages]);\n\n  // Focus input on mount\n  useEffect(() => {\n    inputRef.current?.focus();\n  }, []);\n\n  const handleSendMessage = async (e) => {\n    e.preventDefault();\n    if (!inputMessage.trim() || isLoading) return;\n\n    const message = inputMessage.trim();\n    setInputMessage('');\n    await sendMessage(message);\n  };\n\n  const handleSuggestedQuestion = async (question) => {\n    await sendMessage(question);\n  };\n\n  const handleVoiceInput = () => {\n    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {\n      alert('Speech recognition is not supported in your browser');\n      return;\n    }\n\n    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n    const recognition = new SpeechRecognition();\n\n    recognition.continuous = false;\n    recognition.interimResults = false;\n    recognition.lang = 'en-US';\n\n    recognition.onstart = () => {\n      setIsListening(true);\n    };\n\n    recognition.onresult = (event) => {\n      const transcript = event.results[0][0].transcript;\n      setInputMessage(transcript);\n      setIsListening(false);\n    };\n\n    recognition.onerror = () => {\n      setIsListening(false);\n    };\n\n    recognition.onend = () => {\n      setIsListening(false);\n    };\n\n    if (isListening) {\n      recognition.stop();\n    } else {\n      recognition.start();\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col h-full max-h-[calc(100vh-200px)]\">\n      {/* Chat Header */}\n      <motion.div\n        className=\"glass-dark rounded-t-xl p-4 border-b border-dark-700\"\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        <div className=\"flex items-center space-x-3\">\n          <motion.div\n            className=\"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center\"\n            animate={{ \n              boxShadow: [\n                \"0 0 20px rgba(14, 165, 233, 0.3)\",\n                \"0 0 40px rgba(14, 165, 233, 0.6)\",\n                \"0 0 20px rgba(14, 165, 233, 0.3)\"\n              ]\n            }}\n            transition={{ duration: 2, repeat: Infinity }}\n          >\n            <FaRobot className=\"text-white\" />\n          </motion.div>\n          <div>\n            <h3 className=\"font-semibold text-white\">AI Assistant</h3>\n            <p className=\"text-sm text-gray-400\">\n              {isLoading ? 'Thinking...' : 'Ready to help'}\n            </p>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Messages Area */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-4 bg-dark-900/50\">\n        <AnimatePresence>\n          {messages.length === 0 ? (\n            <motion.div\n              className=\"text-center py-8\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              exit={{ opacity: 0 }}\n            >\n              <motion.div\n                className=\"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4\"\n                animate={{ \n                  scale: [1, 1.1, 1],\n                  rotate: [0, 5, -5, 0]\n                }}\n                transition={{ duration: 4, repeat: Infinity }}\n              >\n                <FaRobot className=\"text-2xl text-white\" />\n              </motion.div>\n              <h3 className=\"text-xl font-semibold text-white mb-2\">\n                Welcome! How can I help you today?\n              </h3>\n              <p className=\"text-gray-400 max-w-md mx-auto\">\n                I can help you with your bills, answer questions about market trends, \n                or assist with any general inquiries you might have.\n              </p>\n            </motion.div>\n          ) : (\n            messages.map((message) => (\n              <ChatMessage key={message.id} message={message} />\n            ))\n          )}\n        </AnimatePresence>\n\n        {/* Loading indicator */}\n        {isLoading && (\n          <motion.div\n            className=\"flex items-center space-x-3 p-4\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n          >\n            <div className=\"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center\">\n              <FaRobot className=\"text-white text-sm\" />\n            </div>\n            <div className=\"glass rounded-lg p-3\">\n              <div className=\"flex space-x-1\">\n                {[0, 1, 2].map((i) => (\n                  <motion.div\n                    key={i}\n                    className=\"w-2 h-2 bg-primary-500 rounded-full\"\n                    animate={{\n                      scale: [1, 1.5, 1],\n                      opacity: [0.5, 1, 0.5]\n                    }}\n                    transition={{\n                      duration: 1,\n                      repeat: Infinity,\n                      delay: i * 0.2\n                    }}\n                  />\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Suggested Questions */}\n      {messages.length === 0 && (\n        <SuggestedQuestions \n          onQuestionClick={handleSuggestedQuestion}\n          hasInvoice={!!invoiceData}\n        />\n      )}\n\n      {/* Input Area */}\n      <motion.div\n        className=\"glass-dark rounded-b-xl p-4 border-t border-dark-700\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n      >\n        <form onSubmit={handleSendMessage} className=\"flex items-center space-x-3\">\n          <div className=\"flex-1 relative\">\n            <input\n              ref={inputRef}\n              type=\"text\"\n              value={inputMessage}\n              onChange={(e) => setInputMessage(e.target.value)}\n              placeholder=\"Ask me anything about your bills or general questions...\"\n              className=\"w-full input-primary pr-12\"\n              disabled={isLoading}\n            />\n            \n            {/* Voice input button */}\n            <motion.button\n              type=\"button\"\n              onClick={handleVoiceInput}\n              className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-2 rounded-lg transition-colors ${\n                isListening \n                  ? 'text-red-400 bg-red-400/10' \n                  : 'text-gray-400 hover:text-white hover:bg-dark-700'\n              }`}\n              whileHover={{ scale: 1.1 }}\n              whileTap={{ scale: 0.95 }}\n              disabled={isLoading}\n            >\n              {isListening ? <FaStop /> : <FaMicrophone />}\n            </motion.button>\n          </div>\n\n          <motion.button\n            type=\"submit\"\n            disabled={!inputMessage.trim() || isLoading}\n            className=\"btn-primary p-3 disabled:opacity-50 disabled:cursor-not-allowed\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <FaPaperPlane />\n          </motion.button>\n        </form>\n\n        {/* Voice status */}\n        {isListening && (\n          <motion.p\n            className=\"text-center text-red-400 text-sm mt-2\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n          >\n            🎤 Listening... Speak now\n          </motion.p>\n        )}\n      </motion.div>\n    </div>\n  );\n};\n\nexport default ChatInterface;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,YAAY,EAAEC,YAAY,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,QAAQ,gBAAgB;AACpF,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,kBAAkB,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,aAAa,GAAGA,CAAC;EAAEC,cAAc;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACzD,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMuB,cAAc,GAAGtB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMuB,QAAQ,GAAGvB,MAAM,CAAC,IAAI,CAAC;EAE7B,MAAM;IACJwB,QAAQ;IACRC,SAAS;IACTC,WAAW;IACXC,iBAAiB;IACjBC;EACF,CAAC,GAAGnB,OAAO,CAAC,CAAC;;EAEb;EACAR,SAAS,CAAC,MAAM;IACd,IAAIc,cAAc,EAAE;MAClBY,iBAAiB,CAACZ,cAAc,CAAC;IACnC;EACF,CAAC,EAAE,CAACA,cAAc,EAAEY,iBAAiB,CAAC,CAAC;EAEvC1B,SAAS,CAAC,MAAM;IACd,IAAIe,WAAW,EAAE;MACfY,cAAc,CAACZ,WAAW,CAAC;IAC7B;EACF,CAAC,EAAE,CAACA,WAAW,EAAEY,cAAc,CAAC,CAAC;;EAEjC;EACA3B,SAAS,CAAC,MAAM;IAAA,IAAA4B,qBAAA;IACd,CAAAA,qBAAA,GAAAP,cAAc,CAACQ,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAACR,QAAQ,CAAC,CAAC;;EAEd;EACAvB,SAAS,CAAC,MAAM;IAAA,IAAAgC,iBAAA;IACd,CAAAA,iBAAA,GAAAV,QAAQ,CAACO,OAAO,cAAAG,iBAAA,uBAAhBA,iBAAA,CAAkBC,KAAK,CAAC,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,iBAAiB,GAAG,MAAOC,CAAC,IAAK;IACrCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACnB,YAAY,CAACoB,IAAI,CAAC,CAAC,IAAIb,SAAS,EAAE;IAEvC,MAAMc,OAAO,GAAGrB,YAAY,CAACoB,IAAI,CAAC,CAAC;IACnCnB,eAAe,CAAC,EAAE,CAAC;IACnB,MAAMO,WAAW,CAACa,OAAO,CAAC;EAC5B,CAAC;EAED,MAAMC,uBAAuB,GAAG,MAAOC,QAAQ,IAAK;IAClD,MAAMf,WAAW,CAACe,QAAQ,CAAC;EAC7B,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAAE,yBAAyB,IAAIC,MAAM,CAAC,IAAI,EAAE,mBAAmB,IAAIA,MAAM,CAAC,EAAE;MAC9EC,KAAK,CAAC,qDAAqD,CAAC;MAC5D;IACF;IAEA,MAAMC,iBAAiB,GAAGF,MAAM,CAACE,iBAAiB,IAAIF,MAAM,CAACG,uBAAuB;IACpF,MAAMC,WAAW,GAAG,IAAIF,iBAAiB,CAAC,CAAC;IAE3CE,WAAW,CAACC,UAAU,GAAG,KAAK;IAC9BD,WAAW,CAACE,cAAc,GAAG,KAAK;IAClCF,WAAW,CAACG,IAAI,GAAG,OAAO;IAE1BH,WAAW,CAACI,OAAO,GAAG,MAAM;MAC1B9B,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC;IAED0B,WAAW,CAACK,QAAQ,GAAIC,KAAK,IAAK;MAChC,MAAMC,UAAU,GAAGD,KAAK,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACD,UAAU;MACjDnC,eAAe,CAACmC,UAAU,CAAC;MAC3BjC,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC;IAED0B,WAAW,CAACS,OAAO,GAAG,MAAM;MAC1BnC,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC;IAED0B,WAAW,CAACU,KAAK,GAAG,MAAM;MACxBpC,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC;IAED,IAAID,WAAW,EAAE;MACf2B,WAAW,CAACW,IAAI,CAAC,CAAC;IACpB,CAAC,MAAM;MACLX,WAAW,CAACY,KAAK,CAAC,CAAC;IACrB;EACF,CAAC;EAED,oBACE9C,OAAA;IAAK+C,SAAS,EAAC,gDAAgD;IAAAC,QAAA,gBAE7DhD,OAAA,CAACX,MAAM,CAAC4D,GAAG;MACTF,SAAS,EAAC,sDAAsD;MAChEG,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAE9BhD,OAAA;QAAK+C,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1ChD,OAAA,CAACX,MAAM,CAAC4D,GAAG;UACTF,SAAS,EAAC,2GAA2G;UACrHM,OAAO,EAAE;YACPC,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;UAEtC,CAAE;UACFC,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC;UAAS,CAAE;UAAAV,QAAA,eAE9ChD,OAAA,CAACN,OAAO;YAACqD,SAAS,EAAC;UAAY;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACb9D,OAAA;UAAAgD,QAAA,gBACEhD,OAAA;YAAI+C,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EAAC;UAAY;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1D9D,OAAA;YAAG+C,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EACjCpC,SAAS,GAAG,aAAa,GAAG;UAAe;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGb9D,OAAA;MAAK+C,SAAS,EAAC,qDAAqD;MAAAC,QAAA,gBAClEhD,OAAA,CAACV,eAAe;QAAA0D,QAAA,EACbrC,QAAQ,CAACoD,MAAM,KAAK,CAAC,gBACpB/D,OAAA,CAACX,MAAM,CAAC4D,GAAG;UACTF,SAAS,EAAC,kBAAkB;UAC5BG,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBE,OAAO,EAAE;YAAEF,OAAO,EAAE;UAAE,CAAE;UACxBa,IAAI,EAAE;YAAEb,OAAO,EAAE;UAAE,CAAE;UAAAH,QAAA,gBAErBhD,OAAA,CAACX,MAAM,CAAC4D,GAAG;YACTF,SAAS,EAAC,wHAAwH;YAClIM,OAAO,EAAE;cACPY,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;cAClBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;YACtB,CAAE;YACFX,UAAU,EAAE;cAAEC,QAAQ,EAAE,CAAC;cAAEC,MAAM,EAAEC;YAAS,CAAE;YAAAV,QAAA,eAE9ChD,OAAA,CAACN,OAAO;cAACqD,SAAS,EAAC;YAAqB;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACb9D,OAAA;YAAI+C,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAEtD;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL9D,OAAA;YAAG+C,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAG9C;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,GAEbnD,QAAQ,CAACwD,GAAG,CAAEzC,OAAO,iBACnB1B,OAAA,CAACH,WAAW;UAAkB6B,OAAO,EAAEA;QAAQ,GAA7BA,OAAO,CAAC0C,EAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAqB,CAClD;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACc,CAAC,EAGjBlD,SAAS,iBACRZ,OAAA,CAACX,MAAM,CAAC4D,GAAG;QACTF,SAAS,EAAC,iCAAiC;QAC3CG,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBAE9BhD,OAAA;UAAK+C,SAAS,EAAC,yGAAyG;UAAAC,QAAA,eACtHhD,OAAA,CAACN,OAAO;YAACqD,SAAS,EAAC;UAAoB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACN9D,OAAA;UAAK+C,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eACnChD,OAAA;YAAK+C,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5B,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACmB,GAAG,CAAEE,CAAC,iBACfrE,OAAA,CAACX,MAAM,CAAC4D,GAAG;cAETF,SAAS,EAAC,qCAAqC;cAC/CM,OAAO,EAAE;gBACPY,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;gBAClBd,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG;cACvB,CAAE;cACFI,UAAU,EAAE;gBACVC,QAAQ,EAAE,CAAC;gBACXC,MAAM,EAAEC,QAAQ;gBAChBY,KAAK,EAAED,CAAC,GAAG;cACb;YAAE,GAVGA,CAAC;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWP,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,eAED9D,OAAA;QAAKuE,GAAG,EAAE9D;MAAe;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,EAGLnD,QAAQ,CAACoD,MAAM,KAAK,CAAC,iBACpB/D,OAAA,CAACF,kBAAkB;MACjB0E,eAAe,EAAE7C,uBAAwB;MACzC8C,UAAU,EAAE,CAAC,CAACtE;IAAY;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CACF,eAGD9D,OAAA,CAACX,MAAM,CAAC4D,GAAG;MACTF,SAAS,EAAC,sDAAsD;MAChEG,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBAE9BhD,OAAA;QAAM0E,QAAQ,EAAEpD,iBAAkB;QAACyB,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBACxEhD,OAAA;UAAK+C,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BhD,OAAA;YACEuE,GAAG,EAAE7D,QAAS;YACdiE,IAAI,EAAC,MAAM;YACXC,KAAK,EAAEvE,YAAa;YACpBwE,QAAQ,EAAGtD,CAAC,IAAKjB,eAAe,CAACiB,CAAC,CAACuD,MAAM,CAACF,KAAK,CAAE;YACjDG,WAAW,EAAC,0DAA0D;YACtEhC,SAAS,EAAC,4BAA4B;YACtCiC,QAAQ,EAAEpE;UAAU;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eAGF9D,OAAA,CAACX,MAAM,CAAC4F,MAAM;YACZN,IAAI,EAAC,QAAQ;YACbO,OAAO,EAAErD,gBAAiB;YAC1BkB,SAAS,EAAE,wFACTxC,WAAW,GACP,4BAA4B,GAC5B,kDAAkD,EACrD;YACH4E,UAAU,EAAE;cAAElB,KAAK,EAAE;YAAI,CAAE;YAC3BmB,QAAQ,EAAE;cAAEnB,KAAK,EAAE;YAAK,CAAE;YAC1Be,QAAQ,EAAEpE,SAAU;YAAAoC,QAAA,EAEnBzC,WAAW,gBAAGP,OAAA,CAACP,MAAM;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG9D,OAAA,CAACR,YAAY;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eAEN9D,OAAA,CAACX,MAAM,CAAC4F,MAAM;UACZN,IAAI,EAAC,QAAQ;UACbK,QAAQ,EAAE,CAAC3E,YAAY,CAACoB,IAAI,CAAC,CAAC,IAAIb,SAAU;UAC5CmC,SAAS,EAAC,iEAAiE;UAC3EoC,UAAU,EAAE;YAAElB,KAAK,EAAE;UAAK,CAAE;UAC5BmB,QAAQ,EAAE;YAAEnB,KAAK,EAAE;UAAK,CAAE;UAAAjB,QAAA,eAE1BhD,OAAA,CAACT,YAAY;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,EAGNvD,WAAW,iBACVP,OAAA,CAACX,MAAM,CAACgG,CAAC;QACPtC,SAAS,EAAC,uCAAuC;QACjDG,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBE,OAAO,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QAAAH,QAAA,EACzB;MAED;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CACX;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAAC1D,EAAA,CAhQIH,aAAa;EAAA,QAYbL,OAAO;AAAA;AAAA0F,EAAA,GAZPrF,aAAa;AAkQnB,eAAeA,aAAa;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}