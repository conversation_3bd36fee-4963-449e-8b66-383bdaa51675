{"ast": null, "code": "/**\n * @typedef {import('micromark-util-types').Chunk} Chunk\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').ConstructRecord} ConstructRecord\n * @typedef {import('micromark-util-types').Effects} Effects\n * @typedef {import('micromark-util-types').InitialConstruct} InitialConstruct\n * @typedef {import('micromark-util-types').ParseContext} ParseContext\n * @typedef {import('micromark-util-types').Point} Point\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenType} TokenType\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n */\n\n/**\n * @callback Restore\n * @returns {void}\n *\n * @typedef Info\n * @property {Restore} restore\n * @property {number} from\n *\n * @callback ReturnHandle\n *   Handle a successful run.\n * @param {Construct} construct\n * @param {Info} info\n * @returns {void}\n */\n\nimport createDebug from 'debug';\nimport { markdownLineEnding } from 'micromark-util-character';\nimport { push, splice } from 'micromark-util-chunked';\nimport { resolveAll } from 'micromark-util-resolve-all';\nimport { codes } from 'micromark-util-symbol/codes.js';\nimport { values } from 'micromark-util-symbol/values.js';\nimport { ok as assert } from 'uvu/assert';\nconst debug = createDebug('micromark');\n\n/**\n * Create a tokenizer.\n * Tokenizers deal with one type of data (e.g., containers, flow, text).\n * The parser is the object dealing with it all.\n * `initialize` works like other constructs, except that only its `tokenize`\n * function is used, in which case it doesn’t receive an `ok` or `nok`.\n * `from` can be given to set the point before the first character, although\n * when further lines are indented, they must be set with `defineSkip`.\n *\n * @param {ParseContext} parser\n * @param {InitialConstruct} initialize\n * @param {Omit<Point, '_bufferIndex' | '_index'> | undefined} [from]\n * @returns {TokenizeContext}\n */\nexport function createTokenizer(parser, initialize, from) {\n  /** @type {Point} */\n  let point = Object.assign(from ? Object.assign({}, from) : {\n    line: 1,\n    column: 1,\n    offset: 0\n  }, {\n    _index: 0,\n    _bufferIndex: -1\n  });\n  /** @type {Record<string, number>} */\n  const columnStart = {};\n  /** @type {Array<Construct>} */\n  const resolveAllConstructs = [];\n  /** @type {Array<Chunk>} */\n  let chunks = [];\n  /** @type {Array<Token>} */\n  let stack = [];\n  /** @type {boolean | undefined} */\n  let consumed = true;\n\n  /**\n   * Tools used for tokenizing.\n   *\n   * @type {Effects}\n   */\n  const effects = {\n    consume,\n    enter,\n    exit,\n    attempt: constructFactory(onsuccessfulconstruct),\n    check: constructFactory(onsuccessfulcheck),\n    interrupt: constructFactory(onsuccessfulcheck, {\n      interrupt: true\n    })\n  };\n\n  /**\n   * State and tools for resolving and serializing.\n   *\n   * @type {TokenizeContext}\n   */\n  const context = {\n    previous: codes.eof,\n    code: codes.eof,\n    containerState: {},\n    events: [],\n    parser,\n    sliceStream,\n    sliceSerialize,\n    now,\n    defineSkip,\n    write\n  };\n\n  /**\n   * The state function.\n   *\n   * @type {State | void}\n   */\n  let state = initialize.tokenize.call(context, effects);\n\n  /**\n   * Track which character we expect to be consumed, to catch bugs.\n   *\n   * @type {Code}\n   */\n  let expectedCode;\n  if (initialize.resolveAll) {\n    resolveAllConstructs.push(initialize);\n  }\n  return context;\n\n  /** @type {TokenizeContext['write']} */\n  function write(slice) {\n    chunks = push(chunks, slice);\n    main();\n\n    // Exit if we’re not done, resolve might change stuff.\n    if (chunks[chunks.length - 1] !== codes.eof) {\n      return [];\n    }\n    addResult(initialize, 0);\n\n    // Otherwise, resolve, and exit.\n    context.events = resolveAll(resolveAllConstructs, context.events, context);\n    return context.events;\n  }\n\n  //\n  // Tools.\n  //\n\n  /** @type {TokenizeContext['sliceSerialize']} */\n  function sliceSerialize(token, expandTabs) {\n    return serializeChunks(sliceStream(token), expandTabs);\n  }\n\n  /** @type {TokenizeContext['sliceStream']} */\n  function sliceStream(token) {\n    return sliceChunks(chunks, token);\n  }\n\n  /** @type {TokenizeContext['now']} */\n  function now() {\n    // This is a hot path, so we clone manually instead of `Object.assign({}, point)`\n    const {\n      line,\n      column,\n      offset,\n      _index,\n      _bufferIndex\n    } = point;\n    return {\n      line,\n      column,\n      offset,\n      _index,\n      _bufferIndex\n    };\n  }\n\n  /** @type {TokenizeContext['defineSkip']} */\n  function defineSkip(value) {\n    columnStart[value.line] = value.column;\n    accountForPotentialSkip();\n    debug('position: define skip: `%j`', point);\n  }\n\n  //\n  // State management.\n  //\n\n  /**\n   * Main loop (note that `_index` and `_bufferIndex` in `point` are modified by\n   * `consume`).\n   * Here is where we walk through the chunks, which either include strings of\n   * several characters, or numerical character codes.\n   * The reason to do this in a loop instead of a call is so the stack can\n   * drain.\n   *\n   * @returns {void}\n   */\n  function main() {\n    /** @type {number} */\n    let chunkIndex;\n    while (point._index < chunks.length) {\n      const chunk = chunks[point._index];\n\n      // If we’re in a buffer chunk, loop through it.\n      if (typeof chunk === 'string') {\n        chunkIndex = point._index;\n        if (point._bufferIndex < 0) {\n          point._bufferIndex = 0;\n        }\n        while (point._index === chunkIndex && point._bufferIndex < chunk.length) {\n          go(chunk.charCodeAt(point._bufferIndex));\n        }\n      } else {\n        go(chunk);\n      }\n    }\n  }\n\n  /**\n   * Deal with one code.\n   *\n   * @param {Code} code\n   * @returns {void}\n   */\n  function go(code) {\n    assert(consumed === true, 'expected character to be consumed');\n    consumed = undefined;\n    debug('main: passing `%s` to %s', code, state && state.name);\n    expectedCode = code;\n    assert(typeof state === 'function', 'expected state');\n    state = state(code);\n  }\n\n  /** @type {Effects['consume']} */\n  function consume(code) {\n    assert(code === expectedCode, 'expected given code to equal expected code');\n    debug('consume: `%s`', code);\n    assert(consumed === undefined, 'expected code to not have been consumed: this might be because `return x(code)` instead of `return x` was used');\n    assert(code === null ? context.events.length === 0 || context.events[context.events.length - 1][0] === 'exit' : context.events[context.events.length - 1][0] === 'enter', 'expected last token to be open');\n    if (markdownLineEnding(code)) {\n      point.line++;\n      point.column = 1;\n      point.offset += code === codes.carriageReturnLineFeed ? 2 : 1;\n      accountForPotentialSkip();\n      debug('position: after eol: `%j`', point);\n    } else if (code !== codes.virtualSpace) {\n      point.column++;\n      point.offset++;\n    }\n\n    // Not in a string chunk.\n    if (point._bufferIndex < 0) {\n      point._index++;\n    } else {\n      point._bufferIndex++;\n\n      // At end of string chunk.\n      // @ts-expect-error Points w/ non-negative `_bufferIndex` reference\n      // strings.\n      if (point._bufferIndex === chunks[point._index].length) {\n        point._bufferIndex = -1;\n        point._index++;\n      }\n    }\n\n    // Expose the previous character.\n    context.previous = code;\n\n    // Mark as consumed.\n    consumed = true;\n  }\n\n  /** @type {Effects['enter']} */\n  function enter(type, fields) {\n    /** @type {Token} */\n    // @ts-expect-error Patch instead of assign required fields to help GC.\n    const token = fields || {};\n    token.type = type;\n    token.start = now();\n    assert(typeof type === 'string', 'expected string type');\n    assert(type.length > 0, 'expected non-empty string');\n    debug('enter: `%s`', type);\n    context.events.push(['enter', token, context]);\n    stack.push(token);\n    return token;\n  }\n\n  /** @type {Effects['exit']} */\n  function exit(type) {\n    assert(typeof type === 'string', 'expected string type');\n    assert(type.length > 0, 'expected non-empty string');\n    const token = stack.pop();\n    assert(token, 'cannot close w/o open tokens');\n    token.end = now();\n    assert(type === token.type, 'expected exit token to match current token');\n    assert(!(token.start._index === token.end._index && token.start._bufferIndex === token.end._bufferIndex), 'expected non-empty token (`' + type + '`)');\n    debug('exit: `%s`', token.type);\n    context.events.push(['exit', token, context]);\n    return token;\n  }\n\n  /**\n   * Use results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulconstruct(construct, info) {\n    addResult(construct, info.from);\n  }\n\n  /**\n   * Discard results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulcheck(_, info) {\n    info.restore();\n  }\n\n  /**\n   * Factory to attempt/check/interrupt.\n   *\n   * @param {ReturnHandle} onreturn\n   * @param {{interrupt?: boolean | undefined} | undefined} [fields]\n   */\n  function constructFactory(onreturn, fields) {\n    return hook;\n\n    /**\n     * Handle either an object mapping codes to constructs, a list of\n     * constructs, or a single construct.\n     *\n     * @param {Array<Construct> | Construct | ConstructRecord} constructs\n     * @param {State} returnState\n     * @param {State | undefined} [bogusState]\n     * @returns {State}\n     */\n    function hook(constructs, returnState, bogusState) {\n      /** @type {Array<Construct>} */\n      let listOfConstructs;\n      /** @type {number} */\n      let constructIndex;\n      /** @type {Construct} */\n      let currentConstruct;\n      /** @type {Info} */\n      let info;\n      return Array.isArray(constructs) ? /* c8 ignore next 1 */\n      handleListOfConstructs(constructs) : 'tokenize' in constructs ?\n      // @ts-expect-error Looks like a construct.\n      handleListOfConstructs([constructs]) : handleMapOfConstructs(constructs);\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ConstructRecord} map\n       * @returns {State}\n       */\n      function handleMapOfConstructs(map) {\n        return start;\n\n        /** @type {State} */\n        function start(code) {\n          const def = code !== null && map[code];\n          const all = code !== null && map.null;\n          const list = [\n          // To do: add more extension tests.\n          /* c8 ignore next 2 */\n          ...(Array.isArray(def) ? def : def ? [def] : []), ...(Array.isArray(all) ? all : all ? [all] : [])];\n          return handleListOfConstructs(list)(code);\n        }\n      }\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {Array<Construct>} list\n       * @returns {State}\n       */\n      function handleListOfConstructs(list) {\n        listOfConstructs = list;\n        constructIndex = 0;\n        if (list.length === 0) {\n          assert(bogusState, 'expected `bogusState` to be given');\n          return bogusState;\n        }\n        return handleConstruct(list[constructIndex]);\n      }\n\n      /**\n       * Handle a single construct.\n       *\n       * @param {Construct} construct\n       * @returns {State}\n       */\n      function handleConstruct(construct) {\n        return start;\n\n        /** @type {State} */\n        function start(code) {\n          // To do: not needed to store if there is no bogus state, probably?\n          // Currently doesn’t work because `inspect` in document does a check\n          // w/o a bogus, which doesn’t make sense. But it does seem to help perf\n          // by not storing.\n          info = store();\n          currentConstruct = construct;\n          if (!construct.partial) {\n            context.currentConstruct = construct;\n          }\n\n          // Always populated by defaults.\n          assert(context.parser.constructs.disable.null, 'expected `disable.null` to be populated');\n          if (construct.name && context.parser.constructs.disable.null.includes(construct.name)) {\n            return nok(code);\n          }\n          return construct.tokenize.call(\n          // If we do have fields, create an object w/ `context` as its\n          // prototype.\n          // This allows a “live binding”, which is needed for `interrupt`.\n          fields ? Object.assign(Object.create(context), fields) : context, effects, ok, nok)(code);\n        }\n      }\n\n      /** @type {State} */\n      function ok(code) {\n        assert(code === expectedCode, 'expected code');\n        consumed = true;\n        onreturn(currentConstruct, info);\n        return returnState;\n      }\n\n      /** @type {State} */\n      function nok(code) {\n        assert(code === expectedCode, 'expected code');\n        consumed = true;\n        info.restore();\n        if (++constructIndex < listOfConstructs.length) {\n          return handleConstruct(listOfConstructs[constructIndex]);\n        }\n        return bogusState;\n      }\n    }\n  }\n\n  /**\n   * @param {Construct} construct\n   * @param {number} from\n   * @returns {void}\n   */\n  function addResult(construct, from) {\n    if (construct.resolveAll && !resolveAllConstructs.includes(construct)) {\n      resolveAllConstructs.push(construct);\n    }\n    if (construct.resolve) {\n      splice(context.events, from, context.events.length - from, construct.resolve(context.events.slice(from), context));\n    }\n    if (construct.resolveTo) {\n      context.events = construct.resolveTo(context.events, context);\n    }\n    assert(construct.partial || context.events.length === 0 || context.events[context.events.length - 1][0] === 'exit', 'expected last token to end');\n  }\n\n  /**\n   * Store state.\n   *\n   * @returns {Info}\n   */\n  function store() {\n    const startPoint = now();\n    const startPrevious = context.previous;\n    const startCurrentConstruct = context.currentConstruct;\n    const startEventsIndex = context.events.length;\n    const startStack = Array.from(stack);\n    return {\n      restore,\n      from: startEventsIndex\n    };\n\n    /**\n     * Restore state.\n     *\n     * @returns {void}\n     */\n    function restore() {\n      point = startPoint;\n      context.previous = startPrevious;\n      context.currentConstruct = startCurrentConstruct;\n      context.events.length = startEventsIndex;\n      stack = startStack;\n      accountForPotentialSkip();\n      debug('position: restore: `%j`', point);\n    }\n  }\n\n  /**\n   * Move the current point a bit forward in the line when it’s on a column\n   * skip.\n   *\n   * @returns {void}\n   */\n  function accountForPotentialSkip() {\n    if (point.line in columnStart && point.column < 2) {\n      point.column = columnStart[point.line];\n      point.offset += columnStart[point.line] - 1;\n    }\n  }\n}\n\n/**\n * Get the chunks from a slice of chunks in the range of a token.\n *\n * @param {Array<Chunk>} chunks\n * @param {Pick<Token, 'end' | 'start'>} token\n * @returns {Array<Chunk>}\n */\nfunction sliceChunks(chunks, token) {\n  const startIndex = token.start._index;\n  const startBufferIndex = token.start._bufferIndex;\n  const endIndex = token.end._index;\n  const endBufferIndex = token.end._bufferIndex;\n  /** @type {Array<Chunk>} */\n  let view;\n  if (startIndex === endIndex) {\n    assert(endBufferIndex > -1, 'expected non-negative end buffer index');\n    assert(startBufferIndex > -1, 'expected non-negative start buffer index');\n    // @ts-expect-error `_bufferIndex` is used on string chunks.\n    view = [chunks[startIndex].slice(startBufferIndex, endBufferIndex)];\n  } else {\n    view = chunks.slice(startIndex, endIndex);\n    if (startBufferIndex > -1) {\n      const head = view[0];\n      if (typeof head === 'string') {\n        view[0] = head.slice(startBufferIndex);\n      } else {\n        assert(startBufferIndex === 0, 'expected `startBufferIndex` to be `0`');\n        view.shift();\n      }\n    }\n    if (endBufferIndex > 0) {\n      // @ts-expect-error `_bufferIndex` is used on string chunks.\n      view.push(chunks[endIndex].slice(0, endBufferIndex));\n    }\n  }\n  return view;\n}\n\n/**\n * Get the string value of a slice of chunks.\n *\n * @param {Array<Chunk>} chunks\n * @param {boolean | undefined} [expandTabs=false]\n * @returns {string}\n */\nfunction serializeChunks(chunks, expandTabs) {\n  let index = -1;\n  /** @type {Array<string>} */\n  const result = [];\n  /** @type {boolean | undefined} */\n  let atTab;\n  while (++index < chunks.length) {\n    const chunk = chunks[index];\n    /** @type {string} */\n    let value;\n    if (typeof chunk === 'string') {\n      value = chunk;\n    } else switch (chunk) {\n      case codes.carriageReturn:\n        {\n          value = values.cr;\n          break;\n        }\n      case codes.lineFeed:\n        {\n          value = values.lf;\n          break;\n        }\n      case codes.carriageReturnLineFeed:\n        {\n          value = values.cr + values.lf;\n          break;\n        }\n      case codes.horizontalTab:\n        {\n          value = expandTabs ? values.space : values.ht;\n          break;\n        }\n      case codes.virtualSpace:\n        {\n          if (!expandTabs && atTab) continue;\n          value = values.space;\n          break;\n        }\n      default:\n        {\n          assert(typeof chunk === 'number', 'expected number');\n          // Currently only replacement character.\n          value = String.fromCharCode(chunk);\n        }\n    }\n    atTab = chunk === codes.horizontalTab;\n    result.push(value);\n  }\n  return result.join('');\n}", "map": {"version": 3, "names": ["createDebug", "markdownLineEnding", "push", "splice", "resolveAll", "codes", "values", "ok", "assert", "debug", "createTokenizer", "parser", "initialize", "from", "point", "Object", "assign", "line", "column", "offset", "_index", "_bufferIndex", "columnStart", "resolveAllConstructs", "chunks", "stack", "consumed", "effects", "consume", "enter", "exit", "attempt", "constructFactory", "onsuccessfulconstruct", "check", "onsuccessfulcheck", "interrupt", "context", "previous", "eof", "code", "containerState", "events", "sliceStream", "sliceSerialize", "now", "defineSkip", "write", "state", "tokenize", "call", "expectedCode", "slice", "main", "length", "addResult", "token", "expandTabs", "serializeChunks", "sliceChunks", "value", "accountForPotentialSkip", "chunkIndex", "chunk", "go", "charCodeAt", "undefined", "name", "carriageReturnLineFeed", "virtualSpace", "type", "fields", "start", "pop", "end", "construct", "info", "_", "restore", "onreturn", "hook", "constructs", "returnState", "bogusState", "listOfConstructs", "constructIndex", "currentConstruct", "Array", "isArray", "handleListOfConstructs", "handleMapOfConstructs", "map", "def", "all", "null", "list", "handleConstruct", "store", "partial", "disable", "includes", "nok", "create", "resolve", "resolveTo", "startPoint", "startPrevious", "startCurrentConstruct", "startEventsIndex", "startStack", "startIndex", "startBufferIndex", "endIndex", "endBufferIndex", "view", "head", "shift", "index", "result", "atTab", "carriageReturn", "cr", "lineFeed", "lf", "horizontalTab", "space", "ht", "String", "fromCharCode", "join"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/micromark/dev/lib/create-tokenizer.js"], "sourcesContent": ["/**\n * @typedef {import('micromark-util-types').Chunk} Chunk\n * @typedef {import('micromark-util-types').Code} Code\n * @typedef {import('micromark-util-types').Construct} Construct\n * @typedef {import('micromark-util-types').ConstructRecord} ConstructRecord\n * @typedef {import('micromark-util-types').Effects} Effects\n * @typedef {import('micromark-util-types').InitialConstruct} InitialConstruct\n * @typedef {import('micromark-util-types').ParseContext} ParseContext\n * @typedef {import('micromark-util-types').Point} Point\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenType} TokenType\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n */\n\n/**\n * @callback Restore\n * @returns {void}\n *\n * @typedef Info\n * @property {Restore} restore\n * @property {number} from\n *\n * @callback ReturnHandle\n *   Handle a successful run.\n * @param {Construct} construct\n * @param {Info} info\n * @returns {void}\n */\n\nimport createDebug from 'debug'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {push, splice} from 'micromark-util-chunked'\nimport {resolveAll} from 'micromark-util-resolve-all'\nimport {codes} from 'micromark-util-symbol/codes.js'\nimport {values} from 'micromark-util-symbol/values.js'\nimport {ok as assert} from 'uvu/assert'\n\nconst debug = createDebug('micromark')\n\n/**\n * Create a tokenizer.\n * Tokenizers deal with one type of data (e.g., containers, flow, text).\n * The parser is the object dealing with it all.\n * `initialize` works like other constructs, except that only its `tokenize`\n * function is used, in which case it doesn’t receive an `ok` or `nok`.\n * `from` can be given to set the point before the first character, although\n * when further lines are indented, they must be set with `defineSkip`.\n *\n * @param {ParseContext} parser\n * @param {InitialConstruct} initialize\n * @param {Omit<Point, '_bufferIndex' | '_index'> | undefined} [from]\n * @returns {TokenizeContext}\n */\nexport function createTokenizer(parser, initialize, from) {\n  /** @type {Point} */\n  let point = Object.assign(\n    from ? Object.assign({}, from) : {line: 1, column: 1, offset: 0},\n    {_index: 0, _bufferIndex: -1}\n  )\n  /** @type {Record<string, number>} */\n  const columnStart = {}\n  /** @type {Array<Construct>} */\n  const resolveAllConstructs = []\n  /** @type {Array<Chunk>} */\n  let chunks = []\n  /** @type {Array<Token>} */\n  let stack = []\n  /** @type {boolean | undefined} */\n  let consumed = true\n\n  /**\n   * Tools used for tokenizing.\n   *\n   * @type {Effects}\n   */\n  const effects = {\n    consume,\n    enter,\n    exit,\n    attempt: constructFactory(onsuccessfulconstruct),\n    check: constructFactory(onsuccessfulcheck),\n    interrupt: constructFactory(onsuccessfulcheck, {interrupt: true})\n  }\n\n  /**\n   * State and tools for resolving and serializing.\n   *\n   * @type {TokenizeContext}\n   */\n  const context = {\n    previous: codes.eof,\n    code: codes.eof,\n    containerState: {},\n    events: [],\n    parser,\n    sliceStream,\n    sliceSerialize,\n    now,\n    defineSkip,\n    write\n  }\n\n  /**\n   * The state function.\n   *\n   * @type {State | void}\n   */\n  let state = initialize.tokenize.call(context, effects)\n\n  /**\n   * Track which character we expect to be consumed, to catch bugs.\n   *\n   * @type {Code}\n   */\n  let expectedCode\n\n  if (initialize.resolveAll) {\n    resolveAllConstructs.push(initialize)\n  }\n\n  return context\n\n  /** @type {TokenizeContext['write']} */\n  function write(slice) {\n    chunks = push(chunks, slice)\n\n    main()\n\n    // Exit if we’re not done, resolve might change stuff.\n    if (chunks[chunks.length - 1] !== codes.eof) {\n      return []\n    }\n\n    addResult(initialize, 0)\n\n    // Otherwise, resolve, and exit.\n    context.events = resolveAll(resolveAllConstructs, context.events, context)\n\n    return context.events\n  }\n\n  //\n  // Tools.\n  //\n\n  /** @type {TokenizeContext['sliceSerialize']} */\n  function sliceSerialize(token, expandTabs) {\n    return serializeChunks(sliceStream(token), expandTabs)\n  }\n\n  /** @type {TokenizeContext['sliceStream']} */\n  function sliceStream(token) {\n    return sliceChunks(chunks, token)\n  }\n\n  /** @type {TokenizeContext['now']} */\n  function now() {\n    // This is a hot path, so we clone manually instead of `Object.assign({}, point)`\n    const {line, column, offset, _index, _bufferIndex} = point\n    return {line, column, offset, _index, _bufferIndex}\n  }\n\n  /** @type {TokenizeContext['defineSkip']} */\n  function defineSkip(value) {\n    columnStart[value.line] = value.column\n    accountForPotentialSkip()\n    debug('position: define skip: `%j`', point)\n  }\n\n  //\n  // State management.\n  //\n\n  /**\n   * Main loop (note that `_index` and `_bufferIndex` in `point` are modified by\n   * `consume`).\n   * Here is where we walk through the chunks, which either include strings of\n   * several characters, or numerical character codes.\n   * The reason to do this in a loop instead of a call is so the stack can\n   * drain.\n   *\n   * @returns {void}\n   */\n  function main() {\n    /** @type {number} */\n    let chunkIndex\n\n    while (point._index < chunks.length) {\n      const chunk = chunks[point._index]\n\n      // If we’re in a buffer chunk, loop through it.\n      if (typeof chunk === 'string') {\n        chunkIndex = point._index\n\n        if (point._bufferIndex < 0) {\n          point._bufferIndex = 0\n        }\n\n        while (\n          point._index === chunkIndex &&\n          point._bufferIndex < chunk.length\n        ) {\n          go(chunk.charCodeAt(point._bufferIndex))\n        }\n      } else {\n        go(chunk)\n      }\n    }\n  }\n\n  /**\n   * Deal with one code.\n   *\n   * @param {Code} code\n   * @returns {void}\n   */\n  function go(code) {\n    assert(consumed === true, 'expected character to be consumed')\n    consumed = undefined\n    debug('main: passing `%s` to %s', code, state && state.name)\n    expectedCode = code\n    assert(typeof state === 'function', 'expected state')\n    state = state(code)\n  }\n\n  /** @type {Effects['consume']} */\n  function consume(code) {\n    assert(code === expectedCode, 'expected given code to equal expected code')\n\n    debug('consume: `%s`', code)\n\n    assert(\n      consumed === undefined,\n      'expected code to not have been consumed: this might be because `return x(code)` instead of `return x` was used'\n    )\n    assert(\n      code === null\n        ? context.events.length === 0 ||\n            context.events[context.events.length - 1][0] === 'exit'\n        : context.events[context.events.length - 1][0] === 'enter',\n      'expected last token to be open'\n    )\n\n    if (markdownLineEnding(code)) {\n      point.line++\n      point.column = 1\n      point.offset += code === codes.carriageReturnLineFeed ? 2 : 1\n      accountForPotentialSkip()\n      debug('position: after eol: `%j`', point)\n    } else if (code !== codes.virtualSpace) {\n      point.column++\n      point.offset++\n    }\n\n    // Not in a string chunk.\n    if (point._bufferIndex < 0) {\n      point._index++\n    } else {\n      point._bufferIndex++\n\n      // At end of string chunk.\n      // @ts-expect-error Points w/ non-negative `_bufferIndex` reference\n      // strings.\n      if (point._bufferIndex === chunks[point._index].length) {\n        point._bufferIndex = -1\n        point._index++\n      }\n    }\n\n    // Expose the previous character.\n    context.previous = code\n\n    // Mark as consumed.\n    consumed = true\n  }\n\n  /** @type {Effects['enter']} */\n  function enter(type, fields) {\n    /** @type {Token} */\n    // @ts-expect-error Patch instead of assign required fields to help GC.\n    const token = fields || {}\n    token.type = type\n    token.start = now()\n\n    assert(typeof type === 'string', 'expected string type')\n    assert(type.length > 0, 'expected non-empty string')\n    debug('enter: `%s`', type)\n\n    context.events.push(['enter', token, context])\n\n    stack.push(token)\n\n    return token\n  }\n\n  /** @type {Effects['exit']} */\n  function exit(type) {\n    assert(typeof type === 'string', 'expected string type')\n    assert(type.length > 0, 'expected non-empty string')\n\n    const token = stack.pop()\n    assert(token, 'cannot close w/o open tokens')\n    token.end = now()\n\n    assert(type === token.type, 'expected exit token to match current token')\n\n    assert(\n      !(\n        token.start._index === token.end._index &&\n        token.start._bufferIndex === token.end._bufferIndex\n      ),\n      'expected non-empty token (`' + type + '`)'\n    )\n\n    debug('exit: `%s`', token.type)\n    context.events.push(['exit', token, context])\n\n    return token\n  }\n\n  /**\n   * Use results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulconstruct(construct, info) {\n    addResult(construct, info.from)\n  }\n\n  /**\n   * Discard results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulcheck(_, info) {\n    info.restore()\n  }\n\n  /**\n   * Factory to attempt/check/interrupt.\n   *\n   * @param {ReturnHandle} onreturn\n   * @param {{interrupt?: boolean | undefined} | undefined} [fields]\n   */\n  function constructFactory(onreturn, fields) {\n    return hook\n\n    /**\n     * Handle either an object mapping codes to constructs, a list of\n     * constructs, or a single construct.\n     *\n     * @param {Array<Construct> | Construct | ConstructRecord} constructs\n     * @param {State} returnState\n     * @param {State | undefined} [bogusState]\n     * @returns {State}\n     */\n    function hook(constructs, returnState, bogusState) {\n      /** @type {Array<Construct>} */\n      let listOfConstructs\n      /** @type {number} */\n      let constructIndex\n      /** @type {Construct} */\n      let currentConstruct\n      /** @type {Info} */\n      let info\n\n      return Array.isArray(constructs)\n        ? /* c8 ignore next 1 */\n          handleListOfConstructs(constructs)\n        : 'tokenize' in constructs\n        ? // @ts-expect-error Looks like a construct.\n          handleListOfConstructs([constructs])\n        : handleMapOfConstructs(constructs)\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ConstructRecord} map\n       * @returns {State}\n       */\n      function handleMapOfConstructs(map) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          const def = code !== null && map[code]\n          const all = code !== null && map.null\n          const list = [\n            // To do: add more extension tests.\n            /* c8 ignore next 2 */\n            ...(Array.isArray(def) ? def : def ? [def] : []),\n            ...(Array.isArray(all) ? all : all ? [all] : [])\n          ]\n\n          return handleListOfConstructs(list)(code)\n        }\n      }\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {Array<Construct>} list\n       * @returns {State}\n       */\n      function handleListOfConstructs(list) {\n        listOfConstructs = list\n        constructIndex = 0\n\n        if (list.length === 0) {\n          assert(bogusState, 'expected `bogusState` to be given')\n          return bogusState\n        }\n\n        return handleConstruct(list[constructIndex])\n      }\n\n      /**\n       * Handle a single construct.\n       *\n       * @param {Construct} construct\n       * @returns {State}\n       */\n      function handleConstruct(construct) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          // To do: not needed to store if there is no bogus state, probably?\n          // Currently doesn’t work because `inspect` in document does a check\n          // w/o a bogus, which doesn’t make sense. But it does seem to help perf\n          // by not storing.\n          info = store()\n          currentConstruct = construct\n\n          if (!construct.partial) {\n            context.currentConstruct = construct\n          }\n\n          // Always populated by defaults.\n          assert(\n            context.parser.constructs.disable.null,\n            'expected `disable.null` to be populated'\n          )\n\n          if (\n            construct.name &&\n            context.parser.constructs.disable.null.includes(construct.name)\n          ) {\n            return nok(code)\n          }\n\n          return construct.tokenize.call(\n            // If we do have fields, create an object w/ `context` as its\n            // prototype.\n            // This allows a “live binding”, which is needed for `interrupt`.\n            fields ? Object.assign(Object.create(context), fields) : context,\n            effects,\n            ok,\n            nok\n          )(code)\n        }\n      }\n\n      /** @type {State} */\n      function ok(code) {\n        assert(code === expectedCode, 'expected code')\n        consumed = true\n        onreturn(currentConstruct, info)\n        return returnState\n      }\n\n      /** @type {State} */\n      function nok(code) {\n        assert(code === expectedCode, 'expected code')\n        consumed = true\n        info.restore()\n\n        if (++constructIndex < listOfConstructs.length) {\n          return handleConstruct(listOfConstructs[constructIndex])\n        }\n\n        return bogusState\n      }\n    }\n  }\n\n  /**\n   * @param {Construct} construct\n   * @param {number} from\n   * @returns {void}\n   */\n  function addResult(construct, from) {\n    if (construct.resolveAll && !resolveAllConstructs.includes(construct)) {\n      resolveAllConstructs.push(construct)\n    }\n\n    if (construct.resolve) {\n      splice(\n        context.events,\n        from,\n        context.events.length - from,\n        construct.resolve(context.events.slice(from), context)\n      )\n    }\n\n    if (construct.resolveTo) {\n      context.events = construct.resolveTo(context.events, context)\n    }\n\n    assert(\n      construct.partial ||\n        context.events.length === 0 ||\n        context.events[context.events.length - 1][0] === 'exit',\n      'expected last token to end'\n    )\n  }\n\n  /**\n   * Store state.\n   *\n   * @returns {Info}\n   */\n  function store() {\n    const startPoint = now()\n    const startPrevious = context.previous\n    const startCurrentConstruct = context.currentConstruct\n    const startEventsIndex = context.events.length\n    const startStack = Array.from(stack)\n\n    return {restore, from: startEventsIndex}\n\n    /**\n     * Restore state.\n     *\n     * @returns {void}\n     */\n    function restore() {\n      point = startPoint\n      context.previous = startPrevious\n      context.currentConstruct = startCurrentConstruct\n      context.events.length = startEventsIndex\n      stack = startStack\n      accountForPotentialSkip()\n      debug('position: restore: `%j`', point)\n    }\n  }\n\n  /**\n   * Move the current point a bit forward in the line when it’s on a column\n   * skip.\n   *\n   * @returns {void}\n   */\n  function accountForPotentialSkip() {\n    if (point.line in columnStart && point.column < 2) {\n      point.column = columnStart[point.line]\n      point.offset += columnStart[point.line] - 1\n    }\n  }\n}\n\n/**\n * Get the chunks from a slice of chunks in the range of a token.\n *\n * @param {Array<Chunk>} chunks\n * @param {Pick<Token, 'end' | 'start'>} token\n * @returns {Array<Chunk>}\n */\nfunction sliceChunks(chunks, token) {\n  const startIndex = token.start._index\n  const startBufferIndex = token.start._bufferIndex\n  const endIndex = token.end._index\n  const endBufferIndex = token.end._bufferIndex\n  /** @type {Array<Chunk>} */\n  let view\n\n  if (startIndex === endIndex) {\n    assert(endBufferIndex > -1, 'expected non-negative end buffer index')\n    assert(startBufferIndex > -1, 'expected non-negative start buffer index')\n    // @ts-expect-error `_bufferIndex` is used on string chunks.\n    view = [chunks[startIndex].slice(startBufferIndex, endBufferIndex)]\n  } else {\n    view = chunks.slice(startIndex, endIndex)\n\n    if (startBufferIndex > -1) {\n      const head = view[0]\n      if (typeof head === 'string') {\n        view[0] = head.slice(startBufferIndex)\n      } else {\n        assert(startBufferIndex === 0, 'expected `startBufferIndex` to be `0`')\n        view.shift()\n      }\n    }\n\n    if (endBufferIndex > 0) {\n      // @ts-expect-error `_bufferIndex` is used on string chunks.\n      view.push(chunks[endIndex].slice(0, endBufferIndex))\n    }\n  }\n\n  return view\n}\n\n/**\n * Get the string value of a slice of chunks.\n *\n * @param {Array<Chunk>} chunks\n * @param {boolean | undefined} [expandTabs=false]\n * @returns {string}\n */\nfunction serializeChunks(chunks, expandTabs) {\n  let index = -1\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {boolean | undefined} */\n  let atTab\n\n  while (++index < chunks.length) {\n    const chunk = chunks[index]\n    /** @type {string} */\n    let value\n\n    if (typeof chunk === 'string') {\n      value = chunk\n    } else\n      switch (chunk) {\n        case codes.carriageReturn: {\n          value = values.cr\n\n          break\n        }\n\n        case codes.lineFeed: {\n          value = values.lf\n\n          break\n        }\n\n        case codes.carriageReturnLineFeed: {\n          value = values.cr + values.lf\n\n          break\n        }\n\n        case codes.horizontalTab: {\n          value = expandTabs ? values.space : values.ht\n\n          break\n        }\n\n        case codes.virtualSpace: {\n          if (!expandTabs && atTab) continue\n          value = values.space\n\n          break\n        }\n\n        default: {\n          assert(typeof chunk === 'number', 'expected number')\n          // Currently only replacement character.\n          value = String.fromCharCode(chunk)\n        }\n      }\n\n    atTab = chunk === codes.horizontalTab\n    result.push(value)\n  }\n\n  return result.join('')\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,WAAW,MAAM,OAAO;AAC/B,SAAQC,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,IAAI,EAAEC,MAAM,QAAO,wBAAwB;AACnD,SAAQC,UAAU,QAAO,4BAA4B;AACrD,SAAQC,KAAK,QAAO,gCAAgC;AACpD,SAAQC,MAAM,QAAO,iCAAiC;AACtD,SAAQC,EAAE,IAAIC,MAAM,QAAO,YAAY;AAEvC,MAAMC,KAAK,GAAGT,WAAW,CAAC,WAAW,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASU,eAAeA,CAACC,MAAM,EAAEC,UAAU,EAAEC,IAAI,EAAE;EACxD;EACA,IAAIC,KAAK,GAAGC,MAAM,CAACC,MAAM,CACvBH,IAAI,GAAGE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,IAAI,CAAC,GAAG;IAACI,IAAI,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAC,CAAC,EAChE;IAACC,MAAM,EAAE,CAAC;IAAEC,YAAY,EAAE,CAAC;EAAC,CAC9B,CAAC;EACD;EACA,MAAMC,WAAW,GAAG,CAAC,CAAC;EACtB;EACA,MAAMC,oBAAoB,GAAG,EAAE;EAC/B;EACA,IAAIC,MAAM,GAAG,EAAE;EACf;EACA,IAAIC,KAAK,GAAG,EAAE;EACd;EACA,IAAIC,QAAQ,GAAG,IAAI;;EAEnB;AACF;AACA;AACA;AACA;EACE,MAAMC,OAAO,GAAG;IACdC,OAAO;IACPC,KAAK;IACLC,IAAI;IACJC,OAAO,EAAEC,gBAAgB,CAACC,qBAAqB,CAAC;IAChDC,KAAK,EAAEF,gBAAgB,CAACG,iBAAiB,CAAC;IAC1CC,SAAS,EAAEJ,gBAAgB,CAACG,iBAAiB,EAAE;MAACC,SAAS,EAAE;IAAI,CAAC;EAClE,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMC,OAAO,GAAG;IACdC,QAAQ,EAAEjC,KAAK,CAACkC,GAAG;IACnBC,IAAI,EAAEnC,KAAK,CAACkC,GAAG;IACfE,cAAc,EAAE,CAAC,CAAC;IAClBC,MAAM,EAAE,EAAE;IACV/B,MAAM;IACNgC,WAAW;IACXC,cAAc;IACdC,GAAG;IACHC,UAAU;IACVC;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,IAAIC,KAAK,GAAGpC,UAAU,CAACqC,QAAQ,CAACC,IAAI,CAACb,OAAO,EAAEV,OAAO,CAAC;;EAEtD;AACF;AACA;AACA;AACA;EACE,IAAIwB,YAAY;EAEhB,IAAIvC,UAAU,CAACR,UAAU,EAAE;IACzBmB,oBAAoB,CAACrB,IAAI,CAACU,UAAU,CAAC;EACvC;EAEA,OAAOyB,OAAO;;EAEd;EACA,SAASU,KAAKA,CAACK,KAAK,EAAE;IACpB5B,MAAM,GAAGtB,IAAI,CAACsB,MAAM,EAAE4B,KAAK,CAAC;IAE5BC,IAAI,CAAC,CAAC;;IAEN;IACA,IAAI7B,MAAM,CAACA,MAAM,CAAC8B,MAAM,GAAG,CAAC,CAAC,KAAKjD,KAAK,CAACkC,GAAG,EAAE;MAC3C,OAAO,EAAE;IACX;IAEAgB,SAAS,CAAC3C,UAAU,EAAE,CAAC,CAAC;;IAExB;IACAyB,OAAO,CAACK,MAAM,GAAGtC,UAAU,CAACmB,oBAAoB,EAAEc,OAAO,CAACK,MAAM,EAAEL,OAAO,CAAC;IAE1E,OAAOA,OAAO,CAACK,MAAM;EACvB;;EAEA;EACA;EACA;;EAEA;EACA,SAASE,cAAcA,CAACY,KAAK,EAAEC,UAAU,EAAE;IACzC,OAAOC,eAAe,CAACf,WAAW,CAACa,KAAK,CAAC,EAAEC,UAAU,CAAC;EACxD;;EAEA;EACA,SAASd,WAAWA,CAACa,KAAK,EAAE;IAC1B,OAAOG,WAAW,CAACnC,MAAM,EAAEgC,KAAK,CAAC;EACnC;;EAEA;EACA,SAASX,GAAGA,CAAA,EAAG;IACb;IACA,MAAM;MAAC5B,IAAI;MAAEC,MAAM;MAAEC,MAAM;MAAEC,MAAM;MAAEC;IAAY,CAAC,GAAGP,KAAK;IAC1D,OAAO;MAACG,IAAI;MAAEC,MAAM;MAAEC,MAAM;MAAEC,MAAM;MAAEC;IAAY,CAAC;EACrD;;EAEA;EACA,SAASyB,UAAUA,CAACc,KAAK,EAAE;IACzBtC,WAAW,CAACsC,KAAK,CAAC3C,IAAI,CAAC,GAAG2C,KAAK,CAAC1C,MAAM;IACtC2C,uBAAuB,CAAC,CAAC;IACzBpD,KAAK,CAAC,6BAA6B,EAAEK,KAAK,CAAC;EAC7C;;EAEA;EACA;EACA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASuC,IAAIA,CAAA,EAAG;IACd;IACA,IAAIS,UAAU;IAEd,OAAOhD,KAAK,CAACM,MAAM,GAAGI,MAAM,CAAC8B,MAAM,EAAE;MACnC,MAAMS,KAAK,GAAGvC,MAAM,CAACV,KAAK,CAACM,MAAM,CAAC;;MAElC;MACA,IAAI,OAAO2C,KAAK,KAAK,QAAQ,EAAE;QAC7BD,UAAU,GAAGhD,KAAK,CAACM,MAAM;QAEzB,IAAIN,KAAK,CAACO,YAAY,GAAG,CAAC,EAAE;UAC1BP,KAAK,CAACO,YAAY,GAAG,CAAC;QACxB;QAEA,OACEP,KAAK,CAACM,MAAM,KAAK0C,UAAU,IAC3BhD,KAAK,CAACO,YAAY,GAAG0C,KAAK,CAACT,MAAM,EACjC;UACAU,EAAE,CAACD,KAAK,CAACE,UAAU,CAACnD,KAAK,CAACO,YAAY,CAAC,CAAC;QAC1C;MACF,CAAC,MAAM;QACL2C,EAAE,CAACD,KAAK,CAAC;MACX;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASC,EAAEA,CAACxB,IAAI,EAAE;IAChBhC,MAAM,CAACkB,QAAQ,KAAK,IAAI,EAAE,mCAAmC,CAAC;IAC9DA,QAAQ,GAAGwC,SAAS;IACpBzD,KAAK,CAAC,0BAA0B,EAAE+B,IAAI,EAAEQ,KAAK,IAAIA,KAAK,CAACmB,IAAI,CAAC;IAC5DhB,YAAY,GAAGX,IAAI;IACnBhC,MAAM,CAAC,OAAOwC,KAAK,KAAK,UAAU,EAAE,gBAAgB,CAAC;IACrDA,KAAK,GAAGA,KAAK,CAACR,IAAI,CAAC;EACrB;;EAEA;EACA,SAASZ,OAAOA,CAACY,IAAI,EAAE;IACrBhC,MAAM,CAACgC,IAAI,KAAKW,YAAY,EAAE,4CAA4C,CAAC;IAE3E1C,KAAK,CAAC,eAAe,EAAE+B,IAAI,CAAC;IAE5BhC,MAAM,CACJkB,QAAQ,KAAKwC,SAAS,EACtB,gHACF,CAAC;IACD1D,MAAM,CACJgC,IAAI,KAAK,IAAI,GACTH,OAAO,CAACK,MAAM,CAACY,MAAM,KAAK,CAAC,IACzBjB,OAAO,CAACK,MAAM,CAACL,OAAO,CAACK,MAAM,CAACY,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,GACzDjB,OAAO,CAACK,MAAM,CAACL,OAAO,CAACK,MAAM,CAACY,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,EAC5D,gCACF,CAAC;IAED,IAAIrD,kBAAkB,CAACuC,IAAI,CAAC,EAAE;MAC5B1B,KAAK,CAACG,IAAI,EAAE;MACZH,KAAK,CAACI,MAAM,GAAG,CAAC;MAChBJ,KAAK,CAACK,MAAM,IAAIqB,IAAI,KAAKnC,KAAK,CAAC+D,sBAAsB,GAAG,CAAC,GAAG,CAAC;MAC7DP,uBAAuB,CAAC,CAAC;MACzBpD,KAAK,CAAC,2BAA2B,EAAEK,KAAK,CAAC;IAC3C,CAAC,MAAM,IAAI0B,IAAI,KAAKnC,KAAK,CAACgE,YAAY,EAAE;MACtCvD,KAAK,CAACI,MAAM,EAAE;MACdJ,KAAK,CAACK,MAAM,EAAE;IAChB;;IAEA;IACA,IAAIL,KAAK,CAACO,YAAY,GAAG,CAAC,EAAE;MAC1BP,KAAK,CAACM,MAAM,EAAE;IAChB,CAAC,MAAM;MACLN,KAAK,CAACO,YAAY,EAAE;;MAEpB;MACA;MACA;MACA,IAAIP,KAAK,CAACO,YAAY,KAAKG,MAAM,CAACV,KAAK,CAACM,MAAM,CAAC,CAACkC,MAAM,EAAE;QACtDxC,KAAK,CAACO,YAAY,GAAG,CAAC,CAAC;QACvBP,KAAK,CAACM,MAAM,EAAE;MAChB;IACF;;IAEA;IACAiB,OAAO,CAACC,QAAQ,GAAGE,IAAI;;IAEvB;IACAd,QAAQ,GAAG,IAAI;EACjB;;EAEA;EACA,SAASG,KAAKA,CAACyC,IAAI,EAAEC,MAAM,EAAE;IAC3B;IACA;IACA,MAAMf,KAAK,GAAGe,MAAM,IAAI,CAAC,CAAC;IAC1Bf,KAAK,CAACc,IAAI,GAAGA,IAAI;IACjBd,KAAK,CAACgB,KAAK,GAAG3B,GAAG,CAAC,CAAC;IAEnBrC,MAAM,CAAC,OAAO8D,IAAI,KAAK,QAAQ,EAAE,sBAAsB,CAAC;IACxD9D,MAAM,CAAC8D,IAAI,CAAChB,MAAM,GAAG,CAAC,EAAE,2BAA2B,CAAC;IACpD7C,KAAK,CAAC,aAAa,EAAE6D,IAAI,CAAC;IAE1BjC,OAAO,CAACK,MAAM,CAACxC,IAAI,CAAC,CAAC,OAAO,EAAEsD,KAAK,EAAEnB,OAAO,CAAC,CAAC;IAE9CZ,KAAK,CAACvB,IAAI,CAACsD,KAAK,CAAC;IAEjB,OAAOA,KAAK;EACd;;EAEA;EACA,SAAS1B,IAAIA,CAACwC,IAAI,EAAE;IAClB9D,MAAM,CAAC,OAAO8D,IAAI,KAAK,QAAQ,EAAE,sBAAsB,CAAC;IACxD9D,MAAM,CAAC8D,IAAI,CAAChB,MAAM,GAAG,CAAC,EAAE,2BAA2B,CAAC;IAEpD,MAAME,KAAK,GAAG/B,KAAK,CAACgD,GAAG,CAAC,CAAC;IACzBjE,MAAM,CAACgD,KAAK,EAAE,8BAA8B,CAAC;IAC7CA,KAAK,CAACkB,GAAG,GAAG7B,GAAG,CAAC,CAAC;IAEjBrC,MAAM,CAAC8D,IAAI,KAAKd,KAAK,CAACc,IAAI,EAAE,4CAA4C,CAAC;IAEzE9D,MAAM,CACJ,EACEgD,KAAK,CAACgB,KAAK,CAACpD,MAAM,KAAKoC,KAAK,CAACkB,GAAG,CAACtD,MAAM,IACvCoC,KAAK,CAACgB,KAAK,CAACnD,YAAY,KAAKmC,KAAK,CAACkB,GAAG,CAACrD,YAAY,CACpD,EACD,6BAA6B,GAAGiD,IAAI,GAAG,IACzC,CAAC;IAED7D,KAAK,CAAC,YAAY,EAAE+C,KAAK,CAACc,IAAI,CAAC;IAC/BjC,OAAO,CAACK,MAAM,CAACxC,IAAI,CAAC,CAAC,MAAM,EAAEsD,KAAK,EAAEnB,OAAO,CAAC,CAAC;IAE7C,OAAOmB,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASvB,qBAAqBA,CAAC0C,SAAS,EAAEC,IAAI,EAAE;IAC9CrB,SAAS,CAACoB,SAAS,EAAEC,IAAI,CAAC/D,IAAI,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASsB,iBAAiBA,CAAC0C,CAAC,EAAED,IAAI,EAAE;IAClCA,IAAI,CAACE,OAAO,CAAC,CAAC;EAChB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAAS9C,gBAAgBA,CAAC+C,QAAQ,EAAER,MAAM,EAAE;IAC1C,OAAOS,IAAI;;IAEX;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAASA,IAAIA,CAACC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAE;MACjD;MACA,IAAIC,gBAAgB;MACpB;MACA,IAAIC,cAAc;MAClB;MACA,IAAIC,gBAAgB;MACpB;MACA,IAAIV,IAAI;MAER,OAAOW,KAAK,CAACC,OAAO,CAACP,UAAU,CAAC,GAC5B;MACAQ,sBAAsB,CAACR,UAAU,CAAC,GAClC,UAAU,IAAIA,UAAU;MACxB;MACAQ,sBAAsB,CAAC,CAACR,UAAU,CAAC,CAAC,GACpCS,qBAAqB,CAACT,UAAU,CAAC;;MAErC;AACN;AACA;AACA;AACA;AACA;MACM,SAASS,qBAAqBA,CAACC,GAAG,EAAE;QAClC,OAAOnB,KAAK;;QAEZ;QACA,SAASA,KAAKA,CAAChC,IAAI,EAAE;UACnB,MAAMoD,GAAG,GAAGpD,IAAI,KAAK,IAAI,IAAImD,GAAG,CAACnD,IAAI,CAAC;UACtC,MAAMqD,GAAG,GAAGrD,IAAI,KAAK,IAAI,IAAImD,GAAG,CAACG,IAAI;UACrC,MAAMC,IAAI,GAAG;UACX;UACA;UACA,IAAIR,KAAK,CAACC,OAAO,CAACI,GAAG,CAAC,GAAGA,GAAG,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC,GAAG,EAAE,CAAC,EAChD,IAAIL,KAAK,CAACC,OAAO,CAACK,GAAG,CAAC,GAAGA,GAAG,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC,GAAG,EAAE,CAAC,CACjD;UAED,OAAOJ,sBAAsB,CAACM,IAAI,CAAC,CAACvD,IAAI,CAAC;QAC3C;MACF;;MAEA;AACN;AACA;AACA;AACA;AACA;MACM,SAASiD,sBAAsBA,CAACM,IAAI,EAAE;QACpCX,gBAAgB,GAAGW,IAAI;QACvBV,cAAc,GAAG,CAAC;QAElB,IAAIU,IAAI,CAACzC,MAAM,KAAK,CAAC,EAAE;UACrB9C,MAAM,CAAC2E,UAAU,EAAE,mCAAmC,CAAC;UACvD,OAAOA,UAAU;QACnB;QAEA,OAAOa,eAAe,CAACD,IAAI,CAACV,cAAc,CAAC,CAAC;MAC9C;;MAEA;AACN;AACA;AACA;AACA;AACA;MACM,SAASW,eAAeA,CAACrB,SAAS,EAAE;QAClC,OAAOH,KAAK;;QAEZ;QACA,SAASA,KAAKA,CAAChC,IAAI,EAAE;UACnB;UACA;UACA;UACA;UACAoC,IAAI,GAAGqB,KAAK,CAAC,CAAC;UACdX,gBAAgB,GAAGX,SAAS;UAE5B,IAAI,CAACA,SAAS,CAACuB,OAAO,EAAE;YACtB7D,OAAO,CAACiD,gBAAgB,GAAGX,SAAS;UACtC;;UAEA;UACAnE,MAAM,CACJ6B,OAAO,CAAC1B,MAAM,CAACsE,UAAU,CAACkB,OAAO,CAACL,IAAI,EACtC,yCACF,CAAC;UAED,IACEnB,SAAS,CAACR,IAAI,IACd9B,OAAO,CAAC1B,MAAM,CAACsE,UAAU,CAACkB,OAAO,CAACL,IAAI,CAACM,QAAQ,CAACzB,SAAS,CAACR,IAAI,CAAC,EAC/D;YACA,OAAOkC,GAAG,CAAC7D,IAAI,CAAC;UAClB;UAEA,OAAOmC,SAAS,CAAC1B,QAAQ,CAACC,IAAI;UAC5B;UACA;UACA;UACAqB,MAAM,GAAGxD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACuF,MAAM,CAACjE,OAAO,CAAC,EAAEkC,MAAM,CAAC,GAAGlC,OAAO,EAChEV,OAAO,EACPpB,EAAE,EACF8F,GACF,CAAC,CAAC7D,IAAI,CAAC;QACT;MACF;;MAEA;MACA,SAASjC,EAAEA,CAACiC,IAAI,EAAE;QAChBhC,MAAM,CAACgC,IAAI,KAAKW,YAAY,EAAE,eAAe,CAAC;QAC9CzB,QAAQ,GAAG,IAAI;QACfqD,QAAQ,CAACO,gBAAgB,EAAEV,IAAI,CAAC;QAChC,OAAOM,WAAW;MACpB;;MAEA;MACA,SAASmB,GAAGA,CAAC7D,IAAI,EAAE;QACjBhC,MAAM,CAACgC,IAAI,KAAKW,YAAY,EAAE,eAAe,CAAC;QAC9CzB,QAAQ,GAAG,IAAI;QACfkD,IAAI,CAACE,OAAO,CAAC,CAAC;QAEd,IAAI,EAAEO,cAAc,GAAGD,gBAAgB,CAAC9B,MAAM,EAAE;UAC9C,OAAO0C,eAAe,CAACZ,gBAAgB,CAACC,cAAc,CAAC,CAAC;QAC1D;QAEA,OAAOF,UAAU;MACnB;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,SAAS5B,SAASA,CAACoB,SAAS,EAAE9D,IAAI,EAAE;IAClC,IAAI8D,SAAS,CAACvE,UAAU,IAAI,CAACmB,oBAAoB,CAAC6E,QAAQ,CAACzB,SAAS,CAAC,EAAE;MACrEpD,oBAAoB,CAACrB,IAAI,CAACyE,SAAS,CAAC;IACtC;IAEA,IAAIA,SAAS,CAAC4B,OAAO,EAAE;MACrBpG,MAAM,CACJkC,OAAO,CAACK,MAAM,EACd7B,IAAI,EACJwB,OAAO,CAACK,MAAM,CAACY,MAAM,GAAGzC,IAAI,EAC5B8D,SAAS,CAAC4B,OAAO,CAAClE,OAAO,CAACK,MAAM,CAACU,KAAK,CAACvC,IAAI,CAAC,EAAEwB,OAAO,CACvD,CAAC;IACH;IAEA,IAAIsC,SAAS,CAAC6B,SAAS,EAAE;MACvBnE,OAAO,CAACK,MAAM,GAAGiC,SAAS,CAAC6B,SAAS,CAACnE,OAAO,CAACK,MAAM,EAAEL,OAAO,CAAC;IAC/D;IAEA7B,MAAM,CACJmE,SAAS,CAACuB,OAAO,IACf7D,OAAO,CAACK,MAAM,CAACY,MAAM,KAAK,CAAC,IAC3BjB,OAAO,CAACK,MAAM,CAACL,OAAO,CAACK,MAAM,CAACY,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,EACzD,4BACF,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;EACE,SAAS2C,KAAKA,CAAA,EAAG;IACf,MAAMQ,UAAU,GAAG5D,GAAG,CAAC,CAAC;IACxB,MAAM6D,aAAa,GAAGrE,OAAO,CAACC,QAAQ;IACtC,MAAMqE,qBAAqB,GAAGtE,OAAO,CAACiD,gBAAgB;IACtD,MAAMsB,gBAAgB,GAAGvE,OAAO,CAACK,MAAM,CAACY,MAAM;IAC9C,MAAMuD,UAAU,GAAGtB,KAAK,CAAC1E,IAAI,CAACY,KAAK,CAAC;IAEpC,OAAO;MAACqD,OAAO;MAAEjE,IAAI,EAAE+F;IAAgB,CAAC;;IAExC;AACJ;AACA;AACA;AACA;IACI,SAAS9B,OAAOA,CAAA,EAAG;MACjBhE,KAAK,GAAG2F,UAAU;MAClBpE,OAAO,CAACC,QAAQ,GAAGoE,aAAa;MAChCrE,OAAO,CAACiD,gBAAgB,GAAGqB,qBAAqB;MAChDtE,OAAO,CAACK,MAAM,CAACY,MAAM,GAAGsD,gBAAgB;MACxCnF,KAAK,GAAGoF,UAAU;MAClBhD,uBAAuB,CAAC,CAAC;MACzBpD,KAAK,CAAC,yBAAyB,EAAEK,KAAK,CAAC;IACzC;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAAS+C,uBAAuBA,CAAA,EAAG;IACjC,IAAI/C,KAAK,CAACG,IAAI,IAAIK,WAAW,IAAIR,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;MACjDJ,KAAK,CAACI,MAAM,GAAGI,WAAW,CAACR,KAAK,CAACG,IAAI,CAAC;MACtCH,KAAK,CAACK,MAAM,IAAIG,WAAW,CAACR,KAAK,CAACG,IAAI,CAAC,GAAG,CAAC;IAC7C;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0C,WAAWA,CAACnC,MAAM,EAAEgC,KAAK,EAAE;EAClC,MAAMsD,UAAU,GAAGtD,KAAK,CAACgB,KAAK,CAACpD,MAAM;EACrC,MAAM2F,gBAAgB,GAAGvD,KAAK,CAACgB,KAAK,CAACnD,YAAY;EACjD,MAAM2F,QAAQ,GAAGxD,KAAK,CAACkB,GAAG,CAACtD,MAAM;EACjC,MAAM6F,cAAc,GAAGzD,KAAK,CAACkB,GAAG,CAACrD,YAAY;EAC7C;EACA,IAAI6F,IAAI;EAER,IAAIJ,UAAU,KAAKE,QAAQ,EAAE;IAC3BxG,MAAM,CAACyG,cAAc,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC;IACrEzG,MAAM,CAACuG,gBAAgB,GAAG,CAAC,CAAC,EAAE,0CAA0C,CAAC;IACzE;IACAG,IAAI,GAAG,CAAC1F,MAAM,CAACsF,UAAU,CAAC,CAAC1D,KAAK,CAAC2D,gBAAgB,EAAEE,cAAc,CAAC,CAAC;EACrE,CAAC,MAAM;IACLC,IAAI,GAAG1F,MAAM,CAAC4B,KAAK,CAAC0D,UAAU,EAAEE,QAAQ,CAAC;IAEzC,IAAID,gBAAgB,GAAG,CAAC,CAAC,EAAE;MACzB,MAAMI,IAAI,GAAGD,IAAI,CAAC,CAAC,CAAC;MACpB,IAAI,OAAOC,IAAI,KAAK,QAAQ,EAAE;QAC5BD,IAAI,CAAC,CAAC,CAAC,GAAGC,IAAI,CAAC/D,KAAK,CAAC2D,gBAAgB,CAAC;MACxC,CAAC,MAAM;QACLvG,MAAM,CAACuG,gBAAgB,KAAK,CAAC,EAAE,uCAAuC,CAAC;QACvEG,IAAI,CAACE,KAAK,CAAC,CAAC;MACd;IACF;IAEA,IAAIH,cAAc,GAAG,CAAC,EAAE;MACtB;MACAC,IAAI,CAAChH,IAAI,CAACsB,MAAM,CAACwF,QAAQ,CAAC,CAAC5D,KAAK,CAAC,CAAC,EAAE6D,cAAc,CAAC,CAAC;IACtD;EACF;EAEA,OAAOC,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASxD,eAAeA,CAAClC,MAAM,EAAEiC,UAAU,EAAE;EAC3C,IAAI4D,KAAK,GAAG,CAAC,CAAC;EACd;EACA,MAAMC,MAAM,GAAG,EAAE;EACjB;EACA,IAAIC,KAAK;EAET,OAAO,EAAEF,KAAK,GAAG7F,MAAM,CAAC8B,MAAM,EAAE;IAC9B,MAAMS,KAAK,GAAGvC,MAAM,CAAC6F,KAAK,CAAC;IAC3B;IACA,IAAIzD,KAAK;IAET,IAAI,OAAOG,KAAK,KAAK,QAAQ,EAAE;MAC7BH,KAAK,GAAGG,KAAK;IACf,CAAC,MACC,QAAQA,KAAK;MACX,KAAK1D,KAAK,CAACmH,cAAc;QAAE;UACzB5D,KAAK,GAAGtD,MAAM,CAACmH,EAAE;UAEjB;QACF;MAEA,KAAKpH,KAAK,CAACqH,QAAQ;QAAE;UACnB9D,KAAK,GAAGtD,MAAM,CAACqH,EAAE;UAEjB;QACF;MAEA,KAAKtH,KAAK,CAAC+D,sBAAsB;QAAE;UACjCR,KAAK,GAAGtD,MAAM,CAACmH,EAAE,GAAGnH,MAAM,CAACqH,EAAE;UAE7B;QACF;MAEA,KAAKtH,KAAK,CAACuH,aAAa;QAAE;UACxBhE,KAAK,GAAGH,UAAU,GAAGnD,MAAM,CAACuH,KAAK,GAAGvH,MAAM,CAACwH,EAAE;UAE7C;QACF;MAEA,KAAKzH,KAAK,CAACgE,YAAY;QAAE;UACvB,IAAI,CAACZ,UAAU,IAAI8D,KAAK,EAAE;UAC1B3D,KAAK,GAAGtD,MAAM,CAACuH,KAAK;UAEpB;QACF;MAEA;QAAS;UACPrH,MAAM,CAAC,OAAOuD,KAAK,KAAK,QAAQ,EAAE,iBAAiB,CAAC;UACpD;UACAH,KAAK,GAAGmE,MAAM,CAACC,YAAY,CAACjE,KAAK,CAAC;QACpC;IACF;IAEFwD,KAAK,GAAGxD,KAAK,KAAK1D,KAAK,CAACuH,aAAa;IACrCN,MAAM,CAACpH,IAAI,CAAC0D,KAAK,CAAC;EACpB;EAEA,OAAO0D,MAAM,CAACW,IAAI,CAAC,EAAE,CAAC;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}