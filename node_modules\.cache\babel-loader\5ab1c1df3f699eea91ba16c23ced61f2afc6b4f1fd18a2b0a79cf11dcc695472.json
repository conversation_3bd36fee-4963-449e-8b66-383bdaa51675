{"ast": null, "code": "/**\n * This module is compiled away!\n *\n * Here is the list of all types of tokens exposed by micromark, with a short\n * explanation of what they include and where they are found.\n * In picking names, generally, the rule is to be as explicit as possible\n * instead of reusing names.\n * For example, there is a `definitionDestination` and a `resourceDestination`,\n * instead of one shared name.\n */\n\n// Note: when changing the next record, you must also change `TokenTypeMap`\n// in `micromark-util-types/index.d.ts`.\nexport const types = /** @type {const} */{\n  // Generic type for data, such as in a title, a destination, etc.\n  data: 'data',\n  // Generic type for syntactic whitespace (tabs, virtual spaces, spaces).\n  // Such as, between a fenced code fence and an info string.\n  whitespace: 'whitespace',\n  // Generic type for line endings (line feed, carriage return, carriage return +\n  // line feed).\n  lineEnding: 'lineEnding',\n  // A line ending, but ending a blank line.\n  lineEndingBlank: 'lineEndingBlank',\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the start of a\n  // line.\n  linePrefix: 'linePrefix',\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the end of a\n  // line.\n  lineSuffix: 'lineSuffix',\n  // Whole ATX heading:\n  //\n  // ```markdown\n  // #\n  // ## Alpha\n  // ### Bravo ###\n  // ```\n  //\n  // Includes `atxHeadingSequence`, `whitespace`, `atxHeadingText`.\n  atxHeading: 'atxHeading',\n  // Sequence of number signs in an ATX heading (`###`).\n  atxHeadingSequence: 'atxHeadingSequence',\n  // Content in an ATX heading (`alpha`).\n  // Includes text.\n  atxHeadingText: 'atxHeadingText',\n  // Whole autolink (`<https://example.com>` or `<<EMAIL>>`)\n  // Includes `autolinkMarker` and `autolinkProtocol` or `autolinkEmail`.\n  autolink: 'autolink',\n  // Email autolink w/o markers (`<EMAIL>`)\n  autolinkEmail: 'autolinkEmail',\n  // Marker around an `autolinkProtocol` or `autolinkEmail` (`<` or `>`).\n  autolinkMarker: 'autolinkMarker',\n  // Protocol autolink w/o markers (`https://example.com`)\n  autolinkProtocol: 'autolinkProtocol',\n  // A whole character escape (`\\-`).\n  // Includes `escapeMarker` and `characterEscapeValue`.\n  characterEscape: 'characterEscape',\n  // The escaped character (`-`).\n  characterEscapeValue: 'characterEscapeValue',\n  // A whole character reference (`&amp;`, `&#8800;`, or `&#x1D306;`).\n  // Includes `characterReferenceMarker`, an optional\n  // `characterReferenceMarkerNumeric`, in which case an optional\n  // `characterReferenceMarkerHexadecimal`, and a `characterReferenceValue`.\n  characterReference: 'characterReference',\n  // The start or end marker (`&` or `;`).\n  characterReferenceMarker: 'characterReferenceMarker',\n  // Mark reference as numeric (`#`).\n  characterReferenceMarkerNumeric: 'characterReferenceMarkerNumeric',\n  // Mark reference as numeric (`x` or `X`).\n  characterReferenceMarkerHexadecimal: 'characterReferenceMarkerHexadecimal',\n  // Value of character reference w/o markers (`amp`, `8800`, or `1D306`).\n  characterReferenceValue: 'characterReferenceValue',\n  // Whole fenced code:\n  //\n  // ````markdown\n  // ```js\n  // alert(1)\n  // ```\n  // ````\n  codeFenced: 'codeFenced',\n  // A fenced code fence, including whitespace, sequence, info, and meta\n  // (` ```js `).\n  codeFencedFence: 'codeFencedFence',\n  // Sequence of grave accent or tilde characters (` ``` `) in a fence.\n  codeFencedFenceSequence: 'codeFencedFenceSequence',\n  // Info word (`js`) in a fence.\n  // Includes string.\n  codeFencedFenceInfo: 'codeFencedFenceInfo',\n  // Meta words (`highlight=\"1\"`) in a fence.\n  // Includes string.\n  codeFencedFenceMeta: 'codeFencedFenceMeta',\n  // A line of code.\n  codeFlowValue: 'codeFlowValue',\n  // Whole indented code:\n  //\n  // ```markdown\n  //     alert(1)\n  // ```\n  //\n  // Includes `lineEnding`, `linePrefix`, and `codeFlowValue`.\n  codeIndented: 'codeIndented',\n  // A text code (``` `alpha` ```).\n  // Includes `codeTextSequence`, `codeTextData`, `lineEnding`, and can include\n  // `codeTextPadding`.\n  codeText: 'codeText',\n  codeTextData: 'codeTextData',\n  // A space or line ending right after or before a tick.\n  codeTextPadding: 'codeTextPadding',\n  // A text code fence (` `` `).\n  codeTextSequence: 'codeTextSequence',\n  // Whole content:\n  //\n  // ```markdown\n  // [a]: b\n  // c\n  // =\n  // d\n  // ```\n  //\n  // Includes `paragraph` and `definition`.\n  content: 'content',\n  // Whole definition:\n  //\n  // ```markdown\n  // [micromark]: https://github.com/micromark/micromark\n  // ```\n  //\n  // Includes `definitionLabel`, `definitionMarker`, `whitespace`,\n  // `definitionDestination`, and optionally `lineEnding` and `definitionTitle`.\n  definition: 'definition',\n  // Destination of a definition (`https://github.com/micromark/micromark` or\n  // `<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteral` or `definitionDestinationRaw`.\n  definitionDestination: 'definitionDestination',\n  // Enclosed destination of a definition\n  // (`<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteralMarker` and optionally\n  // `definitionDestinationString`.\n  definitionDestinationLiteral: 'definitionDestinationLiteral',\n  // Markers of an enclosed definition destination (`<` or `>`).\n  definitionDestinationLiteralMarker: 'definitionDestinationLiteralMarker',\n  // Unenclosed destination of a definition\n  // (`https://github.com/micromark/micromark`).\n  // Includes `definitionDestinationString`.\n  definitionDestinationRaw: 'definitionDestinationRaw',\n  // Text in an destination (`https://github.com/micromark/micromark`).\n  // Includes string.\n  definitionDestinationString: 'definitionDestinationString',\n  // Label of a definition (`[micromark]`).\n  // Includes `definitionLabelMarker` and `definitionLabelString`.\n  definitionLabel: 'definitionLabel',\n  // Markers of a definition label (`[` or `]`).\n  definitionLabelMarker: 'definitionLabelMarker',\n  // Value of a definition label (`micromark`).\n  // Includes string.\n  definitionLabelString: 'definitionLabelString',\n  // Marker between a label and a destination (`:`).\n  definitionMarker: 'definitionMarker',\n  // Title of a definition (`\"x\"`, `'y'`, or `(z)`).\n  // Includes `definitionTitleMarker` and optionally `definitionTitleString`.\n  definitionTitle: 'definitionTitle',\n  // Marker around a title of a definition (`\"`, `'`, `(`, or `)`).\n  definitionTitleMarker: 'definitionTitleMarker',\n  // Data without markers in a title (`z`).\n  // Includes string.\n  definitionTitleString: 'definitionTitleString',\n  // Emphasis (`*alpha*`).\n  // Includes `emphasisSequence` and `emphasisText`.\n  emphasis: 'emphasis',\n  // Sequence of emphasis markers (`*` or `_`).\n  emphasisSequence: 'emphasisSequence',\n  // Emphasis text (`alpha`).\n  // Includes text.\n  emphasisText: 'emphasisText',\n  // The character escape marker (`\\`).\n  escapeMarker: 'escapeMarker',\n  // A hard break created with a backslash (`\\\\n`).\n  // Note: does not include the line ending.\n  hardBreakEscape: 'hardBreakEscape',\n  // A hard break created with trailing spaces (`  \\n`).\n  // Does not include the line ending.\n  hardBreakTrailing: 'hardBreakTrailing',\n  // Flow HTML:\n  //\n  // ```markdown\n  // <div\n  // ```\n  //\n  // Inlcudes `lineEnding`, `htmlFlowData`.\n  htmlFlow: 'htmlFlow',\n  htmlFlowData: 'htmlFlowData',\n  // HTML in text (the tag in `a <i> b`).\n  // Includes `lineEnding`, `htmlTextData`.\n  htmlText: 'htmlText',\n  htmlTextData: 'htmlTextData',\n  // Whole image (`![alpha](bravo)`, `![alpha][bravo]`, `![alpha][]`, or\n  // `![alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  image: 'image',\n  // Whole link label (`[*alpha*]`).\n  // Includes `labelLink` or `labelImage`, `labelText`, and `labelEnd`.\n  label: 'label',\n  // Text in an label (`*alpha*`).\n  // Includes text.\n  labelText: 'labelText',\n  // Start a link label (`[`).\n  // Includes a `labelMarker`.\n  labelLink: 'labelLink',\n  // Start an image label (`![`).\n  // Includes `labelImageMarker` and `labelMarker`.\n  labelImage: 'labelImage',\n  // Marker of a label (`[` or `]`).\n  labelMarker: 'labelMarker',\n  // Marker to start an image (`!`).\n  labelImageMarker: 'labelImageMarker',\n  // End a label (`]`).\n  // Includes `labelMarker`.\n  labelEnd: 'labelEnd',\n  // Whole link (`[alpha](bravo)`, `[alpha][bravo]`, `[alpha][]`, or `[alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  link: 'link',\n  // Whole paragraph:\n  //\n  // ```markdown\n  // alpha\n  // bravo.\n  // ```\n  //\n  // Includes text.\n  paragraph: 'paragraph',\n  // A reference (`[alpha]` or `[]`).\n  // Includes `referenceMarker` and an optional `referenceString`.\n  reference: 'reference',\n  // A reference marker (`[` or `]`).\n  referenceMarker: 'referenceMarker',\n  // Reference text (`alpha`).\n  // Includes string.\n  referenceString: 'referenceString',\n  // A resource (`(https://example.com \"alpha\")`).\n  // Includes `resourceMarker`, an optional `resourceDestination` with an optional\n  // `whitespace` and `resourceTitle`.\n  resource: 'resource',\n  // A resource destination (`https://example.com`).\n  // Includes `resourceDestinationLiteral` or `resourceDestinationRaw`.\n  resourceDestination: 'resourceDestination',\n  // A literal resource destination (`<https://example.com>`).\n  // Includes `resourceDestinationLiteralMarker` and optionally\n  // `resourceDestinationString`.\n  resourceDestinationLiteral: 'resourceDestinationLiteral',\n  // A resource destination marker (`<` or `>`).\n  resourceDestinationLiteralMarker: 'resourceDestinationLiteralMarker',\n  // A raw resource destination (`https://example.com`).\n  // Includes `resourceDestinationString`.\n  resourceDestinationRaw: 'resourceDestinationRaw',\n  // Resource destination text (`https://example.com`).\n  // Includes string.\n  resourceDestinationString: 'resourceDestinationString',\n  // A resource marker (`(` or `)`).\n  resourceMarker: 'resourceMarker',\n  // A resource title (`\"alpha\"`, `'alpha'`, or `(alpha)`).\n  // Includes `resourceTitleMarker` and optionally `resourceTitleString`.\n  resourceTitle: 'resourceTitle',\n  // A resource title marker (`\"`, `'`, `(`, or `)`).\n  resourceTitleMarker: 'resourceTitleMarker',\n  // Resource destination title (`alpha`).\n  // Includes string.\n  resourceTitleString: 'resourceTitleString',\n  // Whole setext heading:\n  //\n  // ```markdown\n  // alpha\n  // bravo\n  // =====\n  // ```\n  //\n  // Includes `setextHeadingText`, `lineEnding`, `linePrefix`, and\n  // `setextHeadingLine`.\n  setextHeading: 'setextHeading',\n  // Content in a setext heading (`alpha\\nbravo`).\n  // Includes text.\n  setextHeadingText: 'setextHeadingText',\n  // Underline in a setext heading, including whitespace suffix (`==`).\n  // Includes `setextHeadingLineSequence`.\n  setextHeadingLine: 'setextHeadingLine',\n  // Sequence of equals or dash characters in underline in a setext heading (`-`).\n  setextHeadingLineSequence: 'setextHeadingLineSequence',\n  // Strong (`**alpha**`).\n  // Includes `strongSequence` and `strongText`.\n  strong: 'strong',\n  // Sequence of strong markers (`**` or `__`).\n  strongSequence: 'strongSequence',\n  // Strong text (`alpha`).\n  // Includes text.\n  strongText: 'strongText',\n  // Whole thematic break:\n  //\n  // ```markdown\n  // * * *\n  // ```\n  //\n  // Includes `thematicBreakSequence` and `whitespace`.\n  thematicBreak: 'thematicBreak',\n  // A sequence of one or more thematic break markers (`***`).\n  thematicBreakSequence: 'thematicBreakSequence',\n  // Whole block quote:\n  //\n  // ```markdown\n  // > a\n  // >\n  // > b\n  // ```\n  //\n  // Includes `blockQuotePrefix` and flow.\n  blockQuote: 'blockQuote',\n  // The `>` or `> ` of a block quote.\n  blockQuotePrefix: 'blockQuotePrefix',\n  // The `>` of a block quote prefix.\n  blockQuoteMarker: 'blockQuoteMarker',\n  // The optional ` ` of a block quote prefix.\n  blockQuotePrefixWhitespace: 'blockQuotePrefixWhitespace',\n  // Whole unordered list:\n  //\n  // ```markdown\n  // - a\n  //   b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listOrdered: 'listOrdered',\n  // Whole ordered list:\n  //\n  // ```markdown\n  // 1. a\n  //    b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listUnordered: 'listUnordered',\n  // The indent of further list item lines.\n  listItemIndent: 'listItemIndent',\n  // A marker, as in, `*`, `+`, `-`, `.`, or `)`.\n  listItemMarker: 'listItemMarker',\n  // The thing that starts a list item, such as `1. `.\n  // Includes `listItemValue` if ordered, `listItemMarker`, and\n  // `listItemPrefixWhitespace` (unless followed by a line ending).\n  listItemPrefix: 'listItemPrefix',\n  // The whitespace after a marker.\n  listItemPrefixWhitespace: 'listItemPrefixWhitespace',\n  // The numerical value of an ordered item.\n  listItemValue: 'listItemValue',\n  // Internal types used for subtokenizers, compiled away\n  chunkDocument: 'chunkDocument',\n  chunkContent: 'chunkContent',\n  chunkFlow: 'chunkFlow',\n  chunkText: 'chunkText',\n  chunkString: 'chunkString'\n};", "map": {"version": 3, "names": ["types", "data", "whitespace", "lineEnding", "lineEndingBlank", "linePrefix", "lineSuffix", "atxHeading", "atxHeadingSequence", "atxHeadingText", "autolink", "autolinkEmail", "autolinkMarker", "autolinkProtocol", "characterEscape", "characterEscapeValue", "characterReference", "character<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "characterReferenceMarkerNumeric", "characterReferenceMarkerHexadecimal", "characterReferenceValue", "codeFenced", "codeFencedFence", "codeFencedFenceSequence", "codeFencedFenceInfo", "codeFencedFenceMeta", "codeFlowValue", "codeIndented", "codeText", "codeTextData", "codeTextPadding", "codeTextSequence", "content", "definition", "definitionDestination", "definitionDestinationLiteral", "definitionDestinationLiteralMarker", "definitionDestinationRaw", "definitionDestinationString", "definitionLabel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "definitionLabelString", "definitionMarker", "definitionTitle", "definitionTitleMarker", "definitionTitleString", "emphasis", "emphasisSequence", "emphasisText", "<PERSON><PERSON><PERSON><PERSON>", "hardBreakEscape", "hardBreakTrailing", "htmlFlow", "htmlFlowData", "htmlText", "htmlTextData", "image", "label", "labelText", "labelLink", "labelImage", "labelMarker", "labelImageMarker", "labelEnd", "link", "paragraph", "reference", "<PERSON><PERSON><PERSON><PERSON>", "referenceString", "resource", "resourceDestination", "resourceDestinationLiteral", "resourceDestinationLiteralMarker", "resourceDestinationRaw", "resourceDestinationString", "resourceMarker", "resourceTitle", "resourceTitleMarker", "resourceTitleString", "setextHeading", "setextHeadingText", "setextHeadingLine", "setextHeadingLineSequence", "strong", "strongSequence", "strongText", "thematicBreak", "thematicBreakSequence", "blockQuote", "blockQuotePrefix", "blockQuoteMarker", "blockQuotePrefixWhitespace", "listOrdered", "listUnordered", "listItemIndent", "listItemMarker", "listItemPrefix", "listItemPrefixWhitespace", "listItemValue", "chunkDocument", "chunkContent", "chunkFlow", "chunkText", "chunkString"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/micromark-util-symbol/types.js"], "sourcesContent": ["/**\n * This module is compiled away!\n *\n * Here is the list of all types of tokens exposed by micromark, with a short\n * explanation of what they include and where they are found.\n * In picking names, generally, the rule is to be as explicit as possible\n * instead of reusing names.\n * For example, there is a `definitionDestination` and a `resourceDestination`,\n * instead of one shared name.\n */\n\n// Note: when changing the next record, you must also change `TokenTypeMap`\n// in `micromark-util-types/index.d.ts`.\nexport const types = /** @type {const} */ ({\n  // Generic type for data, such as in a title, a destination, etc.\n  data: 'data',\n\n  // Generic type for syntactic whitespace (tabs, virtual spaces, spaces).\n  // Such as, between a fenced code fence and an info string.\n  whitespace: 'whitespace',\n\n  // Generic type for line endings (line feed, carriage return, carriage return +\n  // line feed).\n  lineEnding: 'lineEnding',\n\n  // A line ending, but ending a blank line.\n  lineEndingBlank: 'lineEndingBlank',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the start of a\n  // line.\n  linePrefix: 'linePrefix',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the end of a\n  // line.\n  lineSuffix: 'lineSuffix',\n\n  // Whole ATX heading:\n  //\n  // ```markdown\n  // #\n  // ## Alpha\n  // ### Bravo ###\n  // ```\n  //\n  // Includes `atxHeadingSequence`, `whitespace`, `atxHeadingText`.\n  atxHeading: 'atxHeading',\n\n  // Sequence of number signs in an ATX heading (`###`).\n  atxHeadingSequence: 'atxHeadingSequence',\n\n  // Content in an ATX heading (`alpha`).\n  // Includes text.\n  atxHeadingText: 'atxHeadingText',\n\n  // Whole autolink (`<https://example.com>` or `<<EMAIL>>`)\n  // Includes `autolinkMarker` and `autolinkProtocol` or `autolinkEmail`.\n  autolink: 'autolink',\n\n  // Email autolink w/o markers (`<EMAIL>`)\n  autolinkEmail: 'autolinkEmail',\n\n  // Marker around an `autolinkProtocol` or `autolinkEmail` (`<` or `>`).\n  autolinkMarker: 'autolinkMarker',\n\n  // Protocol autolink w/o markers (`https://example.com`)\n  autolinkProtocol: 'autolinkProtocol',\n\n  // A whole character escape (`\\-`).\n  // Includes `escapeMarker` and `characterEscapeValue`.\n  characterEscape: 'characterEscape',\n\n  // The escaped character (`-`).\n  characterEscapeValue: 'characterEscapeValue',\n\n  // A whole character reference (`&amp;`, `&#8800;`, or `&#x1D306;`).\n  // Includes `characterReferenceMarker`, an optional\n  // `characterReferenceMarkerNumeric`, in which case an optional\n  // `characterReferenceMarkerHexadecimal`, and a `characterReferenceValue`.\n  characterReference: 'characterReference',\n\n  // The start or end marker (`&` or `;`).\n  characterReferenceMarker: 'characterReferenceMarker',\n\n  // Mark reference as numeric (`#`).\n  characterReferenceMarkerNumeric: 'characterReferenceMarkerNumeric',\n\n  // Mark reference as numeric (`x` or `X`).\n  characterReferenceMarkerHexadecimal: 'characterReferenceMarkerHexadecimal',\n\n  // Value of character reference w/o markers (`amp`, `8800`, or `1D306`).\n  characterReferenceValue: 'characterReferenceValue',\n\n  // Whole fenced code:\n  //\n  // ````markdown\n  // ```js\n  // alert(1)\n  // ```\n  // ````\n  codeFenced: 'codeFenced',\n\n  // A fenced code fence, including whitespace, sequence, info, and meta\n  // (` ```js `).\n  codeFencedFence: 'codeFencedFence',\n\n  // Sequence of grave accent or tilde characters (` ``` `) in a fence.\n  codeFencedFenceSequence: 'codeFencedFenceSequence',\n\n  // Info word (`js`) in a fence.\n  // Includes string.\n  codeFencedFenceInfo: 'codeFencedFenceInfo',\n\n  // Meta words (`highlight=\"1\"`) in a fence.\n  // Includes string.\n  codeFencedFenceMeta: 'codeFencedFenceMeta',\n\n  // A line of code.\n  codeFlowValue: 'codeFlowValue',\n\n  // Whole indented code:\n  //\n  // ```markdown\n  //     alert(1)\n  // ```\n  //\n  // Includes `lineEnding`, `linePrefix`, and `codeFlowValue`.\n  codeIndented: 'codeIndented',\n\n  // A text code (``` `alpha` ```).\n  // Includes `codeTextSequence`, `codeTextData`, `lineEnding`, and can include\n  // `codeTextPadding`.\n  codeText: 'codeText',\n\n  codeTextData: 'codeTextData',\n\n  // A space or line ending right after or before a tick.\n  codeTextPadding: 'codeTextPadding',\n\n  // A text code fence (` `` `).\n  codeTextSequence: 'codeTextSequence',\n\n  // Whole content:\n  //\n  // ```markdown\n  // [a]: b\n  // c\n  // =\n  // d\n  // ```\n  //\n  // Includes `paragraph` and `definition`.\n  content: 'content',\n  // Whole definition:\n  //\n  // ```markdown\n  // [micromark]: https://github.com/micromark/micromark\n  // ```\n  //\n  // Includes `definitionLabel`, `definitionMarker`, `whitespace`,\n  // `definitionDestination`, and optionally `lineEnding` and `definitionTitle`.\n  definition: 'definition',\n\n  // Destination of a definition (`https://github.com/micromark/micromark` or\n  // `<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteral` or `definitionDestinationRaw`.\n  definitionDestination: 'definitionDestination',\n\n  // Enclosed destination of a definition\n  // (`<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteralMarker` and optionally\n  // `definitionDestinationString`.\n  definitionDestinationLiteral: 'definitionDestinationLiteral',\n\n  // Markers of an enclosed definition destination (`<` or `>`).\n  definitionDestinationLiteralMarker: 'definitionDestinationLiteralMarker',\n\n  // Unenclosed destination of a definition\n  // (`https://github.com/micromark/micromark`).\n  // Includes `definitionDestinationString`.\n  definitionDestinationRaw: 'definitionDestinationRaw',\n\n  // Text in an destination (`https://github.com/micromark/micromark`).\n  // Includes string.\n  definitionDestinationString: 'definitionDestinationString',\n\n  // Label of a definition (`[micromark]`).\n  // Includes `definitionLabelMarker` and `definitionLabelString`.\n  definitionLabel: 'definitionLabel',\n\n  // Markers of a definition label (`[` or `]`).\n  definitionLabelMarker: 'definitionLabelMarker',\n\n  // Value of a definition label (`micromark`).\n  // Includes string.\n  definitionLabelString: 'definitionLabelString',\n\n  // Marker between a label and a destination (`:`).\n  definitionMarker: 'definitionMarker',\n\n  // Title of a definition (`\"x\"`, `'y'`, or `(z)`).\n  // Includes `definitionTitleMarker` and optionally `definitionTitleString`.\n  definitionTitle: 'definitionTitle',\n\n  // Marker around a title of a definition (`\"`, `'`, `(`, or `)`).\n  definitionTitleMarker: 'definitionTitleMarker',\n\n  // Data without markers in a title (`z`).\n  // Includes string.\n  definitionTitleString: 'definitionTitleString',\n\n  // Emphasis (`*alpha*`).\n  // Includes `emphasisSequence` and `emphasisText`.\n  emphasis: 'emphasis',\n\n  // Sequence of emphasis markers (`*` or `_`).\n  emphasisSequence: 'emphasisSequence',\n\n  // Emphasis text (`alpha`).\n  // Includes text.\n  emphasisText: 'emphasisText',\n\n  // The character escape marker (`\\`).\n  escapeMarker: 'escapeMarker',\n\n  // A hard break created with a backslash (`\\\\n`).\n  // Note: does not include the line ending.\n  hardBreakEscape: 'hardBreakEscape',\n\n  // A hard break created with trailing spaces (`  \\n`).\n  // Does not include the line ending.\n  hardBreakTrailing: 'hardBreakTrailing',\n\n  // Flow HTML:\n  //\n  // ```markdown\n  // <div\n  // ```\n  //\n  // Inlcudes `lineEnding`, `htmlFlowData`.\n  htmlFlow: 'htmlFlow',\n\n  htmlFlowData: 'htmlFlowData',\n\n  // HTML in text (the tag in `a <i> b`).\n  // Includes `lineEnding`, `htmlTextData`.\n  htmlText: 'htmlText',\n\n  htmlTextData: 'htmlTextData',\n\n  // Whole image (`![alpha](bravo)`, `![alpha][bravo]`, `![alpha][]`, or\n  // `![alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  image: 'image',\n\n  // Whole link label (`[*alpha*]`).\n  // Includes `labelLink` or `labelImage`, `labelText`, and `labelEnd`.\n  label: 'label',\n\n  // Text in an label (`*alpha*`).\n  // Includes text.\n  labelText: 'labelText',\n\n  // Start a link label (`[`).\n  // Includes a `labelMarker`.\n  labelLink: 'labelLink',\n\n  // Start an image label (`![`).\n  // Includes `labelImageMarker` and `labelMarker`.\n  labelImage: 'labelImage',\n\n  // Marker of a label (`[` or `]`).\n  labelMarker: 'labelMarker',\n\n  // Marker to start an image (`!`).\n  labelImageMarker: 'labelImageMarker',\n\n  // End a label (`]`).\n  // Includes `labelMarker`.\n  labelEnd: 'labelEnd',\n\n  // Whole link (`[alpha](bravo)`, `[alpha][bravo]`, `[alpha][]`, or `[alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  link: 'link',\n\n  // Whole paragraph:\n  //\n  // ```markdown\n  // alpha\n  // bravo.\n  // ```\n  //\n  // Includes text.\n  paragraph: 'paragraph',\n\n  // A reference (`[alpha]` or `[]`).\n  // Includes `referenceMarker` and an optional `referenceString`.\n  reference: 'reference',\n\n  // A reference marker (`[` or `]`).\n  referenceMarker: 'referenceMarker',\n\n  // Reference text (`alpha`).\n  // Includes string.\n  referenceString: 'referenceString',\n\n  // A resource (`(https://example.com \"alpha\")`).\n  // Includes `resourceMarker`, an optional `resourceDestination` with an optional\n  // `whitespace` and `resourceTitle`.\n  resource: 'resource',\n\n  // A resource destination (`https://example.com`).\n  // Includes `resourceDestinationLiteral` or `resourceDestinationRaw`.\n  resourceDestination: 'resourceDestination',\n\n  // A literal resource destination (`<https://example.com>`).\n  // Includes `resourceDestinationLiteralMarker` and optionally\n  // `resourceDestinationString`.\n  resourceDestinationLiteral: 'resourceDestinationLiteral',\n\n  // A resource destination marker (`<` or `>`).\n  resourceDestinationLiteralMarker: 'resourceDestinationLiteralMarker',\n\n  // A raw resource destination (`https://example.com`).\n  // Includes `resourceDestinationString`.\n  resourceDestinationRaw: 'resourceDestinationRaw',\n\n  // Resource destination text (`https://example.com`).\n  // Includes string.\n  resourceDestinationString: 'resourceDestinationString',\n\n  // A resource marker (`(` or `)`).\n  resourceMarker: 'resourceMarker',\n\n  // A resource title (`\"alpha\"`, `'alpha'`, or `(alpha)`).\n  // Includes `resourceTitleMarker` and optionally `resourceTitleString`.\n  resourceTitle: 'resourceTitle',\n\n  // A resource title marker (`\"`, `'`, `(`, or `)`).\n  resourceTitleMarker: 'resourceTitleMarker',\n\n  // Resource destination title (`alpha`).\n  // Includes string.\n  resourceTitleString: 'resourceTitleString',\n\n  // Whole setext heading:\n  //\n  // ```markdown\n  // alpha\n  // bravo\n  // =====\n  // ```\n  //\n  // Includes `setextHeadingText`, `lineEnding`, `linePrefix`, and\n  // `setextHeadingLine`.\n  setextHeading: 'setextHeading',\n\n  // Content in a setext heading (`alpha\\nbravo`).\n  // Includes text.\n  setextHeadingText: 'setextHeadingText',\n\n  // Underline in a setext heading, including whitespace suffix (`==`).\n  // Includes `setextHeadingLineSequence`.\n  setextHeadingLine: 'setextHeadingLine',\n\n  // Sequence of equals or dash characters in underline in a setext heading (`-`).\n  setextHeadingLineSequence: 'setextHeadingLineSequence',\n\n  // Strong (`**alpha**`).\n  // Includes `strongSequence` and `strongText`.\n  strong: 'strong',\n\n  // Sequence of strong markers (`**` or `__`).\n  strongSequence: 'strongSequence',\n\n  // Strong text (`alpha`).\n  // Includes text.\n  strongText: 'strongText',\n\n  // Whole thematic break:\n  //\n  // ```markdown\n  // * * *\n  // ```\n  //\n  // Includes `thematicBreakSequence` and `whitespace`.\n  thematicBreak: 'thematicBreak',\n\n  // A sequence of one or more thematic break markers (`***`).\n  thematicBreakSequence: 'thematicBreakSequence',\n\n  // Whole block quote:\n  //\n  // ```markdown\n  // > a\n  // >\n  // > b\n  // ```\n  //\n  // Includes `blockQuotePrefix` and flow.\n  blockQuote: 'blockQuote',\n  // The `>` or `> ` of a block quote.\n  blockQuotePrefix: 'blockQuotePrefix',\n  // The `>` of a block quote prefix.\n  blockQuoteMarker: 'blockQuoteMarker',\n  // The optional ` ` of a block quote prefix.\n  blockQuotePrefixWhitespace: 'blockQuotePrefixWhitespace',\n\n  // Whole unordered list:\n  //\n  // ```markdown\n  // - a\n  //   b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listOrdered: 'listOrdered',\n\n  // Whole ordered list:\n  //\n  // ```markdown\n  // 1. a\n  //    b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listUnordered: 'listUnordered',\n\n  // The indent of further list item lines.\n  listItemIndent: 'listItemIndent',\n\n  // A marker, as in, `*`, `+`, `-`, `.`, or `)`.\n  listItemMarker: 'listItemMarker',\n\n  // The thing that starts a list item, such as `1. `.\n  // Includes `listItemValue` if ordered, `listItemMarker`, and\n  // `listItemPrefixWhitespace` (unless followed by a line ending).\n  listItemPrefix: 'listItemPrefix',\n\n  // The whitespace after a marker.\n  listItemPrefixWhitespace: 'listItemPrefixWhitespace',\n\n  // The numerical value of an ordered item.\n  listItemValue: 'listItemValue',\n\n  // Internal types used for subtokenizers, compiled away\n  chunkDocument: 'chunkDocument',\n  chunkContent: 'chunkContent',\n  chunkFlow: 'chunkFlow',\n  chunkText: 'chunkText',\n  chunkString: 'chunkString'\n})\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,OAAO,MAAMA,KAAK,GAAG,oBAAsB;EACzC;EACAC,IAAI,EAAE,MAAM;EAEZ;EACA;EACAC,UAAU,EAAE,YAAY;EAExB;EACA;EACAC,UAAU,EAAE,YAAY;EAExB;EACAC,eAAe,EAAE,iBAAiB;EAElC;EACA;EACAC,UAAU,EAAE,YAAY;EAExB;EACA;EACAC,UAAU,EAAE,YAAY;EAExB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,UAAU,EAAE,YAAY;EAExB;EACAC,kBAAkB,EAAE,oBAAoB;EAExC;EACA;EACAC,cAAc,EAAE,gBAAgB;EAEhC;EACA;EACAC,QAAQ,EAAE,UAAU;EAEpB;EACAC,aAAa,EAAE,eAAe;EAE9B;EACAC,cAAc,EAAE,gBAAgB;EAEhC;EACAC,gBAAgB,EAAE,kBAAkB;EAEpC;EACA;EACAC,eAAe,EAAE,iBAAiB;EAElC;EACAC,oBAAoB,EAAE,sBAAsB;EAE5C;EACA;EACA;EACA;EACAC,kBAAkB,EAAE,oBAAoB;EAExC;EACAC,wBAAwB,EAAE,0BAA0B;EAEpD;EACAC,+BAA+B,EAAE,iCAAiC;EAElE;EACAC,mCAAmC,EAAE,qCAAqC;EAE1E;EACAC,uBAAuB,EAAE,yBAAyB;EAElD;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,UAAU,EAAE,YAAY;EAExB;EACA;EACAC,eAAe,EAAE,iBAAiB;EAElC;EACAC,uBAAuB,EAAE,yBAAyB;EAElD;EACA;EACAC,mBAAmB,EAAE,qBAAqB;EAE1C;EACA;EACAC,mBAAmB,EAAE,qBAAqB;EAE1C;EACAC,aAAa,EAAE,eAAe;EAE9B;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,YAAY,EAAE,cAAc;EAE5B;EACA;EACA;EACAC,QAAQ,EAAE,UAAU;EAEpBC,YAAY,EAAE,cAAc;EAE5B;EACAC,eAAe,EAAE,iBAAiB;EAElC;EACAC,gBAAgB,EAAE,kBAAkB;EAEpC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,OAAO,EAAE,SAAS;EAClB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,UAAU,EAAE,YAAY;EAExB;EACA;EACA;EACAC,qBAAqB,EAAE,uBAAuB;EAE9C;EACA;EACA;EACA;EACAC,4BAA4B,EAAE,8BAA8B;EAE5D;EACAC,kCAAkC,EAAE,oCAAoC;EAExE;EACA;EACA;EACAC,wBAAwB,EAAE,0BAA0B;EAEpD;EACA;EACAC,2BAA2B,EAAE,6BAA6B;EAE1D;EACA;EACAC,eAAe,EAAE,iBAAiB;EAElC;EACAC,qBAAqB,EAAE,uBAAuB;EAE9C;EACA;EACAC,qBAAqB,EAAE,uBAAuB;EAE9C;EACAC,gBAAgB,EAAE,kBAAkB;EAEpC;EACA;EACAC,eAAe,EAAE,iBAAiB;EAElC;EACAC,qBAAqB,EAAE,uBAAuB;EAE9C;EACA;EACAC,qBAAqB,EAAE,uBAAuB;EAE9C;EACA;EACAC,QAAQ,EAAE,UAAU;EAEpB;EACAC,gBAAgB,EAAE,kBAAkB;EAEpC;EACA;EACAC,YAAY,EAAE,cAAc;EAE5B;EACAC,YAAY,EAAE,cAAc;EAE5B;EACA;EACAC,eAAe,EAAE,iBAAiB;EAElC;EACA;EACAC,iBAAiB,EAAE,mBAAmB;EAEtC;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,QAAQ,EAAE,UAAU;EAEpBC,YAAY,EAAE,cAAc;EAE5B;EACA;EACAC,QAAQ,EAAE,UAAU;EAEpBC,YAAY,EAAE,cAAc;EAE5B;EACA;EACA;EACAC,KAAK,EAAE,OAAO;EAEd;EACA;EACAC,KAAK,EAAE,OAAO;EAEd;EACA;EACAC,SAAS,EAAE,WAAW;EAEtB;EACA;EACAC,SAAS,EAAE,WAAW;EAEtB;EACA;EACAC,UAAU,EAAE,YAAY;EAExB;EACAC,WAAW,EAAE,aAAa;EAE1B;EACAC,gBAAgB,EAAE,kBAAkB;EAEpC;EACA;EACAC,QAAQ,EAAE,UAAU;EAEpB;EACA;EACAC,IAAI,EAAE,MAAM;EAEZ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,SAAS,EAAE,WAAW;EAEtB;EACA;EACAC,SAAS,EAAE,WAAW;EAEtB;EACAC,eAAe,EAAE,iBAAiB;EAElC;EACA;EACAC,eAAe,EAAE,iBAAiB;EAElC;EACA;EACA;EACAC,QAAQ,EAAE,UAAU;EAEpB;EACA;EACAC,mBAAmB,EAAE,qBAAqB;EAE1C;EACA;EACA;EACAC,0BAA0B,EAAE,4BAA4B;EAExD;EACAC,gCAAgC,EAAE,kCAAkC;EAEpE;EACA;EACAC,sBAAsB,EAAE,wBAAwB;EAEhD;EACA;EACAC,yBAAyB,EAAE,2BAA2B;EAEtD;EACAC,cAAc,EAAE,gBAAgB;EAEhC;EACA;EACAC,aAAa,EAAE,eAAe;EAE9B;EACAC,mBAAmB,EAAE,qBAAqB;EAE1C;EACA;EACAC,mBAAmB,EAAE,qBAAqB;EAE1C;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,aAAa,EAAE,eAAe;EAE9B;EACA;EACAC,iBAAiB,EAAE,mBAAmB;EAEtC;EACA;EACAC,iBAAiB,EAAE,mBAAmB;EAEtC;EACAC,yBAAyB,EAAE,2BAA2B;EAEtD;EACA;EACAC,MAAM,EAAE,QAAQ;EAEhB;EACAC,cAAc,EAAE,gBAAgB;EAEhC;EACA;EACAC,UAAU,EAAE,YAAY;EAExB;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,aAAa,EAAE,eAAe;EAE9B;EACAC,qBAAqB,EAAE,uBAAuB;EAE9C;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,UAAU,EAAE,YAAY;EACxB;EACAC,gBAAgB,EAAE,kBAAkB;EACpC;EACAC,gBAAgB,EAAE,kBAAkB;EACpC;EACAC,0BAA0B,EAAE,4BAA4B;EAExD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,WAAW,EAAE,aAAa;EAE1B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,aAAa,EAAE,eAAe;EAE9B;EACAC,cAAc,EAAE,gBAAgB;EAEhC;EACAC,cAAc,EAAE,gBAAgB;EAEhC;EACA;EACA;EACAC,cAAc,EAAE,gBAAgB;EAEhC;EACAC,wBAAwB,EAAE,0BAA0B;EAEpD;EACAC,aAAa,EAAE,eAAe;EAE9B;EACAC,aAAa,EAAE,eAAe;EAC9BC,YAAY,EAAE,cAAc;EAC5BC,SAAS,EAAE,WAAW;EACtBC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE;AACf,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}