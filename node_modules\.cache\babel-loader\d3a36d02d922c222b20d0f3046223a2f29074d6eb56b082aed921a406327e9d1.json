{"ast": null, "code": "const tab = 9; /* `\\t` */\nconst space = 32; /* ` ` */\n\n/**\n * Remove initial and final spaces and tabs at the line breaks in `value`.\n * Does not trim initial and final spaces and tabs of the value itself.\n *\n * @param {string} value\n *   Value to trim.\n * @returns {string}\n *   Trimmed value.\n */\nexport function trimLines(value) {\n  const source = String(value);\n  const search = /\\r?\\n|\\r/g;\n  let match = search.exec(source);\n  let last = 0;\n  /** @type {Array<string>} */\n  const lines = [];\n  while (match) {\n    lines.push(trimLine(source.slice(last, match.index), last > 0, true), match[0]);\n    last = match.index + match[0].length;\n    match = search.exec(source);\n  }\n  lines.push(trimLine(source.slice(last), last > 0, false));\n  return lines.join('');\n}\n\n/**\n * @param {string} value\n *   Line to trim.\n * @param {boolean} start\n *   Whether to trim the start of the line.\n * @param {boolean} end\n *   Whether to trim the end of the line.\n * @returns {string}\n *   Trimmed line.\n */\nfunction trimLine(value, start, end) {\n  let startIndex = 0;\n  let endIndex = value.length;\n  if (start) {\n    let code = value.codePointAt(startIndex);\n    while (code === tab || code === space) {\n      startIndex++;\n      code = value.codePointAt(startIndex);\n    }\n  }\n  if (end) {\n    let code = value.codePointAt(endIndex - 1);\n    while (code === tab || code === space) {\n      endIndex--;\n      code = value.codePointAt(endIndex - 1);\n    }\n  }\n  return endIndex > startIndex ? value.slice(startIndex, endIndex) : '';\n}", "map": {"version": 3, "names": ["tab", "space", "trimLines", "value", "source", "String", "search", "match", "exec", "last", "lines", "push", "trimLine", "slice", "index", "length", "join", "start", "end", "startIndex", "endIndex", "code", "codePointAt"], "sources": ["C:/Users/<USER>/Desktop/invoice-extracting-agent/node_modules/trim-lines/index.js"], "sourcesContent": ["const tab = 9 /* `\\t` */\nconst space = 32 /* ` ` */\n\n/**\n * Remove initial and final spaces and tabs at the line breaks in `value`.\n * Does not trim initial and final spaces and tabs of the value itself.\n *\n * @param {string} value\n *   Value to trim.\n * @returns {string}\n *   Trimmed value.\n */\nexport function trimLines(value) {\n  const source = String(value)\n  const search = /\\r?\\n|\\r/g\n  let match = search.exec(source)\n  let last = 0\n  /** @type {Array<string>} */\n  const lines = []\n\n  while (match) {\n    lines.push(\n      trimLine(source.slice(last, match.index), last > 0, true),\n      match[0]\n    )\n\n    last = match.index + match[0].length\n    match = search.exec(source)\n  }\n\n  lines.push(trimLine(source.slice(last), last > 0, false))\n\n  return lines.join('')\n}\n\n/**\n * @param {string} value\n *   Line to trim.\n * @param {boolean} start\n *   Whether to trim the start of the line.\n * @param {boolean} end\n *   Whether to trim the end of the line.\n * @returns {string}\n *   Trimmed line.\n */\nfunction trimLine(value, start, end) {\n  let startIndex = 0\n  let endIndex = value.length\n\n  if (start) {\n    let code = value.codePointAt(startIndex)\n\n    while (code === tab || code === space) {\n      startIndex++\n      code = value.codePointAt(startIndex)\n    }\n  }\n\n  if (end) {\n    let code = value.codePointAt(endIndex - 1)\n\n    while (code === tab || code === space) {\n      endIndex--\n      code = value.codePointAt(endIndex - 1)\n    }\n  }\n\n  return endIndex > startIndex ? value.slice(startIndex, endIndex) : ''\n}\n"], "mappings": "AAAA,MAAMA,GAAG,GAAG,CAAC,EAAC;AACd,MAAMC,KAAK,GAAG,EAAE,EAAC;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC/B,MAAMC,MAAM,GAAGC,MAAM,CAACF,KAAK,CAAC;EAC5B,MAAMG,MAAM,GAAG,WAAW;EAC1B,IAAIC,KAAK,GAAGD,MAAM,CAACE,IAAI,CAACJ,MAAM,CAAC;EAC/B,IAAIK,IAAI,GAAG,CAAC;EACZ;EACA,MAAMC,KAAK,GAAG,EAAE;EAEhB,OAAOH,KAAK,EAAE;IACZG,KAAK,CAACC,IAAI,CACRC,QAAQ,CAACR,MAAM,CAACS,KAAK,CAACJ,IAAI,EAAEF,KAAK,CAACO,KAAK,CAAC,EAAEL,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC,EACzDF,KAAK,CAAC,CAAC,CACT,CAAC;IAEDE,IAAI,GAAGF,KAAK,CAACO,KAAK,GAAGP,KAAK,CAAC,CAAC,CAAC,CAACQ,MAAM;IACpCR,KAAK,GAAGD,MAAM,CAACE,IAAI,CAACJ,MAAM,CAAC;EAC7B;EAEAM,KAAK,CAACC,IAAI,CAACC,QAAQ,CAACR,MAAM,CAACS,KAAK,CAACJ,IAAI,CAAC,EAAEA,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;EAEzD,OAAOC,KAAK,CAACM,IAAI,CAAC,EAAE,CAAC;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASJ,QAAQA,CAACT,KAAK,EAAEc,KAAK,EAAEC,GAAG,EAAE;EACnC,IAAIC,UAAU,GAAG,CAAC;EAClB,IAAIC,QAAQ,GAAGjB,KAAK,CAACY,MAAM;EAE3B,IAAIE,KAAK,EAAE;IACT,IAAII,IAAI,GAAGlB,KAAK,CAACmB,WAAW,CAACH,UAAU,CAAC;IAExC,OAAOE,IAAI,KAAKrB,GAAG,IAAIqB,IAAI,KAAKpB,KAAK,EAAE;MACrCkB,UAAU,EAAE;MACZE,IAAI,GAAGlB,KAAK,CAACmB,WAAW,CAACH,UAAU,CAAC;IACtC;EACF;EAEA,IAAID,GAAG,EAAE;IACP,IAAIG,IAAI,GAAGlB,KAAK,CAACmB,WAAW,CAACF,QAAQ,GAAG,CAAC,CAAC;IAE1C,OAAOC,IAAI,KAAKrB,GAAG,IAAIqB,IAAI,KAAKpB,KAAK,EAAE;MACrCmB,QAAQ,EAAE;MACVC,IAAI,GAAGlB,KAAK,CAACmB,WAAW,CAACF,QAAQ,GAAG,CAAC,CAAC;IACxC;EACF;EAEA,OAAOA,QAAQ,GAAGD,UAAU,GAAGhB,KAAK,CAACU,KAAK,CAACM,UAAU,EAAEC,QAAQ,CAAC,GAAG,EAAE;AACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}